package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_CONSTRUCTION")
@Schema(description = "报表明细结构表对象")
public class ReportConstructionDO implements Serializable {



    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "报表ID")
    @TableField("REPORT_ID")
    private String reportId;

    @Schema(description = "表名")
    @TableField("TABLE_NAME")
    private String tableName;

    @Schema(description = "字段名")
    @TableField("FIELD_NAME")
    private String fieldName;

    @Schema(description = "关联字段名")
    @TableField("RELEVANCE_FIELD_NAME")
    private String relevanceFieldName;

    @Schema(description = "创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;



}
