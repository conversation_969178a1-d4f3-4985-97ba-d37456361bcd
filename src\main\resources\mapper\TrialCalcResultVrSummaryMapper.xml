<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.TrialCalcResultVrSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <id column="id" property="id" />
        <result column="historyId" property="historyId" />
        <result column="dealerCode" property="dealerCode" />
        <result column="series" property="series" />
        <result column="namt" property="namt" />
        <result column="nqty" property="nqty" />
        <result column="dim" property="dim" />
        <result column="policyId" property="policyId" />
        <result column="DTSTAMP" property="dtstamp" />
        <result column="vrqty" property="vrqty" />
        <result column="vrseries" property="vrseries" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, historyId, dealerCode, series, namt, nqty, dim, policyId, DTSTAMP, vrqty, vrseries
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.historyId,
                a.dealerCode,
                a.series,
                a.namt,
                a.nqty,
                a.dim,
                a.policyId,
                a.DTSTAMP,
                a.vrqty,
                a.vrseries,
                a.id
            from trial_calc_result_vrsummary a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" /> where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=","> #{item} </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null"> ${k} IS NULL </when>
                        <otherwise> ${k} = #{v} </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from ( <include refid="QuerySQL" />${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
</mapper>
