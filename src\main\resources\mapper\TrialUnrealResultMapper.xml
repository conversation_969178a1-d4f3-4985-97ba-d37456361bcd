<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.TrialUnrealResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <id column="id" property="id"/>
        <result column="dealerCode" property="dealerCode"/>
        <result column="series" property="series"/>
        <result column="totalAmt" property="totalAmt"/>
        <result column="totalQty" property="totalQty"/>
        <result column="dim" property="dim"/>
        <result column="DTSTAMP" property="dtstamp"/>
        <result column="aakAmt" property="aakAmt"/>
        <result column="aakQty" property="aakQty"/>
        <result column="batchId" property="batchId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , dealerCode, series, totalAmt, totalQty, dim, DTSTAMP, aakAmt, aakQty, batchId
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select *
        from (select a.dealerCode,
                     a.series,
                     a.totalAmt,
                     a.totalQty,
                     a.dim,
                     a.DTSTAMP,
                     a.aakAmt,
                     a.aakQty,
                     a.batchId,
                     a.id
              from trial_unreal_result a) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="getKanbanResult" resultType="com.qm.ep.rebatetri.domain.dto.KanbanDTO">
        select batchId, dealerCode, series, totalAmt,totalQty, ifnull((totalAmt DIV totalQty),0) as avgAmt,manual from
        trial_unreal_result
        where dealerCode=#{dealerCode} and series=#{series}
        <if test="policyId != null and policyId != ''">
            and policyId=#{policyId}
        </if>
        ORDER BY totalQty
    </select>
    <select id="listAllByCondition" resultType="com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO">
        select * from trial_unreal_result where policyId like "%,%"
        <if test="dim != null and dim != ''">
            and dim=#{dim}
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            and dealerCode=#{dealerCode}
        </if>
        <if test="series != null and series != ''">
            and series=#{series}
        </if>
        limit 1

    </select>
    <select id="countByBatchId" resultType="java.lang.Integer">
        SELECT count(*) from unreal_calc_result a INNER JOIN unreal_calc_history b ON a.historyId = b.id
        WHERE b.batchId = #{batchId} AND b.policyId = #{policyId} and (a.field3 = #{series} or a.field2 = #{series})
    </select>
    <select id="selectByDimAndPolicyIdComma" resultType="java.lang.Integer">
        SELECT count(*) from trial_unreal_result where  policyId=#{policyIdComma}  and dealerCode=#{dealerCode}
    </select>

    <select id="selectOneByBatchId" resultMap="BaseResultMap">
        SELECT * FROM trial_unreal_result WHERE batchId = #{batchId}
    </select>
</mapper>
