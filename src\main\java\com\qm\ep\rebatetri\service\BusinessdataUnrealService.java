package com.qm.ep.rebatetri.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.qm.ep.rebatetri.domain.bean.BusinessDataUnrealDO;

import java.util.List;

/**
 * <p>
 * 业务数据底表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public interface BusinessdataUnrealService extends IService<BusinessDataUnrealDO> {

    /**
     * 批量保存虚拟车
     *
     * @param businessDataUnrealDOList 业务数据不真实dolist
     */
    void saveBatchTran(List<BusinessDataUnrealDO> businessDataUnrealDOList);

    /**
     * 按照数据因子名和政策id删除虚拟车数据或发票数据
     *
     * @param tableName 表名称
     * @param policyId  策略id
     */
    void deleteByTableNameAndPolicyId(String tableName, String policyId);
}
