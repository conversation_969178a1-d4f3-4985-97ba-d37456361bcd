package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
  * 是或否枚举类
  * <AUTHOR>
  * @version 1.0
  * <p>date: 2022/12/22 16:52
  */
@Schema(description = "是或否枚举类 <p>date: 2022/12/22 16:52")
@Getter
public enum YesOrNoEnum {
    @Schema(description = "不") NO(0, "否"),
    @Schema(description = "是") YES(1, "是");

    YesOrNoEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final Integer code;
    @Schema(description = "描述")
    private final String desc;
}
