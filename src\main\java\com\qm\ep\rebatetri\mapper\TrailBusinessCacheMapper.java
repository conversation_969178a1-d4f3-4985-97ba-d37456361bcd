package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Mapper
public interface TrailBusinessCacheMapper extends BaseMapper<TrailBusinessCachePO> {

    /**
     * 缓存业务aak数据
     *
     * @param year 年
     * @return {@link List }<{@link TrailBusinessCachePO }>
     */
    List<TrailBusinessCachePO> cacheBusinessAakData(String year);

    /**
     * 缓存业务std数据
     *
     * @param year 年
     * @return {@link List }<{@link TrailBusinessCachePO }>
     */
    List<TrailBusinessCachePO> cacheBusinessStdData(String year);

    /**
     * 缓存业务发票数据
     *
     * @param year 年
     * @return {@link List }<{@link TrailBusinessCachePO }>
     */
    List<TrailBusinessCachePO> cacheBusinessInvoiceData(String year);

    /**
     * 缓存业务aak目标数据
     *
     * @param year 年
     * @return {@link List }<{@link TrailBusinessCachePO }>
     */
    List<TrailBusinessCachePO> cacheBusinessAakAimData(String year);

    /**
     * 缓存业务std目标数据
     *
     * @param year 年
     * @return {@link List }<{@link TrailBusinessCachePO }>
     */
    List<TrailBusinessCachePO> cacheBusinessStdAimData(String year);

    /**
     * 获取经销商实际aak
     *
     * @param year       年
     * @param aakMonths  aak月
     * @param dealerCode 代理商代码
     * @param series     系列
     * @param policyIds  策略ID
     * @return {@link Integer }
     */
    Integer getDealerActualAak(String year, List<String> aakMonths, String dealerCode, String series, List<String> policyIds);

    /**
     * 获取经销商实际std
     *
     * @param year       年
     * @param stdMonths  std月份
     * @param dealerCode 代理商代码
     * @param series     系列
     * @param policyIds  策略ID
     * @return {@link Integer }
     */
    Integer getDealerActualStd(String year, List<String> stdMonths, String dealerCode, String series, List<String> policyIds);

    /**
     * 获取经销商实际发票
     *
     * @param year          年
     * @param invoiceMonths 发票月份
     * @param dealerCode    代理商代码
     * @param series        系列
     * @param policyIds     策略ID
     * @return {@link Integer }
     */
    Integer getDealerActualInvoice(String year, List<String> invoiceMonths, String dealerCode, String series, List<String> policyIds);

    /**
     * 获取aak目标
     *
     * @param year       年
     * @param aakMonths  aak月
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return {@link Integer }
     */
    Integer getAakMap(String year, List<String> aakMonths, String dealerCode, String series);

    /**
     * 获取std目标
     *
     * @param year       年
     * @param stdMonths  std月份
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return {@link Integer }
     */
    Integer getStdMap(String year, List<String> stdMonths, String dealerCode, String series);
}
