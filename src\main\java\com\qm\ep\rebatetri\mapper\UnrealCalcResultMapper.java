package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.UnrealCalcResultDO;
import com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryPO;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UnrealCalcResultMapper extends QmBaseMapper<UnrealCalcResultDO> {

    /**
     * 动态sql 插入
     * @param map 参数
     * @return 返回
     */
    @Insert("${sql}")
    int insertBySql(Map<String, Object> map);

    /**
     * 动态sql 查询
     * @param map 参数
     * @return 返回
     */
    @Select("${sql}")
    List<Map> selectBySql(Map<String, Object> map);


    Map<String, Object> selectCalcResultSum(@Param("historyId") String historyId, @Param("fields") List<String> fields,
                                            @Param("dealerCodeField") String dealerCodeField, @Param("dealerCodeList") List<String> dealerCodeList);

    List<UnrealCalcResultDO> selectOneByCondition(UnrealCalcResultDO execTrialCalcResultDO);

    int deleteByHistoryIds(List<String> historyIds);

    List<KanbanPolicyTopDTO> getByBatchIdAndSeries(String batchId, String series);

    void summaryUnrealRebateAmount(String dealerField, String seriesField, String rebateField, String policyId, String historyId, int qty, String batchId);

    List<String> selectIdsByHistoryId(String historyId);

    /**
     * 根据政策id获取虚拟返利数据
     *
     * @param policyId 策略id
     * @return {@link List }<{@link UnrealCalcResultDO }>
     */
    List<UnrealCarRebateSummaryPO> selectUnrealCarRebateSummaryByPolicyId(String policyId);
}
