package com.qm.ep.rebatetri.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebatetri.domain.bean.PolicyDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatetri.domain.dto.RebateQueryListRequest;
import com.qm.ep.rebatetri.domain.dto.RebateQueryRequest;
import com.qm.ep.rebatetri.domain.dto.RebateQueryResponse;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 政策基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Component
public interface PolicyMapper extends QmBaseMapper<PolicyDO> {


    PolicyDO inquirePolicyByName(String policyName);

    PolicyDO inquirePolicyByPolicyId(String policyId);

    PolicyDO inquirePolicyByHistoryId(String historyId);


    List<ExecCalcDTO> getCalcPolicyByType(@Param("type") String type);

    List<String> getDistinctPolicyIdAscByType(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("dealerCode") String dealerCode,@Param("type") String type );

    IPage<RebateQueryResponse> selectConfigPoliciesByCondition(IPage<RebateQueryRequest> queryPage, RebateQueryRequest request);

    List<RebateQueryResponse> queryPolicyList(RebateQueryListRequest request);

    IPage<RebateQueryResponse> queryPolicyByRecordStatus(IPage<RebateQueryRequest> queryPage, RebateQueryRequest request);
}
