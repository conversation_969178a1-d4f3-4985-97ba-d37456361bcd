package com.qm.ep.rebatetri.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
* <p>
* 政策基础信息表
* </p>
* <AUTHOR>
* @since 2022-04-21
*/
@Schema(description = "数据政策基础信息表")
@Data
public class PolicyVO {

    
    

    @Schema(description = "同上")
    private String id;

    @Schema(description = "政策代码")
    private String vpolicycode;

    @Schema(description = "政策名称")
    private String vpolicyname;

    @Schema(description = "政策描述")
    private String vpolicydesc;

    @Schema(description = "政策开始时间")
    private Date dbegin;

    @Schema(description = "政策结束时间")
    private Date dend;

    @Schema(description = "关联车型")
    private String vapplyprdt;

    @Schema(description = "关联经销商")
    private String vapplyorg;

    @Schema(description = "特殊车辆设定")
    private String vtscartype;

    @Schema(description = "完成状态")
    private String vfinishstate;

    @Schema(description = "结算类型")
    private String vsmttype;

    @Schema(description = "创建者")
    private String createby;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createon;

    @Schema(description = "更新者")
    private String updateby;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateon;

    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
}