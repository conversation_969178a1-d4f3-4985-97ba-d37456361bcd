package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Schema(description = "帐户输入方法枚举")
@Getter
public enum AccountEntryMethodEnum {
    // 自动入账
    @Schema(description = "自动入账") AUTO("0", "自动入账"),
    // 审核入账
    @Schema(description = "审核入账") AUDIT("1", "审核入账");

    AccountEntryMethodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

}
