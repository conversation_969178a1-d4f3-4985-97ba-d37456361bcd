package com.qm.ep.rebatetri.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("trial_unreal_log")
@Schema(description = "数据数据TrialUnrealLogDO对象")
public class TrialUnrealLogDO implements Serializable {



    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "经销商代码")
    @TableField("dealerCode")
    private String dealerCode;

    @Schema(description = "车系")
    @TableField("series")
    private String series;

    @Schema(description = "维度")
    @TableField("dim")
    private String dim;

    @Schema(description = "左节点返利均值")
    @TableField("leftAmt")
    private Double leftAmt;

    @Schema(description = "左节点数量")
    @TableField("leftQty")
    private Integer leftQty;

    @Schema(description = "右节点返利均值")
    @TableField("rightAmt")
    private Double rightAmt;

    @Schema(description = "右节点数量")
    @TableField("rightQty")
    private Integer rightQty;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @TableField("batchId")
    private String batchId;

    @TableField("remark")
    private String remark;

    @Schema(description = "二分中间节点返利均值")
    @TableField("midAmt")
    private Double midAmt;

    @TableField("policyId")
    private String policyId;
}
