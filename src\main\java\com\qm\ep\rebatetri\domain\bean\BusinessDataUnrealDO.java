package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BUSINESSDATA_UNREAL")
@Schema(description = "BUSINESSDATA_UNREAL对象")
public class BusinessDataUnrealDO {


    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据源表名")
    @TableField("TABLENAME")
    private String tablename;

    @Schema(description = "数据表字段")
    @TableField("FIELD1")
    private String field1;

    @Schema(description = "数据表字段")
    @TableField("FIELD2")
    private String field2;

    @Schema(description = "数据表字段")
    @TableField("FIELD3")
    private String field3;

    @Schema(description = "数据表字段")
    @TableField("FIELD4")
    private String field4;

    @Schema(description = "数据表字段")
    @TableField("FIELD5")
    private String field5;

    @Schema(description = "数据表字段")
    @TableField("FIELD6")
    private String field6;

    @Schema(description = "数据表字段")
    @TableField("FIELD7")
    private String field7;

    @Schema(description = "数据表字段")
    @TableField("FIELD8")
    private String field8;

    @Schema(description = "数据表字段")
    @TableField("FIELD9")
    private String field9;

    @Schema(description = "数据表字段")
    @TableField("FIELD10")
    private String field10;

    @Schema(description = "数据表字段")
    @TableField("FIELD11")
    private String field11;

    @Schema(description = "数据表字段")
    @TableField("FIELD12")
    private String field12;

    @Schema(description = "数据表字段")
    @TableField("FIELD13")
    private String field13;

    @Schema(description = "数据表字段")
    @TableField("FIELD14")
    private String field14;

    @Schema(description = "数据表字段")
    @TableField("FIELD15")
    private String field15;

    @Schema(description = "数据表字段")
    @TableField("FIELD16")
    private String field16;

    @Schema(description = "数据表字段")
    @TableField("FIELD17")
    private String field17;

    @Schema(description = "数据表字段")
    @TableField("FIELD18")
    private String field18;

    @Schema(description = "数据表字段")
    @TableField("FIELD19")
    private String field19;

    @Schema(description = "数据表字段")
    @TableField("FIELD20")
    private String field20;

    @Schema(description = "数据表字段")
    @TableField("FIELD21")
    private String field21;

    @Schema(description = "数据表字段")
    @TableField("FIELD22")
    private String field22;

    @Schema(description = "数据表字段")
    @TableField("FIELD23")
    private String field23;

    @Schema(description = "数据表字段")
    @TableField("FIELD24")
    private String field24;

    @Schema(description = "数据表字段")
    @TableField("FIELD25")
    private String field25;

    @Schema(description = "数据表字段")
    @TableField("FIELD26")
    private String field26;

    @Schema(description = "数据表字段")
    @TableField("FIELD27")
    private String field27;

    @Schema(description = "数据表字段")
    @TableField("FIELD28")
    private String field28;

    @Schema(description = "数据表字段")
    @TableField("FIELD29")
    private String field29;

    @Schema(description = "数据表字段")
    @TableField("FIELD30")
    private String field30;

    @Schema(description = "数据表字段")
    @TableField("FIELD31")
    private String field31;

    @Schema(description = "数据表字段")
    @TableField("FIELD32")
    private String field32;

    @Schema(description = "数据表字段")
    @TableField("FIELD33")
    private String field33;

    @Schema(description = "数据表字段")
    @TableField("FIELD34")
    private String field34;

    @Schema(description = "数据表字段")
    @TableField("FIELD35")
    private String field35;

    @Schema(description = "数据表字段")
    @TableField("FIELD36")
    private String field36;

    @Schema(description = "数据表字段")
    @TableField("FIELD37")
    private String field37;

    @Schema(description = "数据表字段")
    @TableField("FIELD38")
    private String field38;

    @Schema(description = "数据表字段")
    @TableField("FIELD39")
    private String field39;

    @Schema(description = "数据表字段")
    @TableField("FIELD40")
    private String field40;

    @Schema(description = "数据表字段")
    @TableField("FIELD41")
    private String field41;

    @Schema(description = "数据表字段")
    @TableField("FIELD42")
    private String field42;

    @Schema(description = "数据表字段")
    @TableField("FIELD43")
    private String field43;

    @Schema(description = "数据表字段")
    @TableField("FIELD44")
    private String field44;

    @Schema(description = "数据表字段")
    @TableField("FIELD45")
    private String field45;

    @Schema(description = "数据表字段")
    @TableField("FIELD46")
    private String field46;

    @Schema(description = "数据表字段")
    @TableField("FIELD47")
    private String field47;

    @Schema(description = "数据表字段")
    @TableField("FIELD48")
    private String field48;

    @Schema(description = "数据表字段")
    @TableField("FIELD49")
    private String field49;

    @Schema(description = "数据表字段")
    @TableField("FIELD50")
    private String field50;

    @Schema(description = "field51")
    @TableField("field51")
    private String field51;

    @Schema(description = "field52")
    @TableField("field52")
    private String field52;

    @Schema(description = "field53")
    @TableField("field53")
    private String field53;

    @Schema(description = "field54")
    @TableField("field54")
    private String field54;

    @Schema(description = "field55")
    @TableField("field55")
    private String field55;

    @Schema(description = "field56")
    @TableField("field56")
    private String field56;

    @Schema(description = "field57")
    @TableField("field57")
    private String field57;

    @Schema(description = "field58")
    @TableField("field58")
    private String field58;

    @Schema(description = "field59")
    @TableField("field59")
    private String field59;

    @Schema(description = "field60")
    @TableField("field60")
    private String field60;

    @Schema(description = "field61")
    @TableField("field61")
    private String field61;

    @Schema(description = "field62")
    @TableField("field62")
    private String field62;

    @Schema(description = "field63")
    @TableField("field63")
    private String field63;

    @Schema(description = "field64")
    @TableField("field64")
    private String field64;

    @Schema(description = "field65")
    @TableField("field65")
    private String field65;

    @Schema(description = "field66")
    @TableField("field66")
    private String field66;

    @Schema(description = "field67")
    @TableField("field67")
    private String field67;

    @Schema(description = "field68")
    @TableField("field68")
    private String field68;

    @Schema(description = "field69")
    @TableField("field69")
    private String field69;

    @Schema(description = "field70")
    @TableField("field70")
    private String field70;

    @Schema(description = "field71")
    @TableField("field71")
    private String field71;

    @Schema(description = "field72")
    @TableField("field72")
    private String field72;

    @Schema(description = "field73")
    @TableField("field73")
    private String field73;

    @Schema(description = "field74")
    @TableField("field74")
    private String field74;

    @Schema(description = "field75")
    @TableField("field75")
    private String field75;

    @Schema(description = "field76")
    @TableField("field76")
    private String field76;

    @Schema(description = "field77")
    @TableField("field77")
    private String field77;

    @Schema(description = "field78")
    @TableField("field78")
    private String field78;

    @Schema(description = "field79")
    @TableField("field79")
    private String field79;

    @Schema(description = "field80")
    @TableField("field80")
    private String field80;

    @Schema(description = "field81")
    @TableField("field81")
    private String field81;

    @Schema(description = "fieldId")
    @TableField("fieldId")
    private String fieldId;

}
