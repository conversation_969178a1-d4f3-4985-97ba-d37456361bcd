<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.TrialCalcResultSummaryDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO">
        <id column="id" property="id" />
        <result column="dealerCode" property="dealerCode" />
        <result column="series" property="series" />
        <result column="policyId" property="policyId" />
        <result column="policyName" property="policyName" />
        <result column="vin" property="vin" />
        <result column="namt" property="namt" />
        <result column="nqty" property="nqty" />
        <result column="dim" property="dim" />
        <result column="batchId" property="batchId" />
        <result column="DTSTAMP" property="dtstamp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dealerCode, series, policyId, policyName, vin, namt, nqty, dim, batchId, DTSTAMP
    </sql>
    <select id="selectListByBatchId" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO">
        select * from trial_calc_result_summary_detail where batchId = #{batchId}
    </select>
    <select id="getKanbanPolicyTop" resultType="com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO">
        select *
        from (SELECT *
              FROM (SELECT a.policyId,
                           a.policyName                         policyName,
                           sum(IFNULL(a.namt, 0))               totalAmt,
                           count(*)                             total,
                           sum(IFNULL(a.namt, 0)) / count(*) as avgAmt,
                           ROW_NUMBER()                         OVER (PARTITION BY a.policyId ORDER BY a.policyId) AS rn
                    FROM trial_calc_result_summary_detail a
                             LEFT JOIN policy b ON a.policyId = b.Id
                    where a.batchId is NULL
                      and a.series = #{series}
                      AND dealerCode = #{dealerCode}
                    GROUP BY a.policyId) a
              WHERE a.rn = 1) t
        ORDER BY totalAmt desc limit 3
    </select>
</mapper>
