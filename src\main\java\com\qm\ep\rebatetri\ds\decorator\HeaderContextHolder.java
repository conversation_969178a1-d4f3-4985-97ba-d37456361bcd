package com.qm.ep.rebatetri.ds.decorator;

import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class HeaderContextHolder {
    private HeaderContextHolder() {
    }

    private static final ThreadLocal<Map<String, String>> CTX = new ThreadLocal<>();

    public static void setContext(Map<String, String> map) {
        CTX.set(map);
    }

    public static void removeContext() {
        CTX.remove();
    }

    public static String getHeader(String key) {
        if (isEmpty()) {
            return null;
        }
        return CTX.get().get(key);
    }

    public static boolean isEmpty() {
        return MapUtils.isEmpty(CTX.get());
    }
}
