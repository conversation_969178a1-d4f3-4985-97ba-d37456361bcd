package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Getter
@Setter
@TableName("trail_business_cache")
public class TrailBusinessCachePO implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 经销商代码
     */
    @TableField("dealerCode")
    private String dealerCode;

    /**
     * 车系
     */
    @TableField("series")
    private String series;

    /**
     * 底表名字
     */
    @TableField("tableName")
    private String tableName;
    /**
     * 年份
     */
    @TableField("year")
    private String year;
    /**
     * 月份
     */
    @TableField("month")
    private String month;

    /**
     * 数量
     */
    @TableField("nqty")
    private Integer nqty;

    /**
     * 时间戳
     */
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;

    /**
     * 试算类型
     */
    @TableField("trailType")
    private String trailType;


}
