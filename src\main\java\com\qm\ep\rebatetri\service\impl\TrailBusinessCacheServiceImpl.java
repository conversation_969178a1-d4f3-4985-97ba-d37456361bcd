package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.ep.rebatetri.constant.Constants;
import com.qm.ep.rebatetri.domain.bean.PolicyDO;
import com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.mapper.TrailBusinessCacheMapper;
import com.qm.ep.rebatetri.service.TrailBusinessCacheService;
import com.qm.tds.dynamic.constant.DataSourceType;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class TrailBusinessCacheServiceImpl extends ServiceImpl<TrailBusinessCacheMapper, TrailBusinessCachePO> implements TrailBusinessCacheService {

    public static final String ALL_SERIES = "全系";
    public static final String AAK = "aak";
    public static final String STD = "std";
    @Resource
    private TrailBusinessCacheMapper trailBusinessCacheMapper;

    @Resource
    private PolicyMapper policyMapper;

    @Override
    @Transactional(rollbackFor=Exception.class,propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void cacheBusinessAakData(String year) {
        List<TrailBusinessCachePO> aakData = trailBusinessCacheMapper.cacheBusinessAakData(year);
        saveBatch(aakData,2000);
    }

    @Override
    @Transactional(rollbackFor=Exception.class,propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void cacheBusinessStdData(String year) {
        List<TrailBusinessCachePO> stdkData = trailBusinessCacheMapper.cacheBusinessStdData(year);
        saveBatch(stdkData,2000);
    }

    @Override
    @Transactional(rollbackFor=Exception.class,propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void cacheBusinessInvoiceData(String year) {
        List<TrailBusinessCachePO> invoiceData = trailBusinessCacheMapper.cacheBusinessInvoiceData(year);
        saveBatch(invoiceData,2000);
    }

    @Override
    @Transactional(rollbackFor=Exception.class,propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void cacheBusinessAakAimData(String year) {
        List<TrailBusinessCachePO> aakAimData = trailBusinessCacheMapper.cacheBusinessAakAimData(year);
        saveBatch(aakAimData,2000);
    }

    @Override
    @Transactional(rollbackFor=Exception.class,propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void cacheBusinessStdAimData(String year) {
        List<TrailBusinessCachePO> stdAimData = trailBusinessCacheMapper.cacheBusinessStdAimData(year);
        saveBatch(stdAimData,2000);
    }

    /**
     * 获取政策维度一下的实际Aak、Std、Invoice数据。必须是相同试算类型的政策
     *
     * @param policyIds  策略ID
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return {@link Integer }
     */
    @Override
    public Integer getActualAakStdInvoiceCountByPolicyIds(List<String> policyIds, String dealerCode,  String series) {
        String trailType="";
        Integer res = 0;
        List<String> aakMonths = new ArrayList<>();
        List<String> stdMonths = new ArrayList<>();
        List<String> invoiceMonths = new ArrayList<>();
        // 获取所有月份信息
        for (String policyId : policyIds) {
            PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
            Date dbegin = policyDO.getDbegin();
            Date dend = policyDO.getDend();
            trailType= policyDO.getTrailType();
            // 获取dbegin和dend之间的月份
            if (trailType.equals(AAK)) {
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!aakMonths.contains(mm)) {
                        aakMonths.add(mm);
                    }
                }
            }
            if (trailType.equals(STD)) {
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!stdMonths.contains(mm)) {
                        stdMonths.add(mm);
                    }
                }
            }
            if (trailType.equals(Constants.INVOICE)) {
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    invoiceMonths.add(DateUtil.format(date, "MM"));
                }
            }
        }
        if (trailType.equals(AAK)) {
            res = trailBusinessCacheMapper.getDealerActualAak(DateUtil.year(new Date()) + "", aakMonths, dealerCode, series,policyIds);
        }
        if (trailType.equals(STD)) {

            res = trailBusinessCacheMapper.getDealerActualStd(DateUtil.year(new Date()) + "", stdMonths, dealerCode, series,policyIds);

        }
        if (trailType.equals(Constants.INVOICE)) {
            res = trailBusinessCacheMapper.getDealerActualInvoice(DateUtil.year(new Date()) + "", invoiceMonths, dealerCode, series,policyIds);
        }
        return res;
    }

    @Override
    public Integer getAakStdMap(List<String> policyIds, String dealerCode, String series) {
        Integer res = 0;
        String trailType = "";
        List<String> aakMonths = new ArrayList<>();
        List<String> stdMonths = new ArrayList<>();
        // 获取所有月份信息
        for (String policyId : policyIds) {
            PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
            Date dbegin = policyDO.getDbegin();
            Date dend = policyDO.getDend();
            // 获取dbegin和dend之间的月份
            trailType = policyDO.getTrailType();
            if (trailType.equals(AAK)) {
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!aakMonths.contains(mm)) {
                        aakMonths.add(mm);
                    }
                }
            }
            if (trailType.equals(STD)) {
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!stdMonths.contains(mm)) {
                        stdMonths.add(mm);
                    }
                }
            }

            if (trailType.equals(Constants.INVOICE)) {
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!aakMonths.contains(mm)) {
                        aakMonths.add(mm);
                    }
                }
            }

        }
        if (trailType.equals(AAK)) {
            res = trailBusinessCacheMapper.getAakMap(DateUtil.year(new Date()) + "", aakMonths, dealerCode, series);
        }
        if (trailType.equals(STD)) {
            res = trailBusinessCacheMapper.getStdMap(DateUtil.year(new Date()) + "", stdMonths, dealerCode, series);
        }

        if (trailType.equals(Constants.INVOICE)) {
            res = trailBusinessCacheMapper.getAakMap(DateUtil.year(new Date()) + "", aakMonths, dealerCode, series);
        }
        return res;
    }

    
    @Override
    public Integer getMaxAakStdMap(List<String> policyIds, String series) {
        if (ALL_SERIES.equals(series)){
            return getAakStdMap(policyIds, null, null);
        }
        return getAakStdMap(policyIds, null, series);
    }

}
