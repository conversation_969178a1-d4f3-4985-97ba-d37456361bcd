package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "执行类型")
public enum ExecTypeEnum {
    /**
     * 执行类型：真实返利
     */
    @Schema(description = "执行类型：人工")
    REAL_REBATE("real_rebate", "真实返利计算"),
    /**
     * 执行类型：虚拟返利
     */
    @Schema(description = "执行类型：人工")
    UNREAL_REBATE_COMBINATION("unreal_rebate_combination", "虚拟返利-合并方案计算"),
    /**
     * 执行类型：虚拟返利
     */
    @Schema(description = "执行类型：人工")
    UNREAL_REBATE("unreal_rebate", "虚拟返利-跑批计算"),

    /**
     * 执行类型：人工
     */
    @Schema(description = "执行类型：人工")
    MANUAL("manual", "人工"),

    /**
     * 执行类型：自动
     */
    @Schema(description = "执行类型：自动")
    AUTO("auto", "自动");

    ExecTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

}
