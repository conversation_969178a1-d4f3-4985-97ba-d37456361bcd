package com.qm.ep.rebatetri.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 试算结果汇总表-试算器用
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("trial_calc_result_summary")
@Schema(description = "数据试算结果汇总表-试算器用")
public class TrialCalcResultSummaryDO implements Serializable {


    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    @Schema(description = "计算历史id")
    @TableField("historyId")
    private String historyId;

    @Schema(description = "经销商代码")
    @TableField("dealerCode")
    private String dealerCode;

    @Schema(description = "车系")
    @TableField("series")
    private String series;

    @Schema(description = "总返利金额")
    @TableField("namt")
    private Double namt;

    @Schema(description = "计奖数量")
    @TableField("nqty")
    private Integer nqty;

    @Schema(description = "维度")
    @TableField("dim")
    private String dim;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "虚拟车数量")
    @TableField("vrqty")
    private Integer vrqty;

    @Schema(description = "虚拟车车系")
    @TableField("vrseries")
    private String vrseries;

    @Schema(description = "批次号")
    @TableField("batchId")
    private String  batchId;

    @Schema(description = "std数量")
    @TableField("stdQty")
    private Integer stdQty;

    @Schema(description = "实际AAK")
    @TableField("aakQty")
    private Integer aakQty;


    @Schema(description = "底盘号")
    @TableField("vin")
    private String vin;

    @Schema(description = "销售目标")
    private Integer aakMap;

    @Schema(description = "销售目标")
    private Integer stdMap;

    @Schema(description = "试算类型（aak-std-invoice）")
    @TableField(value = "trail_type")
    private String trailType;
}
