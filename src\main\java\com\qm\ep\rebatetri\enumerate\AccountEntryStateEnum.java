package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "入账状态枚举")
public enum AccountEntryStateEnum {
    /**
     * 申请入账状态：发送中
     */
    @Schema(description = "发送中")
    SENDING("01", "发送中"),
    /**
     * 申请入账状态：发送成功
     */
    @Schema(description = "发送成功")
    SUCCESS("02", "发送成功"),
    /**
     * 申请入账状态：发送失败
     */
    @Schema(description = "发送失败")
    ERROR("10", "发送失败"),
    @Schema(description = "取消申请入账中")
    /** 申请入账状态：发送中 */
     CANCEL_SENDING("11", "取消申请入账中"),
    /**
     * 申请入账状态：发送成功
     */
    @Schema(description = "取消申请入账成功")
    CANCEL_SUCCESS("12", "取消申请入账成功"),
    /**
     * 申请入账状态：发送失败
     */
    @Schema(description = "发送中")
    CANCEL_ERROR("20", "取消申请入账失败");

    AccountEntryStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

    @Schema(description = "枚举映射")
    private static final Map<String, AccountEntryStateEnum> ENUM_MAP = new HashMap<>();

    static {
        ENUM_MAP.put("01", SENDING);
        ENUM_MAP.put("02", SUCCESS);
        ENUM_MAP.put("10", ERROR);
        ENUM_MAP.put("11", CANCEL_SENDING);
        ENUM_MAP.put("12", CANCEL_SUCCESS);
        ENUM_MAP.put("20", CANCEL_ERROR);
    }

    public static AccountEntryStateEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
