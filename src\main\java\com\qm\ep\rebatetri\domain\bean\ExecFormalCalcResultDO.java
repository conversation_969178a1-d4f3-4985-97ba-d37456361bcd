package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exec_formal_calc_result")
@Schema(description = "计算结果对象")
public class ExecFormalCalcResultDO implements Serializable {



    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "计算对象计算历史表主键")
    @TableField("historyId")
    private String historyId;

    @Schema(description = "入账状态")
    @TableField(exist = false)
    private String accountEntryStatus;

    @Schema(description = "字段field1")
    @TableField("field1")
    private String field1;

    @Schema(description = "字段field2")
    @TableField("field2")
    private String field2;

    @Schema(description = "字段field3")
    @TableField("field3")
    private String field3;

    @Schema(description = "字段field4")
    @TableField("field4")
    private String field4;

    @Schema(description = "字段field5")
    @TableField("field5")
    private String field5;

    @Schema(description = "字段field6")
    @TableField("field6")
    private String field6;

    @Schema(description = "字段field7")
    @TableField("field7")
    private String field7;

    @Schema(description = "字段field8")
    @TableField("field8")
    private String field8;

    @Schema(description = "字段field9")
    @TableField("field9")
    private String field9;

    @Schema(description = "字段field10")
    @TableField("field10")
    private String field10;

    @Schema(description = "字段field11")
    @TableField("field11")
    private String field11;

    @Schema(description = "字段field12")
    @TableField("field12")
    private String field12;

    @Schema(description = "字段field13")
    @TableField("field13")
    private String field13;

    @Schema(description = "字段field14")
    @TableField("field14")
    private String field14;

    @Schema(description = "字段field15")
    @TableField("field15")
    private String field15;

    @Schema(description = "字段field16")
    @TableField("field16")
    private String field16;

    @Schema(description = "字段field17")
    @TableField("field17")
    private String field17;

    @Schema(description = "字段field18")
    @TableField("field18")
    private String field18;

    @Schema(description = "字段field19")
    @TableField("field19")
    private String field19;

    @Schema(description = "字段field20")
    @TableField("field20")
    private String field20;

    @Schema(description = "字段field21")
    @TableField("field21")
    private String field21;

    @Schema(description = "字段field22")
    @TableField("field22")
    private String field22;

    @Schema(description = "字段field23")
    @TableField("field23")
    private String field23;

    @Schema(description = "字段field24")
    @TableField("field24")
    private String field24;

    @Schema(description = "字段field25")
    @TableField("field25")
    private String field25;

    @Schema(description = "字段field26")
    @TableField("field26")
    private String field26;

    @Schema(description = "字段field27")
    @TableField("field27")
    private String field27;

    @Schema(description = "字段field28")
    @TableField("field28")
    private String field28;

    @Schema(description = "字段field29")
    @TableField("field29")
    private String field29;

    @Schema(description = "字段field30")
    @TableField("field30")
    private String field30;

    @Schema(description = "字段field31")
    @TableField("field31")
    private String field31;

    @Schema(description = "字段field32")
    @TableField("field32")
    private String field32;

    @Schema(description = "字段field33")
    @TableField("field33")
    private String field33;

    @Schema(description = "字段field34")
    @TableField("field34")
    private String field34;

    @Schema(description = "字段field35")
    @TableField("field35")
    private String field35;

    @Schema(description = "字段field36")
    @TableField("field36")
    private String field36;

    @Schema(description = "字段field37")
    @TableField("field37")
    private String field37;

    @Schema(description = "字段field38")
    @TableField("field38")
    private String field38;

    @Schema(description = "字段field39")
    @TableField("field39")
    private String field39;

    @Schema(description = "字段field40")
    @TableField("field40")
    private String field40;

    @Schema(description = "字段field41")
    @TableField("field41")
    private String field41;

    @Schema(description = "字段field42")
    @TableField("field42")
    private String field42;

    @Schema(description = "字段field43")
    @TableField("field43")
    private String field43;

    @Schema(description = "字段field44")
    @TableField("field44")
    private String field44;

    @Schema(description = "字段field45")
    @TableField("field45")
    private String field45;

    @Schema(description = "字段field46")
    @TableField("field46")
    private String field46;

    @Schema(description = "字段field47")
    @TableField("field47")
    private String field47;

    @Schema(description = "字段field48")
    @TableField("field48")
    private String field48;

    @Schema(description = "字段field49")
    @TableField("field49")
    private String field49;

    @Schema(description = "字段field50")
    @TableField("field50")
    private String field50;

}
