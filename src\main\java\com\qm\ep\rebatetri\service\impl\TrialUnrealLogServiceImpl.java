package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.domain.bean.TrialUnrealLogDO;
import com.qm.ep.rebatetri.domain.dto.HalfCalcDTO;
import com.qm.ep.rebatetri.mapper.TrialUnrealLogMapper;
import com.qm.ep.rebatetri.service.TrialUnrealLogService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class TrialUnrealLogServiceImpl extends QmBaseServiceImpl<TrialUnrealLogMapper, TrialUnrealLogDO> implements TrialUnrealLogService {




    @Override
    public void saveCommonLogNoBatchId(String dealerCode, String dim, String series, String remark, String policyId) {
        TrialUnrealLogDO trialUnrealLogDO= new TrialUnrealLogDO();
        trialUnrealLogDO.setDealerCode(dealerCode);
        trialUnrealLogDO.setDim(dim);
        trialUnrealLogDO.setSeries(series);
        trialUnrealLogDO.setRemark(remark);
        trialUnrealLogDO.setPolicyId(policyId);
        save(trialUnrealLogDO);
    }

    @Override
    public void saveCommonLog(String dealerCode, String dim, String series, String batchId, String remark, String policyId) {
        TrialUnrealLogDO trialUnrealLogDO= new TrialUnrealLogDO();
        trialUnrealLogDO.setDealerCode(dealerCode);
        trialUnrealLogDO.setDim(dim);
        trialUnrealLogDO.setSeries(series);
        trialUnrealLogDO.setRemark(remark);
        trialUnrealLogDO.setPolicyId(policyId);
        trialUnrealLogDO.setBatchId(batchId);
        save(trialUnrealLogDO);
    }

    @Override
    public void saveHalfLog(String policyId, String dealerCode, String dim, String series, String batchId, HalfCalcDTO left, HalfCalcDTO right, Double midAmt, String remark) {
        TrialUnrealLogDO trialUnrealLogDO= new TrialUnrealLogDO();
        trialUnrealLogDO.setPolicyId(policyId);
        trialUnrealLogDO.setDealerCode(dealerCode);
        trialUnrealLogDO.setDim(dim);
        trialUnrealLogDO.setSeries(series);
        trialUnrealLogDO.setBatchId(batchId);
        trialUnrealLogDO.setLeftAmt(left.getAmt());
        trialUnrealLogDO.setLeftQty(left.getQty());
        trialUnrealLogDO.setRightAmt(right.getAmt());
        trialUnrealLogDO.setRightQty(right.getQty());
        trialUnrealLogDO.setMidAmt(midAmt);
        trialUnrealLogDO.setRemark(remark);
        save(trialUnrealLogDO);
    }
}
