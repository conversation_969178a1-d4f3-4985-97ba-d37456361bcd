package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "时间维度枚举")
public enum TimeDimensionEnum {

    @Schema(description = "月") MONTH("M", "月度"),

    @Schema(description = "季度") QUARTER("Q", "季度"),

    @Schema(description = "年") YEAR("Y", "年度");

    TimeDimensionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

}
