package com.qm.ep.rebatetri.domain.bean;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 试算结果汇总表-试算器用
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "数据试算结果汇总表-试算器用")
public class TrialCalcResultSummaryDetailDO implements Serializable {


    private String id;


    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "车系")
    private String series;

    @Schema(description = "返利金额")
    private Double namt;

    @Schema(description = "数据aak数量")
    private Integer nqty;

    @Schema(description = "维度")
    private String dim;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "政策ID")
    private String policyId;
    @Schema(description = "政策ID")
    private String policyName;

    private String vin;


}
