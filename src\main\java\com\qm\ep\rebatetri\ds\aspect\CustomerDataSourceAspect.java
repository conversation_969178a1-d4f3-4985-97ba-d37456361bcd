package com.qm.ep.rebatetri.ds.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.qm.ep.rebatetri.ds.constant.Constants;
import com.qm.ep.rebatetri.ds.decorator.HeaderContextHolder;
import com.qm.tds.dynamic.aspect.DataSourceSupporter;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.mq.builder.MessageStruct;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Order(1)
@Component
@Aspect
@ConditionalOnProperty(
        prefix = "spring.datasource.dynamic",
        name = {"primary"},
        havingValue = "tenant"
)
@Slf4j
public class CustomerDataSourceAspect {
    @Value("${spring.datasource.dynamic.primary}")
    private String primary;
    @Autowired
    private DataSourceSupporter dataSourceSupporter;

    @Around("execution(* com.qm.ep.*.listener..*(..))||execution(* com.qm.tds.*.listener..*(..))")
    public Object doListenerAround(ProceedingJoinPoint jp) throws Throwable {
        String tenantId = this.getTenantIdFromRequest();
        if (StringUtils.isBlank(tenantId)) {
            tenantId = this.getTenantIdFromMQ(jp);
        }

        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put(Constants.TENANT_ID, tenantId);
            HeaderContextHolder.setContext(headerMap);
            return this.doAround(jp, tenantId);
        } finally {
            HeaderContextHolder.removeContext();
        }
    }

    private Object doAround(ProceedingJoinPoint jp, String tenantId) throws Throwable {
        if (DataSourceType.TENANT.equals(this.primary) && StringUtils.isNotBlank(tenantId)) {
            Object var6;
            try {
                this.dataSourceSupporter.dataSourceHandle(tenantId);
                MethodSignature signature = (MethodSignature)jp.getSignature();
                Method method = signature.getMethod();
                DS dsSource = method.getAnnotation(DS.class);
                if (dsSource != null) {
                    DynamicDataSourceContextHolder.push(tenantId + dsSource.value());
                } else {
                    DynamicDataSourceContextHolder.push(tenantId + "w");
                }

                var6 = jp.proceed();
            } finally {
                DynamicDataSourceContextHolder.poll();
            }

            return var6;
        } else {
            return jp.proceed();
        }
    }

    private String getTenantIdFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String tenantId = null;
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            tenantId = request.getParameter(Constants.TENANT_ID);
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader(Constants.TENANT_ID);
            }
        }

        return tenantId;
    }

    private String getTenantIdFromMQ(ProceedingJoinPoint jp) throws NoSuchFieldException {
        String tenantId = null;
        Object[] args = jp.getArgs();
        Object[] var4 = args;
        int var5 = args.length;

        for(int var6 = 0; var6 < var5; ++var6) {
            Object object = var4[var6];
            if (object instanceof MessageStruct) {
                log.debug("MQ接收数据：" + object);
                Class<?> meassageStruct = object.getClass();
                Field field = meassageStruct.getDeclaredField("requestInfo");
                field.setAccessible(true);

                try {
                    Object requestInfo = field.get(object);
                    JSONObject json = (JSONObject) JSON.toJSON(requestInfo);
                    tenantId = json.containsKey(Constants.TENANT_ID) ? json.getString(Constants.TENANT_ID) : "";
                } catch (Exception var12) {
                    log.error(var12.getMessage(), var12);
                }
            }
        }

        return tenantId;
    }
}

