package com.qm.ep.rebatetri.enumerate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返利类型枚举
 *
 * <AUTHOR>
 * @date 2024/12/12
 */

@Schema(description = "返利类型枚举")
@Getter
@AllArgsConstructor
public enum RebateTypeEnum {

    /**
     * 00-销售返利
     */
    @Schema(description = "00-销售返利")
    SALE_REBATE("00", "销售返利"),
    /**
     * 01-备件返利
     */
    @Schema(description = "01-备件返利")
    SPT_REBATE("01", "备件返利"),
    /**
     * 02-其他返利
     */
    @Schema(description = "02-其他返利")
    OTHER_REBATE("02", "其他返利"),
    /**
     * 03-代理制保证金折让
     */

    @Schema(description = "03-代理制保证金折让")
    AGENCY_MARGIN("03", "代理制保证金折让"),
    /**
     * 04-代理制佣金
     */
    @Schema(description = "04-代理制佣金")
    AGENCY_COMMISSION("04", "代理制佣金");

    @Getter
    @Schema(description = "业务类型")
    private final String code;

    @Schema(description = "描述")
    private final String desc;


}