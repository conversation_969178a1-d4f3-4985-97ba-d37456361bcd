package com.qm.ep.rebatetri.monitor.threadpool.job;

import com.qm.ep.rebatetri.monitor.threadpool.config.MonitorCache;
import com.qm.ep.rebatetri.monitor.threadpool.executor.MonitoredThreadPoolTaskExecutor;
import com.qm.ep.rebatetri.monitor.threadpool.report.Reporter;
import com.qm.ep.rebatetri.monitor.threadpool.report.ThreadPoolRealTimeInfo;
import com.qm.ep.rebatetri.monitor.threadpool.report.TotalDataInfo;
import com.qm.ep.rebatetri.monitor.threadpool.report.redis.RedisService;
import com.qm.ep.rebatetri.monitor.threadpool.report.redis.Utils;
import com.qm.ep.rebatetri.monitor.threadpool.utils.DateUtils;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class RealTimeInfoWatcher implements Runnable {

    private final Reporter reporter;
    private final RedisService redisService;
    private static long lastSendTimeMills = -1;

    public RealTimeInfoWatcher() {
        this.reporter = SpringContextHolder.getBean(Reporter.class);
        this.redisService = SpringContextHolder.getBean(RedisService.class);
    }

    @Override
    public void run() {
        while (true) {
            try {
                for (String poolName : MonitorCache.executorMap.keySet()) {
                    Executor executor = MonitorCache.executorMap.get(poolName);
                    recordRealTimeInfo(poolName, executor);
                    realTimeAlarmInfo(poolName, executor);
                    refreshThreadPool();
                    cleanJobInfoRecord(poolName);
                    reportIpInfo();
                }
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[RealTimeInfoWatcher] error is ", e);
                break;
            }
        }
    }

    public void reportIpInfo() {
        for (Map.Entry<String, Executor> item : MonitorCache.executorMap.entrySet()) {
            TotalDataInfo totalDataInfo = new TotalDataInfo();
            totalDataInfo.setApplicationName(MonitorCache.applicationName);
            totalDataInfo.setIp(MonitorCache.ip);
            totalDataInfo.setPoolName(item.getKey());
            totalDataInfo.setPort(MonitorCache.port);
            reporter.doReportTotalData(totalDataInfo);
        }
    }


    //清理记录标签任务的记录
    private void cleanJobInfoRecord(String poolName) {
        String tagMapKey = Utils.buildTagKey(MonitorCache.applicationName, poolName);
        Map<Object, Object> tagIdMap = redisService.hmget(tagMapKey);
        for (Object tagName : tagIdMap.keySet()) {
            String tagUUid = (String) tagIdMap.get(tagName);
            String jobInfoKey = Utils.buildJobInfoListKey(MonitorCache.ip, MonitorCache.port, MonitorCache.applicationName,
                    DateUtils.getTodayStr(), poolName, tagUUid);
            long size = redisService.zZCard(jobInfoKey);

            //如果体积过大，则需要删除部分耗时最低的数据
//            if (size > Double.valueOf(executorProperties.getMaxTagRecordSize()) * 0.75) {
            if (size > 1000 * 0.75) {
                int begin = (int) (size * 0.75);
                //删除耗时最低的25%的数据
                redisService.zRemoveRange(jobInfoKey, begin, -1);
                log.info("删除TAG任务数据！");
            }
        }
    }

    public void refreshThreadPool() {
        /*if (isThreadPoolPropertiesIllegal()) {
            log.error("[RealTimeInfoWatcher] dynamic threadPool's properties is illegal!");
            return;
        }
        Map<String, Executor> runningExecutorMap = applicationContext.getBeansOfType(Executor.class);
        DynamicThreadPoolProperties dynamicThreadPoolProperties = applicationContext.getBean(DynamicThreadPoolProperties.class);
        Map<String, ExecutorProperties> iExecutorsMap = dynamicThreadPoolProperties.getExecutors();
        for (String executorName : iExecutorsMap.keySet()) {
            ExecutorProperties executorProperties = iExecutorsMap.get(executorName);
            Executor executor = runningExecutorMap.get(executorName);
            boolean hasChange = false;
            if (executor != null) {
                if (executor.getCorePoolSize() != Integer.valueOf(executorProperties.getCorePoolSize())) {
                    hasChange = true;
                }
                if (executor.getKeepAliveTime(TimeUnit.MILLISECONDS) != Integer.valueOf(executorProperties.getKeepAliveTime())) {
                    hasChange = true;
                }
                if (executor.getQueueCapacity() != Integer.valueOf(executorProperties.getQueueCapacity())) {
                    hasChange = true;
                }
                if (executor.getMaximumPoolSize() != Integer.valueOf(executorProperties.getMaximumPoolSize())) {
                    hasChange = true;
                }
                if (!hasChange) {
                    continue;
                }
                executor.setCorePoolSize(Integer.parseInt(executorProperties.getCorePoolSize()));
                executor.setKeepAliveTime(Long.parseLong(executorProperties.getKeepAliveTime()), TimeUnit.MILLISECONDS);
                executor.setMaximumPoolSize(Integer.parseInt(executorProperties.getMaximumPoolSize()));
                //底层默认统一使用动态扩容队列
                ResizableCapacityLinkedBlockingQueue workQueue = (ResizableCapacityLinkedBlockingQueue) executor.getQueue();
                workQueue.setCapacity(Integer.parseInt(executorProperties.getQueueCapacity()));
                log.info("[RealTimeInfoWatcher] refresh success! executorProperties is {}", executorProperties);
            }
        }*/
    }

    /**
     * 判断线程池配置是否合法
     *
     * @return
     */
    private boolean isThreadPoolPropertiesIllegal() {
        /*boolean isThreadPoolPropertiesIllegal = false;
        StringBuffer errorAlarmMsg = new StringBuffer();
        DynamicThreadPoolProperties dynamicThreadPoolProperties = applicationContext.getBean(DynamicThreadPoolProperties.class);
        String alarmEmails = dynamicThreadPoolProperties.getAlarmEmails();
        if (StringUtils.isEmpty(alarmEmails)) {
            log.error("[preCheckThreadPoolParamVerify] alarmWorkerIds could not be null!");
            return true;
        }
        Map<String, ExecutorProperties> executorsMap = dynamicThreadPoolProperties.getExecutors();
        for (String executorName : executorsMap.keySet()) {
            ExecutorProperties iExecutors = executorsMap.get(executorName);
            if (Integer.valueOf(iExecutors.getCorePoolSize()) > Integer.valueOf(iExecutors.getMaximumPoolSize())) {
                isThreadPoolPropertiesIllegal = true;
                errorAlarmMsg.append("核心线程数不得大于最大线程数! 当前corePoolSize:" + iExecutors.getCorePoolSize() + ",maximumPoolSize:" + iExecutors.getMaximumPoolSize() + "\n");
            }
            if (Integer.valueOf(iExecutors.getQueueCapacity()) > 10000) {
                isThreadPoolPropertiesIllegal = true;
                errorAlarmMsg.append("线程池队列长度范围 0～10000! 当前queueCapacity:" + iExecutors.getQueueCapacity() + "\n");
            }
            if (Double.valueOf(iExecutors.getTaskCountScoreThreshold()) > 1) {
                isThreadPoolPropertiesIllegal = true;
                errorAlarmMsg.append("线程池队列任务上限阈值（queueSize/queueCapacity）不得大于1! 当前taskCountScoreThreshold:" + iExecutors.getTaskCountScoreThreshold() + "\n");
            }
            if (Integer.valueOf(iExecutors.getMaxTagRecordSize()) > 10000) {
                isThreadPoolPropertiesIllegal = true;
                errorAlarmMsg.append("标签队列体积上限不得大于10000! 当前maxTagRecordSize:" + iExecutors.getMaxTagRecordSize() + "\n");
            }
            if (isThreadPoolPropertiesIllegal) {
                return true;
            }
        }*/
        return false;
    }

    /**
     * 实时记录当前服务的线程池信息
     *
     */
    public void recordRealTimeInfo(String poolName, Executor executor) {
        ThreadPoolExecutor poolExecutor = null;
        if(executor instanceof ThreadPoolExecutor) {
            poolExecutor = (ThreadPoolExecutor) executor;
        } else if(executor instanceof ThreadPoolTaskExecutor) {
            poolExecutor = ((ThreadPoolTaskExecutor) executor).getThreadPoolExecutor();
        } else if(executor instanceof MonitoredThreadPoolTaskExecutor) {
            poolExecutor = ((MonitoredThreadPoolTaskExecutor) executor).getThreadPoolExecutor();
        }
        if(poolExecutor==null) {
            return;
        }
        ThreadPoolRealTimeInfo realTimeInfo = new ThreadPoolRealTimeInfo();
        realTimeInfo.setPoolName(poolName);
        realTimeInfo.setActivePoolSize(poolExecutor.getActiveCount());
        realTimeInfo.setMaximumPoolSize(poolExecutor.getMaximumPoolSize());
        realTimeInfo.setQueueCapacity(poolExecutor.getQueue().size());
        realTimeInfo.setQueueSize(poolExecutor.getQueue().size());
        realTimeInfo.setCompletedTaskCount(poolExecutor.getCompletedTaskCount());
        if(executor instanceof MonitoredThreadPoolTaskExecutor) {
            realTimeInfo.setErrorTaskCount(((MonitoredThreadPoolTaskExecutor) executor).getErrorNum());
            realTimeInfo.setQueueCapacity(((MonitoredThreadPoolTaskExecutor) executor).getQueueCapacity());
            realTimeInfo.setTaskCountScoreThreshold(String.valueOf(((MonitoredThreadPoolTaskExecutor) executor).getTaskCountScoreThreshold()));
            realTimeInfo.setPreStartAllCoreThreads(((MonitoredThreadPoolTaskExecutor) executor).getPreStartAllCoreThreads());
            realTimeInfo.setPreStartCoreThread(((MonitoredThreadPoolTaskExecutor) executor).getPreStartCoreThread());
        } else {
            realTimeInfo.setErrorTaskCount(0);
        }
        realTimeInfo.setKeepAliveTime(poolExecutor.getKeepAliveTime(TimeUnit.MILLISECONDS));
        realTimeInfo.setCorePoolSize(poolExecutor.getCorePoolSize());

        realTimeInfo.setRejectedExecutionType(poolExecutor.getRejectedExecutionHandler().getClass().getSimpleName());
        reporter.doReportRealTime(MonitorCache.ip, MonitorCache.port, MonitorCache.applicationName, realTimeInfo);
//        reporter.doReportAlarmInfo(MonitorCache.applicationName, dynamicThreadPoolProperties.getAlarmEmails());
    }

    /**
     * 实时计算线程池负载情况
     *
     * @param executor
     */
    public void realTimeAlarmInfo(String poolName, Executor executor) {
//        double queueLoad = (double) executor.getQueue().size() / (double) executor.getQueueCapacity();
//        if (executor.getTaskCountScoreThreshold() <= queueLoad) {
//            String alarmEmails = dynamicThreadPoolProperties.getAlarmEmails();
//            if (StringUtils.isEmpty(alarmEmails)) {
//                return;
//            }
//            boolean canSend = false;
//            //初始值为-1
//            if (lastSendTimeMills < 0) {
//                canSend = true;
//            } else if (System.currentTimeMillis() - lastSendTimeMills > 5 * 60 * 1000) {
//                canSend = true;
//            }
//            if (canSend) {
//                String[] emailArr = alarmEmails.split(",");
//                String title = Utils.buildErrorAlarmTitle("test");
//                String content = Utils.buildErrorAlarmContent(MonitorCache.applicationName, MonitorCache.ip + ":" + MonitorCache.port, poolName);
//                try {
//                    log.info("【realTimeAlarmInfo】发送实时告警通知");
//                    EmailUtil.send(title, content, emailArr);
//                } catch (Exception e) {
//                    log.error("error is ", e);
//                }
//                lastSendTimeMills = System.currentTimeMillis();
//            }
//        }
    }

}
