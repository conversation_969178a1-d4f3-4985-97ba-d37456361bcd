package com.qm.ep.rebatetri.remote;

import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(contextId= "tds-service-rebate-calc",name = "tds-service-rebate-calc")
public interface RebateCalcRemote {

    /**
     * 由定时任务调用 执行计算
     * @param execCalcDTO 计算对象执行计算
     * @return 返回
     */
    @PostMapping(value = "/execCalc/start", produces = "application/json",headers = {"tenantId=15"})
    JsonResultVo<Object> start(@RequestBody ExecCalcDTO execCalcDTO);


}
