package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import com.qm.tds.api.service.IQmBaseService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试算结果汇总表-试算器用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
public interface TrialCalcResultSummaryService extends IQmBaseService<TrialCalcResultSummaryDO> {

    List<TrialCalcResultSummaryDO> selectAllByDim(String dim);

    /**
     * 获取对应数量的平均返利，如果初始化返利不存在就返回已存在的最大数量点对应的返利
     *
     * @param qty        数量
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @param series     系列
     * @return {@link Double }
     */
    Double getUnrealRebateAmtSumByQty(int qty, String dealerCode, List<String> policyIds, String series);

    /**
     * 通过政策id获取对应数量的虚拟平均返利
     *
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @return {@link Map }<{@link String }, {@link Double }>
     */
    Map<String, Double> getAllUnrealRebateAmtSumByPolicyIds(String dealerCode, List<String> policyIds);

    /**
     * 获得返利政策top3
     *
     * @param series     系列
     * @param dealerCode 代理商代码
     * @param batchId    批次id
     * @return {@link List }<{@link KanbanPolicyTopDTO }>
     */
    List<KanbanPolicyTopDTO> getRebatePolicyTop3(String series, String dealerCode, String batchId);

    /**
     * 根据政策id获取真实返利数据
     *
     * @param policyId 策略id
     * @return {@link List }<{@link TrialCalcResultSummaryDO }>
     */
    List<TrialCalcResultSummaryDO> getRealRebateByPolicyId(String policyId);
}
