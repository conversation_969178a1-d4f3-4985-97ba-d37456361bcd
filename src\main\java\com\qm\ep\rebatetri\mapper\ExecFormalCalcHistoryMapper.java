package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExecFormalCalcHistoryMapper extends QmBaseMapper<ExecFormalCalcHistoryDO> {

    /**
     * 获取计算对象的计算历史
     * @param calcObjectId 计算对象ID
     * @return 返回
     */
    List<ExecFormalCalcHistoryDO> getExecCalcHistory(String calcObjectId);

}
