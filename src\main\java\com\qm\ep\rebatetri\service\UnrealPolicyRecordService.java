package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.UnrealPolicyRecordPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

/**
 * <p>
 * 政策试算数据待初始化表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface UnrealPolicyRecordService extends IService<UnrealPolicyRecordPO> {

    void updateUnrealPolicyStatusById(Integer id, String status);

    UnrealPolicyRecordPO selectTheEarliestPolicy();

    Set<String> selectCalMainTableName(String policyId);

}
