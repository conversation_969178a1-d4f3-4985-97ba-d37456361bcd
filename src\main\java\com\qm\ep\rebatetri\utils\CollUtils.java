package com.qm.ep.rebatetri.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 实用衣领
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class CollUtils {
    /**
     * 将给定的列表按照指定的批次大小进行分片。
     *
     * @param list 要分片的列表
     * @param batchSize 每个分片的大小
     * @param <T> 列表中元素的类型
     * @return 包含所有分片的列表
     */
    public static <T> List<List<T>> partition(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        int size = list.size();

        for (int i = 0; i < size; i += batchSize) {
            // 计算结束索引，确保不超过列表长度
            int end = Math.min(i + batchSize, size);

            // 添加分片到结果列表
            partitions.add(list.subList(i, end));
        }

        return partitions;
    }
}
