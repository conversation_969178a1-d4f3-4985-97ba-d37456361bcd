package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.dto.DealerAAKDTO;
import com.qm.ep.rebatetri.domain.dto.DealerSTDDTO;
import com.qm.ep.rebatetri.domain.dto.DealerTaskDTO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;

import java.util.List;
import java.util.Map;

/**
 * 试算数据服务接口
 * <AUTHOR>
 */
public interface TrialCalcDataService {

    /**
     * 查询经销商MAP目标
     * @param dealerCode 经销商代码
     * @param dim 维护 Y年度 Q季度 M月度
     * @return
     */
    List<DealerTaskDTO> getDealerTask(String dealerCode, String dim);

    /**
     * 获取全部可计算的商务政策信息，作为正式计算的入参
     *
     * @param dim 维护 Y年度 Q季度 M月度
     * @return
     */
    List<ExecCalcDTO> getCalcPolicy(String dim);

    /**
     * 获取经销商AAK
     * @return
     */
    List<DealerAAKDTO> getDealerAAK(String dim, List<String> dealerCodes);

    List<DealerTaskDTO> getDealerSTDTask(String dealerCode, String dim);

    List<DealerSTDDTO> getDealerSTD(String dim, List<String> dealerCodes);

    List<String> isVisible(List<String> policyIds);

    /**
     * 快速清空试算相关表数据
     */
    void truncateData();


    /**
     * 获取复合试算条件的试算对象
     *
     * @param type 类型
     * @return {@link List }<{@link ExecCalcDTO }>
     */
    List<ExecCalcDTO> getCalcPolicyByType(String type);


    List<String> getDistinctPolicyIdAscByType(String dim, String dealerCode, String type);

    /**
     * 按照政策执行周期实时从数据库获取实时aak，STD和发票数量
     *
     * @param policyIds  策略ID
     * @param dealerCode 代理商代码
     * @param trailType  轨迹类型
     * @return int
     */
    Map<String, Integer> getRealAakStdInvoiceCountByMonths(List<String> policyIds, String dealerCode, String trailType);
}
