package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "计算方案-计算结果转业务底表")
@Data
public class TurnBusinessBottomTableDTO extends JsonParamDto {
    
    

    @Schema(description = "主键")
    private String id;

    @Schema(description = "计算对象ID")
    private String objectId;

    @Schema(description = "政策ID")
    private String policyId;

    @Schema(description = "语句sql")
    private String structure;

    @Schema(description = "计算类型")
    private String calculationType;

    @Schema(description = "对象类型")
    private String objectType;
}
