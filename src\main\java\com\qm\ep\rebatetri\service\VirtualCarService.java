package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.BusinessDataDO;
import com.qm.ep.rebatetri.domain.bean.UnrealPolicyRecordPO;
import com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface VirtualCarService {
    void deleteUnrealData();

    void createData(String policyId);

    void completeProcess();


    /**
     * 模拟合并方案的计算（配置阶段）
     *
     * @param policyId 策略id
     * @return {@link List }<{@link BusinessDataDO }>
     */
    List<BusinessDataDO> reCalCombination(String policyId);

    public List<DealerSeriesVinDTO> getRebateSeriesVin(String historyId, String type);

    void initRebateData(String policyId, String historyId);


    void doCopyVin(String policyId, List<BusinessDataDO> datas,String trailType);

    /**
     * 复制虚拟车vin并初始化返款数据
     *
     * @param rebateSeriesVin 返款系列vin
     * @param recordPO        记录po
     * @param trailType       轨迹类型
     */
    @Async("unrealRebateCalcAsync")
    void copyVinAndInitRebateDataAsync(List<BusinessDataDO> rebateSeriesVin, UnrealPolicyRecordPO recordPO, String trailType);
}
