<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qm.tds</groupId>
        <artifactId>tds-base-service-parent</artifactId>
        <version>7.0.0-rebate-SNAPSHOT</version>
    </parent>
    <artifactId>tds-service-rebate-tri</artifactId>
    <version>0.0.4-SNAPSHOT</version>
    <name>tds-service-rebate-tri</name>
    <description>返利试算服务</description>

    <properties>
        <flowin>com.qm.tds.base.controller</flowin>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-dynamic-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-lock</artifactId>
        </dependency>

        <!--  ep junit test case -->
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>ep-test-base-common</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>10.1.34</version>
        </dependency>


        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.3</version>
        </dependency>
    </dependencies>

    <build>
        <!-- 坐标+版本号 -->
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- Maven仓库地址 -->
    <repositories>
        <repository>
            <id>qm_maven_center</id>
            <name>qm dms maven center</name>
            <url>https://jfapp.qm.cn:8003/repository/qm-dms-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <!-- 插件Maven仓库地址 -->
    <pluginRepositories>
        <pluginRepository>
            <id>qm_maven_center</id>
            <name>qm dms maven center</name>
            <url>https://jfapp.qm.cn:8003/repository/qm-dms-public/</url>
        </pluginRepository>
    </pluginRepositories>

</project>
