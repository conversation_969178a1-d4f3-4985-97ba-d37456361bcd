package com.qm.ep.rebatetri.domain.dto;

import com.qm.ep.rebatetri.enumerate.CalcObjectTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "CalcObjectDTO对象")
@Data
@ToString(callSuper = true)
public class CalcObjectDTO extends JsonParamDto {

    
    

    @Schema(description = "政策主键")
    private String policyId;

    @Schema(description = "所属公司")
    private String companyId;

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "计算对象ID")
    private String objectId;

    @Schema(description = "计算对象类型")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "是否使用缩略别名")
    private Boolean abbreviated;

    @Schema(description = "车系对应的虚拟车数量")
    private Map<String, Integer> seriesQty;

    @Schema(description = "计算的是实际返利还是虚拟返利 0实际 1虚拟")
    private int vr;

    @Schema(description = "经销商下车系对应的虚拟车数量")
    private Map<String,Map<String, Integer>> dealerSeriesQty;

    /**
     * 1 - 试算 ; 其他 - 虚拟车试算
     */
    @Schema(description = "1 - 试算 ; 其他 - 虚拟车试算")
    private int vflag;
    @Schema(description = "限制")
    private int limit;
    @Schema(description = "试算类型")
    private String trailType;
}
