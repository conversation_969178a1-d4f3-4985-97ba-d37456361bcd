package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface ExecFormalCalcHistoryService extends IQmBaseService<ExecFormalCalcHistoryDO> {

    @Async("autoEnterAccountAsync")
    void autoApplyEntryAccount(ExecFormalCalcHistoryDO execCalcHistoryDO);

}
