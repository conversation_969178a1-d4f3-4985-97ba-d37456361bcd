package com.qm.ep.rebatetri.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.SystemConfigDO;
import com.qm.ep.rebatetri.mapper.SystemConfigMapper;
import com.qm.ep.rebatetri.service.SystemConfigService;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SystemConfigServiceImpl extends QmBaseServiceImpl<SystemConfigMapper, SystemConfigDO> implements SystemConfigService {

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    /**
     * 通过参数代码获取参数值
     *
     * @param code 参数
     * @return 返回
     */
    @Override
    public String getValueByCode(String code) {
        String ret = "";
        QmQueryWrapper<SystemConfigDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<SystemConfigDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(SystemConfigDO::getCode, code);
        SystemConfigDO one = getOne(lambdaWrapper);
        if(one!=null) {
            ret = one.getValue();
        }
        return ret;
    }

}
