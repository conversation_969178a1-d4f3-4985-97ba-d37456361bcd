package com.qm.ep.rebatetri.config;

import com.qm.ep.rebatetri.ds.decorator.ContextDecorator;
import com.qm.ep.rebatetri.monitor.annotation.Monitor;
import com.qm.ep.rebatetri.monitor.threadpool.annotation.MonitorThreadPool;
import com.qm.ep.rebatetri.monitor.threadpool.executor.MonitoredThreadPoolTaskExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@Monitor
public class AsyncScheduledTaskConfigV2 {

    @Value("${best.task.execution.pool.core-size:10}")
    private int corePoolSize;
    @Value("${best.task.execution.pool.max-size:100}")
    private int maxPoolSize;
    @Value("${best.task.execution.pool.queue-capacity:1000}")
    private int queueCapacity;
    @Value("${best.task.execution.thread-name-prefix:task-}")
    private String namePrefix;
    @Value("${best.task.execution.pool.keep-alive:10}")
    private int keepAliveSeconds;



    @Bean
    @MonitorThreadPool("4.最优节点返利线程池")
    public Executor bestRebateCalAsync() {
        MonitoredThreadPoolTaskExecutor executor = new MonitoredThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "bestRebateCalAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        /*
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }



}