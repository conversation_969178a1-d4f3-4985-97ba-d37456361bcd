package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * dms返利预测查询请求
 *
 * <AUTHOR>
 */
@Schema(description = "dms返利预测查询请求")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RebateCalDetailQueryRequest {


    @Schema(description = "政策id")
    private String policyId;

    /**
     * 经销商代码
     */
    @Schema(description = "经销商代码")
    @NotBlank(message = "经销商代码不能为空")
    private String dealerCode;



    @Schema(description = "批次 ID")
    @NotBlank(message = "批次 ID 不能为空")
    private String batchId;

    @Schema(description = "系列名称")
    @NotBlank(message = "系列不能为空")
    private String seriesName;

    @Schema(description = "再卖数量")
    private int moreCount;

    @Schema(description = "政策id集合")
    @NotEmpty(message = "政策id集合不能为空")
    private List<String> policyIds;

}
