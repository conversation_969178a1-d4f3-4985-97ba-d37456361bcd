<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 控制台输出配置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">cd
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%logger] [%X{ip}-%X{traceId}-%X{spanId}-%X{ep-opr-code:,}-%X{ep-opr-name:,}-%X{ep-opr-id:,}] - %msg %exception %n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出配置 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/logs/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件的归档格式 -->
            <fileNamePattern>/app/logs/app.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留最近30天的日志文件 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <!-- 设置根日志记录器的级别 -->
    <root level="info">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>

    <!-- 设置特定包的日志级别 -->
    <logger name="com.example.package" level="debug" additivity="false">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </logger>

</configuration>