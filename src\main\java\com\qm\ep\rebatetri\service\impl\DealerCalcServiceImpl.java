package com.qm.ep.rebatetri.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qm.ep.rebatetri.domain.dto.RebateQueryListRequest;
import com.qm.ep.rebatetri.domain.dto.RebateQueryRequest;
import com.qm.ep.rebatetri.domain.dto.RebateQueryResponse;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.DealerCalcService;
import com.qm.tds.api.mp.pagination.QmPage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DealerCalcServiceImpl implements DealerCalcService {

    @Autowired
    private PolicyMapper policyMapper;

    @Resource
    private RebateBaseRemote rebateBaseRemote;

    @Override
    public QmPage<RebateQueryResponse> queryPolicy(RebateQueryRequest request) {
        IPage<RebateQueryRequest> queryPage = new Page<>();
        queryPage.setSize(request.getPageSize()==null?10000:request.getPageSize());
        queryPage.setCurrent(request.getPageNum()==null?1:request.getPageNum());

        IPage<RebateQueryResponse> rebateQueryResponses = policyMapper.selectConfigPoliciesByCondition(queryPage, request);


        QmPage<RebateQueryResponse> page = new QmPage<>();
        page.setPageInfo(rebateQueryResponses);

        return page;
    }

    @Override
    public List<RebateQueryResponse> queryPolicyList(RebateQueryListRequest request) {
        return rebateBaseRemote.queryPolicyList(request);
    }

    @Override
    public QmPage<RebateQueryResponse> queryPolicyByRecordStatus(RebateQueryRequest request) {
        IPage<RebateQueryRequest> queryPage = new Page<>();
        queryPage.setSize(request.getPageSize()==null?10000:request.getPageSize());
        queryPage.setCurrent(request.getPageNum()==null?1:request.getPageNum());

        IPage<RebateQueryResponse> rebateQueryResponses = policyMapper.queryPolicyByRecordStatus(queryPage, request);


        QmPage<RebateQueryResponse> page = new QmPage<>();
        page.setPageInfo(rebateQueryResponses);

        return page;
    }
}
