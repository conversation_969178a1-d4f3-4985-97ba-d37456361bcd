<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.TrailBusinessCacheMapper">
    <select id="cacheBusinessAakData" resultType="com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO">
        SELECT
            field1 as dealerCode,
            field10 as  series,
            field6  as month,
            #{year} as year,
            '经销商提车统计' as tableName,
		sum(CAST( IFNULL( field25, '0' ) AS SIGNED )) nqty,
		'aak' as trailType
        FROM
            businessdata
        WHERE
            tablename = '经销商提车统计'
          AND field4 = '零售'
          AND field5 = #{year}
        GROUP BY
            field1,
            field10,
            field6

    </select>
    <select id="cacheBusinessStdData" resultType="com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO">
        SELECT
            field1 as dealerCode,
            field10 as series,
            #{year} as year,
            field6 as month,
	'经销商提车统计' as tableName,
	sum(CAST( IFNULL( field25, '0' ) AS SIGNED )) nqty,
	'std' as trailType
        FROM
            businessdata
        WHERE
            tablename = '经销商提车统计'
          AND field4 = '批发'
          AND field5 = #{year}
        GROUP BY
            field1,
            field10,
            field6
    </select>
    <select id="cacheBusinessInvoiceData" resultType="com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO">
        SELECT
            field27 as dealerCode,
            field22 as series,
            #{year} as year,
            field2 as month,
	'终端发票查询' as tableName,
	sum(CAST( IFNULL( field29, '0' ) AS SIGNED )) nqty,
	'invoice' as trailType
        FROM
            businessdata
        WHERE
            tablename = '终端发票查询'
          AND field1 = #{year}
        GROUP BY
            field27,
            field22,
            field2
        HAVING dealerCode is not NULL
    </select>
    <select id="cacheBusinessAakAimData" resultType="com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO">
        SELECT
            a.field1 dealerCode,
            a.field8 series,
            #{year} as year,
            SUBSTR(field3,5) AS month,
            '确认销售目标' as tableName,
            sum(
            CAST( IFNULL( field9, '0' ) AS SIGNED )) nqty,
            'aak' AS trailType
                FROM
                    businessdata a
                WHERE
                    tablename = '确认销售目标'
                  AND a.field2 = '经销商季度MAP目标'
                  AND a.field10 IN ( '下达', '区域确认', '经销商确认' )
                  AND a.field3 LIKE CONCAT(#{year}, '%' )
                GROUP BY
                    a.field1,
                    a.field8,
                    a.field3


    </select>
    <select id="cacheBusinessStdAimData" resultType="com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO">
        SELECT a.field1 dealerCode,
               a.field8 series,
               #{year} as year,
            SUBSTR(field3,5) AS month,
            '确认销售目标' as tableName,
            sum(
            CAST( IFNULL( field9, '0' ) AS SIGNED )) nqty,
            'std' AS trailType
        FROM
            businessdata a
        WHERE
            tablename = '确认销售目标'
          AND a.field2 = '经销商STD目标'
          AND a.field10 IN ( '下达'
            , '区域确认'
            , '经销商确认' )
          AND a.field3 LIKE CONCAT(#{year}
            , '%' )
        GROUP BY
            a.field1,
            a.field8,
            a.field3
    </select>

    <select id="getDealerActualStd" resultType="java.lang.Integer">
        SELECT IFNULL(sum(nqty),0) from trail_business_cache where tableName='经销商提车统计'
        and dealerCode=#{dealerCode}
        and trailType='std'
        and year=#{year}
        <if test="series != null and series != '' and series != '全系'">
            and series=#{series}
        </if>
        <if test="stdMonths != null and stdMonths.size > 0">
            and month in
            <foreach collection="stdMonths" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="series != null and series == '全系'">
            AND series in (
            SELECT DISTINCT series from  `trial_calc_result_summary` WHERE  dealerCode = #{dealerCode}
            <if test="policyIds != null and policyIds.size > 0">
                and policyId in
                <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
    </select>
    <select id="getDealerActualAak" resultType="java.lang.Integer">
        SELECT IFNULL(sum(nqty),0) from trail_business_cache where tableName='经销商提车统计'
        and dealerCode=#{dealerCode}
        and trailType='aak'
        and year=#{year}
        <if test="series != null and series != '' and series != '全系'">
            and series=#{series}
        </if>
        <if test="aakMonths != null and aakMonths.size > 0">
            and month in
            <foreach collection="aakMonths" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="series != null and series == '全系'">
        	AND series in (
            SELECT DISTINCT series from  `trial_calc_result_summary` WHERE  dealerCode = #{dealerCode}
            <if test="policyIds != null and policyIds.size > 0">
                and policyId in
                <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
    </select>
    <select id="getDealerActualInvoice" resultType="java.lang.Integer">
        SELECT IFNULL(sum(nqty),0) from trail_business_cache where tableName='终端发票查询'
        and dealerCode=#{dealerCode}
        and trailType='invoice'
        and year=#{year}
        <if test="series != null and series != '' and series != '全系'">
            and series=#{series}
        </if>
        <if test="invoiceMonths != null and invoiceMonths.size > 0">
            and month in
            <foreach collection="invoiceMonths" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="series != null and series == '全系'">
            AND series in (
            SELECT DISTINCT series from  `trial_calc_result_summary` WHERE  dealerCode = #{dealerCode}
            <if test="policyIds != null and policyIds.size > 0">
                and policyId in
                <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
    </select>
    <select id="getAakMap" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(sum_nqty),0)
            FROM (
            SELECT
            IFNULL(sum(nqty), 0) AS sum_nqty
            FROM
            trail_business_cache
            WHERE
            tableName = '确认销售目标'
            AND trailType = 'aak'
            AND YEAR = #{year}
            <if test="series != null and series != '' and series != '全系'">
                AND series = #{series}
            </if>

            <if test="aakMonths != null and aakMonths.size > 0">
                and month in
                <foreach collection="aakMonths" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and dealerCode=#{dealerCode}
            </if>
        GROUP BY
        dealerCode
        ) AS subquery
    </select>
    <select id="getStdMap" resultType="java.lang.Integer">
        SELECT
        IFNULL(MAX(sum_nqty),0)
        FROM (
        SELECT
        IFNULL(sum(nqty), 0) AS sum_nqty
        FROM
        trail_business_cache
        WHERE
        tableName = '确认销售目标'
        AND trailType = 'std'
        AND YEAR = #{year}
        <if test="series != null and series != '' and series != '全系'">
            AND series = #{series}
        </if>
        <if test="stdMonths != null and stdMonths.size > 0">
            and month in
            <foreach collection="stdMonths" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            and dealerCode=#{dealerCode}
        </if>
        GROUP BY
        dealerCode
        ) AS subquery
    </select>
</mapper>
