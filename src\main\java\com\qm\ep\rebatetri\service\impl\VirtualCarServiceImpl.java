package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.qm.ep.rebatetri.constant.Constants;
import com.qm.ep.rebatetri.domain.bean.*;
import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.enumerate.*;
import com.qm.ep.rebatetri.mapper.*;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.*;
import com.qm.ep.rebatetri.utils.ReflectionMethodUtil;
import com.qm.ep.rebatetri.utils.SqlUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.JSONUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class VirtualCarServiceImpl implements VirtualCarService {
    @Autowired
    BusinessdataUnrealServiceImpl businessdataUnrealService;

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private UnrealPolicyRecordService unrealPolicyRecordService;
    @Autowired
    private TrailCalAsyncTask trailCalAsyncTask;


    @Value("${rebate.trial.mapMax:0}")
    private int mapMax;

    @Autowired
    private ExecCalcLogService execCalcLogService;

    @Autowired
    private UnrealCalcHistoryMapper unrealCalcHistoryMapper;


    @Autowired
    private RedFlagTrialMainMapper redFlagTrialMainMapper;
    @Autowired
    private UnrealCalcResultMapper unrealCalcResultMapper;

    @Autowired
    private ExecTrialCalcResultService execTrialCalcResultService;

    @Resource
    RebateBaseRemote rebateBaseRemote;

    @Resource
    private TrialCalcDataService trialCalcDataService;

    @Resource
    private BusinessDataMapper businessDataMapper;

    @Resource
    private BusinessDataUnrealMapper businessDataUnrealMapper;

    @Resource
    private UnrealCalcResultServiceImpl unrealCalcResultServiceImpl;

    @Resource
    private ExecCalcService execCalcService;

    @Resource
    private TrialCalcCoreService trialCalcCoreService;


    @Override
    public void deleteUnrealData() {

        List<UnrealCalcHistoryDO> historyList = unrealCalcHistoryMapper
                .getUnrealCalcHistoryByPolicyIdAndObjectType(null, CalcObjectTypeEnum.UNREAL.getCode());

        List<String> historyIdList = historyList.stream().map(UnrealCalcHistoryDO::getId).distinct().collect(Collectors.toList());

        unrealCalcHistoryMapper.deleteByObjectType(CalcObjectTypeEnum.UNREAL.getCode());

        if (!historyIdList.isEmpty()) {
            unrealCalcResultMapper.deleteByHistoryIds(historyIdList);
        }

        businessDataUnrealMapper.deleteAll();

    }

    @Override
    public void createData(String policyId) {

        log.info("--------createData---------------policyId--------------" + policyId);

        List<UnrealCalcHistoryDO> unrealCalcHistoryList = unrealCalcHistoryMapper
                .getUnrealCalcHistoryByPolicyIdAndObjectType(policyId, CalcObjectTypeEnum.UNREAL.getCode());

        // 找出需要虚拟的数据
        List<VirtualCarDTO> virtualCarList = new ArrayList<>();

        unrealCalcHistoryList.forEach(item -> {

            try {

                RedFlagTrialMainDO redFlagTrialMain = rebateBaseRemote.getRedFlagMainById(item.getObjectId()).getData();

                List<BusinessConstructionDTO> businessConstruction = rebateBaseRemote.getDataColumnAndId(redFlagTrialMain.getMainTable()).getDataList();

                List<BusinessConstructionDTO> carSeriesInfo = businessConstruction.stream().filter(construction ->
                        construction.getFieldName().equals(Constants.SERIES)).toList();

                List<BusinessConstructionDTO> dealerCodeInfo = businessConstruction.stream().filter(construction ->
                        construction.getFieldName().equals(Constants.DEALER_CODE)).toList();

                List<BusinessConstructionDTO> vinInfo = businessConstruction.stream().filter(construction ->
                        construction.getFieldName().equals(Constants.VIN)).toList();

                List<BusinessConstructionDTO> typeInfo = businessConstruction.stream().filter(construction ->
                        construction.getFieldName().equals(Constants.TYPE)).toList();

                if (!carSeriesInfo.isEmpty() && !dealerCodeInfo.isEmpty() && !vinInfo.isEmpty()) {

                    virtualCarList.add(VirtualCarDTO.builder()
                            .historyId(item.getId())
                            .carSeriesField(carSeriesInfo.get(0).getRelevanceFieldName())
                            .dealerCodeField(dealerCodeInfo.get(0).getRelevanceFieldName())
                            .vinField(vinInfo.get(0).getRelevanceFieldName())
                            .typeField(!typeInfo.isEmpty() ? typeInfo.get(0).getRelevanceFieldName() : "")
                            .dataSource(redFlagTrialMain.getMainTable())
                            .build());
                }
            } catch (Exception e) {
                log.error("-----------试算数据有问题！！！{}", String.valueOf(e));
            }
        });

        if (virtualCarList.isEmpty()) {
            return;
        }

        PolicyPublishConfigDTO policyPublishConfigDTO = new PolicyPublishConfigDTO();
        policyPublishConfigDTO.setCalc(1);
        policyPublishConfigDTO.setVisible(1);
        policyPublishConfigDTO.setPolicyId(policyId);
        List<PolicyPublishConfigDTO> policyPublishConfigList = businessDataMapper.getPolicyPublishConfig(policyPublishConfigDTO);

        if (policyPublishConfigList.isEmpty()) {
            log.error("-----------policy_config没有查询到数据，政策id{}", policyId);
            return;
        }
        // 由于当月的年度aakMap还不全，所以取前一年的
        int year = DateUtil.year(new Date()) - 1;
        int mapMax = 0;
        Map<String, Integer> seriesVsAakMap = trialCalcCoreService.getDealerYearAakTask(year + "");
        for (Integer aakMap : seriesVsAakMap.values()) {
            if (aakMap > mapMax) {
                mapMax = aakMap;
            }
        }

        int finalMapMax = mapMax;
        List<BusinessDataUnrealDO> businessDataUnrealDOList = new ArrayList<>();
        // 针对该政策每一条unreal计算历史，对policy_config的每一个经销商进行处理
        virtualCarList.forEach(virtualCarInfo -> {
            policyPublishConfigList.forEach(policyPublishConfigInfo -> {

                try {

                    StringBuilder sql = new StringBuilder();
                    sql.append("select distinct " + virtualCarInfo.getCarSeriesField() + " from unreal_calc_result where ");
                    sql.append(virtualCarInfo.getDealerCodeField() + " = '" + policyPublishConfigInfo.getDealerCode() + "' ");
                    sql.append(" and  historyId = '" + virtualCarInfo.getHistoryId() + "' ");
                    Map<String, Object> sqlMap = new HashMap<>();
                    sqlMap.put("sql", sql);
                    // 从试算结果中查询出某经销商卖出的车系
                    List<Map> carSeriesList = unrealCalcResultServiceImpl.getBySql(sqlMap);

                    String vinFeildStr = toUpperCaseFirstOne(virtualCarInfo.getVinField());
                    String typeFeildStr = virtualCarInfo.getTypeField().equals("") ? "" : toUpperCaseFirstOne(virtualCarInfo.getTypeField());

                    String tableName = virtualCarInfo.getDataSource();

                    carSeriesList.forEach(item -> {

                        Set keyset = item.keySet();

                        for (Object key : keyset) {

                            String carSeries = (String) item.get(key);

                            // int mapMax = trialCalcCoreService.getMapMax("Y", policyPublishConfigInfo.getDealerCode(), carSeries);

                            // Integer mapMax = seriesVsAakMap.get(carSeries);
                            // if (mapMax == null) {
                            //     mapMax = 0;
                            // }
                            StringBuilder sql1 = new StringBuilder();
                            sql1.append("select * from unreal_calc_result where ");
                            sql1.append(virtualCarInfo.getCarSeriesField() + " = '" + carSeries + "' ");
                            sql1.append(" and " + virtualCarInfo.getDealerCodeField() + " = '" + policyPublishConfigInfo.getDealerCode() + "' ");
                            sql1.append(" and  historyId = '" + virtualCarInfo.getHistoryId() + "' ");
                            sql1.append(" limit 1");

                            Map<String, Object> sqlMap1 = new HashMap<>();
                            sqlMap1.put("sql", sql1);
                            // 从试算结果中找到一台有返利的车
                            List<Map> result = unrealCalcResultServiceImpl.getBySql(sqlMap1);
                            log.info("计算历史id{},从试算结果中找到一台有返利的车:{}", virtualCarInfo.getHistoryId(), result);
                            if (result.size() > 0) {
                                BusinessDataUnrealDO businessDataUnrealDO =
                                        JSONObject.parseObject(JSONObject.toJSONString(result.get(0)), BusinessDataUnrealDO.class);
                                // 复制虚拟车
                                for (int i = 0; i < finalMapMax; i++) {

                                    BusinessDataUnrealDO newBusinessDataUnrealDO = new BusinessDataUnrealDO();
                                    BeanUtils.copyProperties(businessDataUnrealDO, newBusinessDataUnrealDO);
                                    newBusinessDataUnrealDO.setField81(policyId);
                                    newBusinessDataUnrealDO.setTablename(tableName);
                                    newBusinessDataUnrealDO.setId(UUID.randomUUID().toString());
                                    ReflectionMethodUtil.setValue(newBusinessDataUnrealDO, vinFeildStr, "unreal_vin_" + UUID.randomUUID().toString());
                                    if (Objects.nonNull(typeFeildStr) && ObjectUtils.isNotEmpty(typeFeildStr)) {
                                        ReflectionMethodUtil.setValue(newBusinessDataUnrealDO, typeFeildStr, "零售");
                                    }
                                    businessDataUnrealDOList.add(newBusinessDataUnrealDO);
                                }
                            }

                        }

                    });


//                    List<DealerTaskDTO> dealerTaskDTOS= trialCalcDataService.getDealerTask(policyPublishConfigInfo.getDealerCode(),"Y");
//                    dealerTaskDTOS.forEach(dealerTask ->{
//                        StringBuilder sql = new StringBuilder();
//                        sql.append("select * from unreal_calc_result where ");
//                        sql.append( virtualCarInfo.getCarSeriesField()+" = '"+dealerTask.getSeries()+"' ");
//                        sql.append(" and "+virtualCarInfo.getDealerCodeField()+" = '"+dealerTask.getDealerCode()+"' ");
//                        sql.append(" and  historyId = '"+virtualCarInfo.getHistoryId()+"' ");
//                        sql.append(" limit 1");
//
//                        Map<String, Object> sqlMap = new HashMap<>();
//                        sqlMap.put("sql", sql);
//                        List<Map> result = unrealCalcResultServiceImpl.getBySql(sqlMap);
//                        log.info(String.valueOf(result.size()));
//
//                        String vinFeildStr = toUpperCaseFirstOne(virtualCarInfo.getVinField());
//                        String tableName = virtualCarInfo.getDataSource();
//
//                        if(result.size()>0){
//                            BusinessDataUnrealDO businessDataUnrealDO =
//                                    JSONObject.parseObject(JSONObject.toJSONString(result.get(0)), BusinessDataUnrealDO.class);
//
//                            for(int i=0;i<dealerTask.getTaskQty()*4;i++){
//
//                                BusinessDataUnrealDO newBusinessDataUnrealDO = new BusinessDataUnrealDO();
//                                BeanUtils.copyProperties(businessDataUnrealDO,newBusinessDataUnrealDO);
//                                newBusinessDataUnrealDO.setField81(policyId);
//                                newBusinessDataUnrealDO.setTablename(tableName);
//                                newBusinessDataUnrealDO.setId(UUID.randomUUID().toString());
//                                ReflectionMethodUtil.setValue(newBusinessDataUnrealDO, vinFeildStr ,"unreal_vin_"+ UUID.randomUUID().toString());
//                                businessDataUnrealDOList.add(newBusinessDataUnrealDO);
//                            }
//                        }
//                    });
                } catch (Exception e) {
                    log.error("-----------虚拟车数据有问题！！！" + e);
                }
            });
        });

        if (!businessDataUnrealDOList.isEmpty()) {
            businessDataUnrealMapper.batchInsert(businessDataUnrealDOList);
        } else {
            log.info("-------------------未虚拟数据-----------------" + policyId);
        }
    }

    @Override
    public void completeProcess() {
        // 需要虚拟的政策
        List<ExecCalcDTO> execCalcDTOList = new ArrayList<>();
        List<ExecCalcDTO> execCalcMonthList = trialCalcDataService.getCalcPolicy(TimeDimensionEnum.MONTH.getCode());
        List<ExecCalcDTO> execCalcQuarterList = trialCalcDataService.getCalcPolicy(TimeDimensionEnum.QUARTER.getCode());
        List<ExecCalcDTO> execCalcYearList = trialCalcDataService.getCalcPolicy(TimeDimensionEnum.YEAR.getCode());
        execCalcDTOList.addAll(execCalcMonthList);
        execCalcDTOList.addAll(execCalcQuarterList);
        execCalcDTOList.addAll(execCalcYearList);

        List<String> execCalcIdList = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).collect(Collectors.toList());

        execCalcIdList = new ArrayList<>(new HashSet<>(execCalcIdList));

        if (execCalcIdList.size() == 0) {
            return;
        }

        log.info("-----------------------需要虚拟的政策id-----------" + JSONUtils.beanToJson(execCalcIdList));

        // 删除历史数据
        deleteUnrealData();

        log.info("---------------------已删除旧数据-------------------");

        execCalcIdList.forEach(policyId -> {

            log.info("--------------------当前政策id-------------------" + policyId);

            // 红旗伙伴配置查询
            List<RedFlagTrialMainDO> redFlagTrialMainDOList = rebateBaseRemote.getRedFlagMainByPolicyId(policyId).getDataList();

            // 查出符合条件的历史数据
            redFlagTrialMainDOList.forEach(redFlagTrialMainDO -> {

                ExecCalcDTO execCalcDTO = new ExecCalcDTO();
                execCalcDTO.setPolicyId(policyId);
                execCalcDTO.setObjectId(redFlagTrialMainDO.getId());
                execCalcDTO.setObjectType(CalcObjectTypeEnum.UNREAL);
                execCalcDTO.setCalcType(CalcTypeEnum.TRIAL);

                log.info("--------------------开始查询符合当前'红旗伙伴配置'的历史数据-------------------" + JSONUtils.beanToJson(execCalcDTO));

                execCalcService.startCalcForUnreal(execCalcDTO);
            });

            createData(policyId);
        });
    }



    /**
     * 直接从合并计算方案试算结果中获取返利不为负数的车系，然后从经销商提车统计中通过vin获取并制造虚拟车
     *
     * @param policyId 策略id
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public List<BusinessDataDO> reCalCombination(String policyId) {

        log.info("开始执行合并方案计算，政策id:{}", policyId);
        PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
        String trailType = policyDO.getTrailType();

        // 查询该政策的合并方案
        List<CombinationMainDO> combinationList = businessDataUnrealMapper.selectCombinationByPolicyId(policyId);
        if (CollUtil.isEmpty(combinationList)) {
            throw new QmException("该政策没有配置合并方案");
        }

        ExecCalcDTO execCalcUnrealDTO = new ExecCalcDTO();
        execCalcUnrealDTO.setPolicyId(policyId);
        execCalcUnrealDTO.setObjectId(combinationList.get(0).getId());
        execCalcUnrealDTO.setObjectType(CalcObjectTypeEnum.COMBINATION);
        execCalcUnrealDTO.setObjectName(combinationList.get(0).getCombinationName());
        execCalcUnrealDTO.setCalcType(CalcTypeEnum.TRIAL);
        execCalcUnrealDTO.setExecType(ExecTypeEnum.UNREAL_REBATE_COMBINATION);

        // 模拟进行一次合并方案的计算-政策配置计算
        String historyId = this.startTralCal(execCalcUnrealDTO);

        // 获取有返利>=0的车系的vin，用于后续复制制造虚拟车
        List<DealerSeriesVinDTO> rebateSeriesVin = this.getRebateSeriesVin(historyId, trailType);
        if (CollUtil.isEmpty(rebateSeriesVin)) {
            throw new QmException("没有在虚拟计算结果表中查到车");
        }

        // 查询出该政策下所有返利>=0的车的vin
        List<String> vines = rebateSeriesVin.stream().map(DealerSeriesVinDTO::getVin).collect(Collectors.toList());

        // 根据vin找到对应的车辆信息，排除退车的vin
        String type = null;
        if (StringUtils.isBlank(trailType)) {
            throw new QmException("试算类型trailType不能为空");
        }

        return switch (trailType) {
            case Constants.AAK -> {
                type = "零售";
                yield batchSelectByVins(vines, type, 200);
            }
            case Constants.STD -> {
                type = "批发";
                yield batchSelectByVins(vines, type, 200);
            }
            case Constants.INVOICE -> batchSelectInvoiceByVins(vines, 200);
            default -> throw new QmException("不支持的试算类型: " + trailType);
        };


    }

    // 分批查询方法
    private List<BusinessDataDO> batchSelectByVins(List<String> vines, String type, int batchSize) {
        if (vines.size() <= batchSize) {
            return businessDataMapper.selectByVins(vines, type);
        }

        return Lists.partition(vines, batchSize)
                .stream()
                .flatMap(batch -> businessDataMapper.selectByVins(batch, type).stream())
                .collect(Collectors.toList());
    }

    // 分批查询发票信息
    private List<BusinessDataDO> batchSelectInvoiceByVins(List<String> vines, int batchSize) {
        if (vines.size() <= batchSize) {
            return businessDataMapper.selectInvoiceByVins(vines);
        }

        return Lists.partition(vines, batchSize)
                .stream()
                .flatMap(batch -> businessDataMapper.selectInvoiceByVins(batch).stream())
                .collect(Collectors.toList());
    }

    private String startTralCal(ExecCalcDTO execCalcDTO) {

        log.info("开始获取模拟合并方案计算获取SQL，参数：{}", JSONUtils.beanToJson(execCalcDTO));

        CalcObjectDTO calcObjectDTO = new CalcObjectDTO();
        calcObjectDTO.setPolicyId(execCalcDTO.getPolicyId());
        calcObjectDTO.setObjectId(execCalcDTO.getObjectId());
        calcObjectDTO.setObjectType(execCalcDTO.getObjectType());
        calcObjectDTO.setAbbreviated(true);
        JsonResultVo<SqlStructureVO> sqlStructureFirst = rebateBaseRemote.getRealSqlStructure(calcObjectDTO);

        UnrealCalcHistoryDO execCalcHistoryDO = new UnrealCalcHistoryDO();
        execCalcHistoryDO.setPolicyId(execCalcDTO.getPolicyId());
        execCalcHistoryDO.setObjectId(execCalcDTO.getObjectId());
        execCalcHistoryDO.setObjectType(execCalcDTO.getObjectType());
        execCalcHistoryDO.setObjectName(execCalcDTO.getObjectName());
        execCalcHistoryDO.setExecType(execCalcDTO.getExecType());
        execCalcHistoryDO.setState(StateEnum.PROCESS);
        execCalcHistoryDO.setBegin(DateUtils.getSysdateTime());
        execCalcHistoryDO.setSqlStructure(JSONUtils.beanToJson(sqlStructureFirst.getData()));

        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("创建试算任务").build());
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行SQL：" + sqlStructureFirst.getData().getTemporaryTableSqlFormatted() + "\n\n" + sqlStructureFirst.getData().getSqlFormatted()).build());

        execCalcHistoryDO.setDtstamp(DateUtils.getSysdateTime());
        unrealCalcHistoryMapper.insert(execCalcHistoryDO);

        log.info("准备计算执行：prepareCalc(ExecCalcDTO execCalcDTO={})结束", JSONUtil.toJsonStr(execCalcDTO));

        String historyId = execCalcHistoryDO.getId();

        SqlStructureVO sqlStructure = null;
        Map<String, Object> sqlMap = new HashMap<>(1);
        log.info("试算执行：execCalc(String historyId={}) 开始", historyId);
        int retryCount = 0;
        while (retryCount < 3) {
            execCalcHistoryDO = unrealCalcHistoryMapper.selectById(historyId);
            if (execCalcHistoryDO != null) {
                break;
            }
            retryCount++;
            try {
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        if (execCalcHistoryDO == null) {
            log.error("获取当前试算历史{}日志出错，本次试算退出", historyId);
            return "";
        } else {
            log.info("获取当前试算历史{}日志成功！尝试次数：{}", historyId, retryCount);
        }

        execCalcHistoryDO.setExecutorThreadName("模拟合并方案计算:" + Thread.currentThread().getName());

        try {
            String sqlStructureBase = execCalcHistoryDO.getSqlStructure();
            sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);


            if (BootAppUtil.isnotNullOrEmpty(sqlStructure.getTemporaryTableSqlFormatted())) {
                sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
                // 创建临时表
                log.info("创建临时表：execTrialCalcResultService.getBySql(sqlMap)开始");
                execTrialCalcResultService.getBySql(sqlMap);
            }

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);

            log.info("开始获取计算结果记录数Total：execTrialCalcResultService.getBySql开始");
            List<Map> countResult = execTrialCalcResultService.getBySql(sqlMap);

            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT, "15");
            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : 5000000;
            if (CollUtil.isNotEmpty(countResult) && total <= maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i - 1))) + ") as field" + i);
                }

                String sql = " insert ignore into unreal_calc_result(historyId," + StringUtils.join(fields, ",") + ") \n" +
                        " select '" + historyId + "' as historyId,sqlbase.* from " + sqlbase;

                sqlMap.put("sql", sql);

                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算SQL语句").build());

                log.info("保存试算结果：execTrialCalcResultService.saveBySql开始");

                int insertCount = execTrialCalcResultService.saveBySql(sqlMap);

                log.info("保存试算结果：execTrialCalcResultService.saveBySql结束");

                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算成功，共生成" + insertCount + "条记录").build());

                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());


                List<Map> sumResult = execTrialCalcResultService.getBySql(sqlMap);


                if (CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    execCalcHistoryDO.setSumResult(rs);
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功！结果为：" + rs).build());
                } else {
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                execCalcHistoryDO.setState(StateEnum.FINISH);
            } else {
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("试算结果记录数" + total + "。由于记录数大于500万，本次试算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                execCalcHistoryDO.setState(StateEnum.ERROR);
            }


        } catch (Exception e) {
            execCalcHistoryDO.setState(StateEnum.ERROR);
            execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算出错\n" + e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {

            log.info("试算执行：execCalc(String historyId={}) 结束前", historyId);

            // 删除临时表
            log.info("删除临时表：execTrialCalcResultService.getBySql开始");

            Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
            if (null != temporaryTableSqlMap) {
                List<String> dropTemporaryTableSql = new ArrayList<>();
                temporaryTableSqlMap.values().forEach(s -> s.keySet().forEach(t -> dropTemporaryTableSql.add(" DROP TEMPORARY TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                if (CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                    sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                    try {
                        execTrialCalcResultService.getBySql(sqlMap);
                    } catch (Exception e) {
                        log.error("Failed to delete temporary table", e);
                    }

                }
            }

            log.info("删除临时表：execTrialCalcResultService.getBySql结束");

            execCalcHistoryDO.setLog(execCalcLogService.append(execCalcHistoryDO.getLog(), execCalcLogStructures));
            execCalcHistoryDO.setEnd(DateUtils.getSysdateTime());

            // 不更新SqlStructure
            execCalcHistoryDO.setSqlStructure(null);

            // 这里经常出现链接断开：Error updating database.  Cause: java.sql.SQLException: connection disabled
            unrealCalcHistoryMapper.updateById(execCalcHistoryDO);
            log.info("试算执行：execCalc(String historyId={}) 结束后", historyId);


        }

        return historyId;

    }

    @Override
    public void doCopyVin(String policyId, List<BusinessDataDO> vinOrInvoiceList, String trailType) {
        log.info("--------doCopyVin开始---------------policyId--------------{}", policyId);
        Map<String, List<BusinessDataDO>> table = new HashMap<>(16);
        // 每一個经销商下某车系的有返利的车的底表数据(经销商提车统计和终端发票查询)
        if (StringUtils.containsAny(trailType, Constants.AAK, Constants.STD)) {
            table = vinOrInvoiceList.stream().collect(Collectors.groupingBy(BusinessDataDO::getField1));
        } else {
            table = vinOrInvoiceList.stream().filter(ele->StringUtils.isNotBlank(ele.getField27())).collect(Collectors.groupingBy(BusinessDataDO::getField27));
        }
        // 查询配置了试算器的经销商列表
        PolicyPublishConfigDTO queryDTO = new PolicyPublishConfigDTO();
        queryDTO.setCalc(1);
        queryDTO.setVisible(1);
        queryDTO.setPolicyId(policyId);
        List<PolicyPublishConfigDTO> policyPublishConfigList = businessDataMapper.getPolicyPublishConfig(queryDTO);

        if (policyPublishConfigList.isEmpty()) {
            log.error("-----------policy_config没有查询到数据，政策id{}", policyId);
            return;
        }
        int finalMapMax = 0;
        if (mapMax != 0) {
            finalMapMax = mapMax;
        } else {
            // 由于当月的年度aakMap还不全，所以取前一年的
            int year = DateUtil.year(new Date()) - 1;
            int tempMax = 0;
            Map<String, Integer> SeriesVsAakMap = trialCalcCoreService.getDealerYearAakTask(year + "");
            for (Integer aakMap : SeriesVsAakMap.values()) {
                if (aakMap > tempMax) {
                    tempMax = aakMap;
                }
            }
            finalMapMax = tempMax;
        }


        try {
            if (StringUtils.containsAny(trailType, Constants.AAK, Constants.STD)) {
                trailCalAsyncTask.doInsertCarIntoUnrealBusinessdata(policyPublishConfigList, finalMapMax, policyId, table);
            } else {
                trailCalAsyncTask.doInsertInvoiceIntoUnrealBusinessdata(policyPublishConfigList, finalMapMax, policyId, table);
            }
        } catch (Exception e) {
            log.info("插入虚拟车或发票出现了问题,政策id为{}", policyId);
            log.error(e.getMessage(), e);
            throw new QmException("插入虚拟车出现了问题" + e.getMessage());
        }


    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void copyVinAndInitRebateDataAsync(List<BusinessDataDO> rebateSeriesVin, UnrealPolicyRecordPO recordPO, String trailType) {
        String policyId = recordPO.getPolicyId();
        try {
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("copyVinAndInitRebateDataAsync 被中断，completeProcessAndInitRebateData执行出错，需要虚拟的政策ID: {}", policyId);
                return;
            }
            // 复制车到unreal_business_data表中
            this.doCopyVin(policyId, rebateSeriesVin, trailType);
            // 异步初始化返利数据
            this.initRebateData(policyId, null);
        } catch (Exception e) {
            log.info("-----------------------completeProcessAndInitRebateData执行出错，需要虚拟的政策id{}-----------", policyId);
            unrealPolicyRecordService.updateUnrealPolicyStatusById(recordPO.getId(), e.getMessage());
        }
    }


    private void doCreateSync(String policyId, Map<String, List<BusinessDataDO>> table, List<PolicyPublishConfigDTO> policyPublishConfigList, int finalMapMax) {
        List<BusinessDataUnrealDO> businessDataUnrealDOList = new ArrayList<>();
        policyPublishConfigList.forEach(policyPublishConfigInfo -> {
            String dealerCode = policyPublishConfigInfo.getDealerCode();
            // 一个经销商下所有的车系车数据
            List<BusinessDataDO> vinData = table.get(dealerCode);
            if (vinData != null) {
                vinData.forEach(businessDataDO -> {
                    businessDataUnrealDOList.clear();
                    // 复制该经销商具体的车为虚拟车
                    for (int i = 0; i < finalMapMax; i++) {
                        BusinessDataUnrealDO newBusinessDataUnrealDO = new BusinessDataUnrealDO();
                        BeanUtils.copyProperties(businessDataDO, newBusinessDataUnrealDO);
                        newBusinessDataUnrealDO.setField81(policyId);
                        newBusinessDataUnrealDO.setId(UUID.randomUUID().toString());
                        newBusinessDataUnrealDO.setField20("unreal_vin_" + UUID.randomUUID());
                        newBusinessDataUnrealDO.setTablename("经销商提车统计");
                        businessDataUnrealDOList.add(newBusinessDataUnrealDO);
                    }
                    businessDataUnrealMapper.batchInsert(businessDataUnrealDOList);

                });
            }
        });
    }

    @Override
    public List<DealerSeriesVinDTO> getRebateSeriesVin(String historyId, String type) {

        ExecTrialCalcHistoryDO historyDO = unrealCalcHistoryMapper.selectSimpleById(historyId);
        // 根据historyId查询返利不为0的车
        // 校验入账数据字段
        String sqlStructure = historyDO.getSqlStructure();
        JSONObject sqlStructureObject = JSON.parseObject(sqlStructure);
        JSONArray fields = sqlStructureObject.getJSONArray("fields");
        // 校验入账数据字段是否包含以下字段
        String[] requiredFields = {"返利金额", Constants.SERIES, Constants.VIN, Constants.DEALER_CODE};
        for (String requiredField : requiredFields) {
            if (!fields.contains(requiredField)) {
                throw new QmException(CharSequenceUtil.format("该商务政策缺少<{}>字段", requiredField));
            }
        }
        // 查询入账数据
        int rebateAmountIndex = -1;
        int seriesIndex = -1;
        int vinIndex = -1;
        int dealerCodeIndex = -1;
        for (int i = 1; i <= fields.size(); i++) {
            if (fields.getString(i - 1).equals("返利金额")) {
                rebateAmountIndex = i;
            }
            if (fields.getString(i - 1).equals(Constants.SERIES)) {
                seriesIndex = i;
            }
            if (fields.getString(i - 1).equals(Constants.VIN)) {
                vinIndex = i;
            }
            if (fields.getString(i - 1).equals(Constants.DEALER_CODE)) {
                dealerCodeIndex = i;
            }
        }
        String rebateAmountField = "field" + rebateAmountIndex;
        String seriesField = "field" + seriesIndex;
        String vinField = "field" + vinIndex;
        String dealerCodeField = "field" + dealerCodeIndex;
        List<DealerSeriesVinDTO> dealerSeriesVinDTOS = new ArrayList<>();
        if (Constants.AAK.equals(type) || Constants.STD.equals(type)) {
            dealerSeriesVinDTOS = unrealCalcHistoryMapper.selectAakAndStdRebateResult(historyId, rebateAmountField, seriesField, vinField, dealerCodeField);
        } else if (Constants.INVOICE.equals(type)) {
            dealerSeriesVinDTOS = unrealCalcHistoryMapper.selectInvoiceRebateResult(historyId, rebateAmountField, seriesField, vinField, dealerCodeField);
        } else {
            throw new QmException(type + "类型不存在");
        }
        return dealerSeriesVinDTOS;
    }

    @Override
    public void initRebateData(String policyId, String historyId) {
        log.info("开始执行虚拟车返利{}", policyId);
        businessDataUnrealMapper.deleteRebateSummaryByPolicyId(policyId);
        // 1. 计算出当前时间点，所有经销商针对该政策具备返利的aak数量，为后续计算需要的虚拟车数量做铺垫
        // Map<String, Map<String, Integer>> dealerSeriesAAkMap=this.getDealerSeriesRebateAkkCountMap(policyId,historyId);
        // 2. 根据所需数量选择虚拟车
        List<ExecCalcDTO> execObject = businessDataMapper.getCalcPolicyOnAllDealer(policyId);
        if (CollUtil.isEmpty(execObject)) {
            log.info("政策发布时没有进行返利试算器配置，政策id为：{}", policyId);
            throw new QmException("政策发布时没有进行返利试算器配置，政策id为：" + policyId);
        }
        int map = 0;
        if (this.mapMax == 0) {
            map = trialCalcCoreService.getDealerLastYearAllSeriesAakMaxTask();
        } else {
            map = mapMax;
        }


        for (int qty = 1; qty <= map; qty++) {
            ExecCalcDTO execCalcDTO = new ExecCalcDTO();
            execCalcDTO.setPolicyId(policyId);
            execCalcDTO.setObjectId(execObject.get(0).getObjectId());
            execCalcDTO.setObjectType(CalcObjectTypeEnum.COMBINATION);
            execCalcDTO.setCalcType(CalcTypeEnum.TRIAL);
            execCalcDTO.setExecType(ExecTypeEnum.UNREAL_REBATE);
            execCalcDTO.setLimit(qty);
            execCalcDTO.setVflag(1);
            execCalcDTO.setVr(1);
            execCalcDTO.setBatchId(policyId + "-" + qty);
            // 异步开启试算
            log.info("execCalcService.batchExecCalcV3入参{}", execCalcDTO);
            execCalcService.batchExecCalcV3(execCalcDTO);
        }
    }


    private List<RedFlagTrialMainDO> getRedFlagMainByPolicyId(String policyId) {
        QmQueryWrapper<RedFlagTrialMainDO> mainQueryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<RedFlagTrialMainDO> mainlambdaWrapper = mainQueryWrapper.lambda();
        mainlambdaWrapper.eq(RedFlagTrialMainDO::getPolicyId, policyId);
        return redFlagTrialMainMapper.selectList(mainlambdaWrapper);
    }

    public static String toUpperCaseFirstOne(String s) {
        if (Character.isUpperCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }
}
