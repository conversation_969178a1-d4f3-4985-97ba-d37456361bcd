package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;

/**
 * <AUTHOR>
 */
public interface ExecTrialCalcService {

    /**
     * 试算准备
     * @param execCalcDTO 参数
     * @return 返回
     */
    String prepareCalc(ExecCalcDTO execCalcDTO);

    /**
     * 发起计算对象试算
     * @param historyId 试算历史ID
     * @throws InterruptedException 异常
     */
    //@Async("execCalcAsync")
    void execCalc(ExecCalcDTO execCalcDTO,String historyId) throws InterruptedException;

    /**
     * 获取动态sql，初始化history
     *
     * @param execCalcDTO
     * @param policyIdWithComma
     * @return
     */
    String prepareCalcForUnreal(ExecCalcDTO execCalcDTO);

    /**
     * 开始根据sql执行计算
     * @param execCalcDTO
     * @param historyId
     * @throws InterruptedException
     */
    void execCalcForUnreal(ExecCalcDTO execCalcDTO,String historyId) throws InterruptedException;

    /**
     * 开始根据sql执行计算(同步执行)
     *
     * @param execCalcDTO 执行计算dto
     * @param historyId   历史id
     */
    void execCalcForUnrealV2(ExecCalcDTO execCalcDTO, String historyId);


    void execCalcForUnrealV3(ExecCalcDTO execCalcDTO, String  hisrotyId) throws InterruptedException;
}
