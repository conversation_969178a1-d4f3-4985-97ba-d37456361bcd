<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.BusinessDataMapper">

    <resultMap id="BusinessDataMap" type="com.qm.ep.rebatetri.domain.bean.BusinessDataDO">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="TABLENAME" property="tableName" jdbcType="VARCHAR"/>
        <result column="FIELD1" property="field1" jdbcType="VARCHAR"/>
        <result column="FIELD2" property="field2" jdbcType="VARCHAR"/>
        <result column="FIELD3" property="field3" jdbcType="VARCHAR"/>
        <result column="FIELD4" property="field4" jdbcType="VARCHAR"/>
        <result column="FIELD5" property="field5" jdbcType="VARCHAR"/>
        <result column="FIELD6" property="field6" jdbcType="VARCHAR"/>
        <result column="FIELD7" property="field7" jdbcType="VARCHAR"/>
        <result column="FIELD8" property="field8" jdbcType="VARCHAR"/>
        <result column="FIELD9" property="field9" jdbcType="VARCHAR"/>
        <result column="FIELD10" property="field10" jdbcType="VARCHAR"/>
        <result column="FIELD11" property="field11" jdbcType="VARCHAR"/>
        <result column="FIELD12" property="field12" jdbcType="VARCHAR"/>
        <result column="FIELD13" property="field13" jdbcType="VARCHAR"/>
        <result column="FIELD14" property="field14" jdbcType="VARCHAR"/>
        <result column="FIELD15" property="field15" jdbcType="VARCHAR"/>
        <result column="FIELD16" property="field16" jdbcType="VARCHAR"/>
        <result column="FIELD17" property="field17" jdbcType="VARCHAR"/>
        <result column="FIELD18" property="field18" jdbcType="VARCHAR"/>
        <result column="FIELD19" property="field19" jdbcType="VARCHAR"/>
        <result column="FIELD20" property="field20" jdbcType="VARCHAR"/>
        <result column="FIELD21" property="field21" jdbcType="VARCHAR"/>
        <result column="FIELD22" property="field22" jdbcType="VARCHAR"/>
        <result column="FIELD23" property="field23" jdbcType="VARCHAR"/>
        <result column="FIELD24" property="field24" jdbcType="VARCHAR"/>
        <result column="FIELD25" property="field25" jdbcType="VARCHAR"/>
        <result column="FIELD26" property="field26" jdbcType="VARCHAR"/>
        <result column="FIELD27" property="field27" jdbcType="VARCHAR"/>
        <result column="FIELD28" property="field28" jdbcType="VARCHAR"/>
        <result column="FIELD29" property="field29" jdbcType="VARCHAR"/>
        <result column="FIELD30" property="field30" jdbcType="VARCHAR"/>
        <result column="FIELD31" property="field31" jdbcType="VARCHAR"/>
        <result column="FIELD32" property="field32" jdbcType="VARCHAR"/>
        <result column="FIELD33" property="field33" jdbcType="VARCHAR"/>
        <result column="FIELD34" property="field34" jdbcType="VARCHAR"/>
        <result column="FIELD35" property="field35" jdbcType="VARCHAR"/>
        <result column="FIELD36" property="field36" jdbcType="VARCHAR"/>
        <result column="FIELD37" property="field37" jdbcType="VARCHAR"/>
        <result column="FIELD38" property="field38" jdbcType="VARCHAR"/>
        <result column="FIELD39" property="field39" jdbcType="VARCHAR"/>
        <result column="FIELD40" property="field40" jdbcType="VARCHAR"/>
        <result column="FIELD41" property="field41" jdbcType="VARCHAR"/>
        <result column="FIELD42" property="field42" jdbcType="VARCHAR"/>
        <result column="FIELD43" property="field43" jdbcType="VARCHAR"/>
        <result column="FIELD44" property="field44" jdbcType="VARCHAR"/>
        <result column="FIELD45" property="field45" jdbcType="VARCHAR"/>
        <result column="FIELD46" property="field46" jdbcType="VARCHAR"/>
        <result column="FIELD47" property="field47" jdbcType="VARCHAR"/>
        <result column="FIELD48" property="field48" jdbcType="VARCHAR"/>
        <result column="FIELD49" property="field49" jdbcType="VARCHAR"/>
        <result column="FIELD50" property="field50" jdbcType="VARCHAR"/>
        <result column="FIELD51" property="field51" jdbcType="VARCHAR" />
        <result column="FIELD52" property="field52" jdbcType="VARCHAR" />
        <result column="FIELD53" property="field53" jdbcType="VARCHAR" />
        <result column="FIELD54" property="field54" jdbcType="VARCHAR" />
        <result column="FIELD55" property="field55" jdbcType="VARCHAR" />
        <result column="FIELD56" property="field56" jdbcType="VARCHAR" />
        <result column="FIELD57" property="field57" jdbcType="VARCHAR" />
        <result column="FIELD58" property="field58" jdbcType="VARCHAR" />
        <result column="FIELD59" property="field59" jdbcType="VARCHAR" />
        <result column="FIELD60" property="field60" jdbcType="VARCHAR" />
        <result column="FIELD61" property="field61" jdbcType="VARCHAR" />
        <result column="FIELD62" property="field62" jdbcType="VARCHAR" />
        <result column="FIELD63" property="field63" jdbcType="VARCHAR" />
        <result column="FIELD64" property="field64" jdbcType="VARCHAR" />
        <result column="FIELD65" property="field65" jdbcType="VARCHAR" />
        <result column="FIELD66" property="field66" jdbcType="VARCHAR" />
        <result column="FIELD67" property="field67" jdbcType="VARCHAR" />
        <result column="FIELD68" property="field68" jdbcType="VARCHAR" />
        <result column="FIELD69" property="field69" jdbcType="VARCHAR" />
        <result column="FIELD70" property="field70" jdbcType="VARCHAR" />
        <result column="FIELD71" property="field71" jdbcType="VARCHAR" />
        <result column="FIELD72" property="field72" jdbcType="VARCHAR" />
        <result column="FIELD73" property="field73" jdbcType="VARCHAR" />
        <result column="FIELD74" property="field74" jdbcType="VARCHAR" />
        <result column="FIELD75" property="field75" jdbcType="VARCHAR" />
        <result column="FIELD76" property="field76" jdbcType="VARCHAR" />
        <result column="FIELD77" property="field77" jdbcType="VARCHAR" />
        <result column="FIELD78" property="field78" jdbcType="VARCHAR" />
        <result column="FIELD79" property="field79" jdbcType="VARCHAR" />
        <result column="FIELD80" property="field80" jdbcType="VARCHAR" />
        <result column="FIELD81" property="field81" jdbcType="VARCHAR" />
        <result column="FIELDID" property="fieldId" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select
        a.id,
        a.tablename,
        a.field1,
        a.field2,
        a.field3,
        a.field4,
        a.field5,
        a.field6,
        a.field7,
        a.field8,
        a.field9,
        a.field10,
        a.field11,
        a.field12,
        a.field13,
        a.field14,
        a.field15,
        a.field16,
        a.field17,
        a.field18,
        a.field19,
        a.field20,
        a.field21,
        a.field22,
        a.field23,
        a.field24,
        a.field25,
        a.field26,
        a.field27,
        a.field28,
        a.field29,
        a.field30,
        a.field31,
        a.field32,
        a.field33,
        a.field34,
        a.field35,
        a.field36,
        a.field37,
        a.field38,
        a.field39,
        a.field40,
        a.field41,
        a.field42,
        a.field43,
        a.field44,
        a.field45,
        a.field46,
        a.field47,
        a.field48,
        a.field49,
        a.field50,
        a.field51,
        a.field52,
        a.field53,
        a.field54,
        a.field55,
        a.field56,
        a.field57,
        a.field58,
        a.field59,
        a.field60,
        a.field61,
        a.field62,
        a.field63,
        a.field64,
        a.field65,
        a.field66,
        a.field67,
        a.field68,
        a.field69,
        a.field70,
        a.field71,
        a.field72,
        a.field73,
        a.field74,
        a.field75,
        a.field76,
        a.field77,
        a.field78,
        a.field79,
        a.field80,
        a.field81,
        a.fieldId
        from businessdata a
    </sql>
    <delete id="truncateData" parameterType ="java.util.List" >
        <foreach item="tableName" collection="tables" separator=";">
            TRUNCATE TABLE ${tableName}
        </foreach>
    </delete>


    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.BusinessDataDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <sql id="WhereSQL">
        tablename = '确认销售目标'
        AND a.field2 = '经销商季度MAP目标'
        <if test="dealerCode != null and dealerCode != ''">
            AND a.field1 = #{dealerCode}
        </if>
        AND a.field10 IN ( '下达','区域确认','经销商确认' )
        <choose>
            <when test="dim == 'Y'.toString()">
                AND a.field3 LIKE CONCAT(#{year},'%')
            </when>
            <otherwise>
                <if test="ymList != null and !ymList.isEmpty">
                   and a.field3 in (<foreach collection="ymList" item="item" separator=",">#{item}</foreach>)
                </if>
            </otherwise>
        </choose>
    </sql>
    <sql id="WhereSQL_STD">
        tablename = '确认销售目标'
        AND a.field2 = '经销商STD目标'
        <if test="dealerCode != null and dealerCode != ''">
            AND a.field1 = #{dealerCode}
        </if>
        AND a.field10 IN ( '下达','区域确认','经销商确认' )
        <choose>
            <when test="dim == 'Y'.toString()">
                AND a.field3 LIKE CONCAT(#{year},'%')
            </when>
            <otherwise>
                <if test="ymList != null and !ymList.isEmpty">
                    and a.field3 in (<foreach collection="ymList" item="item" separator=",">#{item}</foreach>)
                </if>
            </otherwise>
        </choose>
    </sql>
    <select id="getDealerAAKTask" resultType="com.qm.ep.rebatetri.domain.dto.DealerTaskDTO">
        SELECT
            a.field1 dealerCode,
            a.field8 series,
            sum(a.field9) taskQty,
            #{dim} DIM
        FROM
            businessdata a
            INNER JOIN (
            SELECT
                field3,
                field8,
                MAX( field80 ) maxdate
            FROM
                businessdata a
            WHERE
                <include refid="WhereSQL"/>
            GROUP BY
                field3,field8
            ) b ON a.field3 = b.field3  and a.field8 = b.field8
            AND ifnull( a.field80, '' )= ifnull( b.maxdate, '' )
        WHERE
            <include refid="WhereSQL"/>
        GROUP BY a.field1,a.field8
            </select>
    <select id="getCalcPolicy" resultType="com.qm.ep.rebatetri.domain.dto.ExecCalcDTO">
        SELECT
            a.ID policyId,
            a.VPOLICYNAME policyName,
            c.id objectId,
            'combination' objectType,
            c.combinationname objectName,
            b.DEALER_CODE dealerCode,
            'trial' calcType
        FROM
            policy a
            INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
            INNER JOIN COMBINATIONMAIN c ON c.policyid = a.id
        WHERE
         a.canTrial = '1'
            AND a.VFINISHSTATE = '30'
            AND b.VISIBLE = '1'
            AND b.CALC = '1'
            AND a.dbegin BETWEEN #{beginDate} and #{endDate}
            AND a.dend BETWEEN #{beginDate} and #{endDate}
    </select>
    <select id="getDealerAAK" resultType="com.qm.ep.rebatetri.domain.dto.DealerAAKDTO">
        SELECT
            field1 dealerCode,
            field10 series,
            sum(CAST( IFNULL( field25, '0' ) AS SIGNED )) nqty,
            #{dim} dim
        FROM
            businessdata
        WHERE
            tablename = '经销商提车统计'
            AND field4 = '零售'
            AND field5 = #{year}
            <if test="months != null and !months.isEmpty">
                and field6 in (<foreach collection="months" item="item" separator=",">#{item}</foreach>)
            </if>
            <if test="dealerCodes != null and  dealerCodes.size>0">
                and field1 in
                <foreach collection="dealerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            field1,
            field10
    </select>

    <select id="getPolicyPublishConfig" resultType="com.qm.ep.rebatetri.domain.dto.PolicyPublishConfigDTO">
        select
            POLICY_ID as policyId,
            DEALER_CODE as dealerCode,
            VISIBLE as visible,
            CALC as calc,
            DTSTAMP as dtstamp,
            ID as id
        from policy_publish_config
        <where>
            <if test="policyId!=null and policyId!= ''">
                and POLICY_ID = #{policyId}
            </if>
            <if test="visible!=null and visible!= ''">
                and VISIBLE = #{visible}
            </if>
            <if test="calc!=null and calc!= ''">
                and CALC = #{calc}
            </if>
        </where>
    </select>
    <select id="getDealerSTD" resultType="com.qm.ep.rebatetri.domain.dto.DealerSTDDTO">
        SELECT
        field1 dealerCode,
        field10 series,
        sum(CAST( IFNULL( field25, '0' ) AS SIGNED )) std,
        #{dim} dim
        FROM
        businessdata
        WHERE
        tablename = '经销商提车统计'
        AND field4 = '批发'
        AND field5 = #{year}
        <if test="months != null and !months.isEmpty">
            and field6 in (<foreach collection="months" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="dealerCodes != null and  dealerCodes.size>0">
        	and field1 in
        	<foreach collection="dealerCodes" item="item" open="(" separator="," close=")">
        		#{item}
        	</foreach>
        </if>
        GROUP BY
        field1,
        field10
    </select>
    <select id="getDealerSTDTask" resultType="com.qm.ep.rebatetri.domain.dto.DealerTaskDTO">
        SELECT
        a.field1 dealerCode,
        a.field8 series,
        sum(a.field9) taskQty,
        #{dim} DIM
        FROM
        businessdata a
        INNER JOIN (
        SELECT
        field3,
        field8,
        MAX( field80 ) maxdate
        FROM
        businessdata a
        WHERE
        <include refid="WhereSQL_STD"/>
        GROUP BY
        field3,field8
        ) b ON a.field3 = b.field3  and a.field8 = b.field8
        AND ifnull( a.field80, '' )= ifnull( b.maxdate, '' )
        WHERE
        <include refid="WhereSQL_STD"/>
        GROUP BY a.field1,a.field8
    </select>
    <select id="isVisible" resultType="java.lang.String">
        SELECT
            b.DEALER_CODE dealerCode
        FROM
            policy a
                INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
        WHERE
         a.canTrial = '1'
            AND a.VFINISHSTATE = '30'
        AND b.VISIBLE = '1'
        AND b.CALC = '1'
    </select>

    <select id="getDealerYearAAKTask" resultType="com.qm.ep.rebatetri.domain.dto.DealerTaskDTO">
        SELECT field1 as dealerCode, field8 as series, aakMap
        FROM (
                 SELECT
                     field1,
                     field8,
                     SUM(a.field9) as aakMap,
                     ROW_NUMBER() OVER (PARTITION BY field8 ORDER BY SUM(a.field9) DESC) AS row_num
                 FROM businessdata a
                 WHERE tablename = '确认销售目标'
                   AND field2 = '经销商季度MAP目标'
                   AND field10 IN ('下达', '区域确认', '经销商确认')
                   AND field3 LIKE CONCAT(#{year}, '%')
                 GROUP BY field1, field8
             ) AS ranked_data
        WHERE row_num = 1;
    </select>

    <select id="selectByVins" resultMap="BusinessDataMap">

        SELECT * FROM businessdata WHERE tablename = '经销商提车统计' and field4 = #{type} and field37 is null
        <if test="vins != null and vins.size > 0">
            and field20 in
            <foreach collection="vins" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getCalcPolicyOnAllDealer" resultType="com.qm.ep.rebatetri.domain.dto.ExecCalcDTO">
        SELECT
            a.ID policyId,
            a.VPOLICYNAME policyName,
            c.id objectId,
            'combination' objectType,
            c.combinationname objectName,
            b.DEALER_CODE dealerCode,
            'trial' calcType,
            s.series
        FROM
            policy a
                INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
                INNER JOIN COMBINATIONMAIN c ON c.policyid = a.id
                INNER JOIN series_quota s
        WHERE
             a.canTrial = '1'
            AND a.VFINISHSTATE = '30'
          AND b.VISIBLE = '1' AND b.CALC = '1'
          AND policyId = #{policyId}
    </select>
    <select id="selectInvoiceByVins" resultType="com.qm.ep.rebatetri.domain.bean.BusinessDataDO">
        SELECT * FROM businessdata WHERE tablename = '终端发票查询' and field9 = '正常' and field29 = '1'
        <if test="vins != null and vins.size > 0">
            and field13 in
            <foreach collection="vins" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by field13
    </select>
    <select id="getDealerInvoice" resultType="com.qm.ep.rebatetri.domain.dto.DealerAAKDTO">
        SELECT
        field27 as dealerCode,
        field22 as series,
        sum(CAST( IFNULL( field29, '0' ) AS SIGNED )) nqty,
        #{dim} dim
        FROM
        businessdata
        WHERE
        tablename = '终端发票查询'
        AND field1 = #{year}
        <if test="months != null and !months.isEmpty">
            and field2 in (<foreach collection="months" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="dealerCodes != null and  dealerCodes.size>0">
            and field27 in
            <foreach collection="dealerCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        field27,
        field22
    </select>

    <delete id="deleteByPolicyId">
        DELETE FROM businessdata_unreal WHERE tablename = '经销商提车统计' and field81=#{policyId}
    </delete>
</mapper>
