package com.qm.ep.rebatetri.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.AccountEntryStatusDO;
import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebatetri.domain.dto.TurnBusinessBottomTableDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.enumerate.StateEnum;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.AccountEntryStatusService;
import com.qm.ep.rebatetri.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebatetri.service.ExecFormalCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execFormalCalcHistory")
@Tag(name = "计算对象计算历史", description = "计算对象计算历史")
@Slf4j
public class ExecFormalCalcHistoryController extends BaseController {


    @Resource
    private ExecFormalCalcHistoryService execCalcHistoryService;

    @Resource
    private ExecFormalCalcResultService execCalcResultService;
    @Resource
    private AccountEntryStatusService accountEntryStatusService;

    @Resource
    private RebateBaseRemote rebateBaseRemote;


    @Operation(summary = "查询计算对象计算历史列表", description = "查询计算对象计算历史列表")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecFormalCalcHistoryDO>> table(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(BootAppUtil.isnotNullOrEmpty(execCalcHistoryDTO.getPolicyId()), ExecFormalCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecFormalCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecFormalCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType());

        QmPage<ExecFormalCalcHistoryDO> page = execCalcHistoryService.table(queryWrapper, execCalcHistoryDTO);
        List<ExecFormalCalcHistoryDO> list = page.getItems();
        list.forEach(item -> {
            String historyId = item.getId();
            LambdaQueryWrapper<ExecFormalCalcResultDO> resultWrapper = new QmQueryWrapper<ExecFormalCalcResultDO>().lambda();
            resultWrapper.eq(ExecFormalCalcResultDO::getHistoryId, historyId);
            long total = execCalcResultService.count(resultWrapper);

            LambdaQueryWrapper<AccountEntryStatusDO> statusWrapper = new QmQueryWrapper<AccountEntryStatusDO>().lambda();
            statusWrapper.eq(AccountEntryStatusDO::getUniqueKey, historyId);
            long count = accountEntryStatusService.count(statusWrapper);

            item.setPassEntryState(CharSequenceUtil.format("已入账{}条，总记录{}条", count, total));
        });

        JsonResultVo<QmPage<ExecFormalCalcHistoryDO>> ret = new JsonResultVo<>();
        ret.setData(page);
        return ret;
    }


    @Operation(summary = "查询计算对象计算字段列表", description = "查询计算对象计算字段列表")
    @PostMapping("/fields")
    public JsonResultVo<List<String>> getFields(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcHistoryDO::getId, execCalcHistoryDTO.getId());

        ExecFormalCalcHistoryDO execCalcHistoryDO = execCalcHistoryService.getOne(lambdaWrapper);
        if(execCalcHistoryDO==null) {
            throw new QmException("未找到计算历史记录！");
        }
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
        ret.setData(sqlStructureVO.getFields());
        return ret;
    }


    @Operation(summary = "删除计算对象计算历史记录", description = "删除计算对象计算历史记录")
    @PostMapping("/deleteList")
    public JsonResultVo<String> deleteList(@RequestBody List<String> deleteIds) {
        if(deleteIds.isEmpty()) {
            throw new QmException("未选择删除记录！");
        }
        execCalcHistoryService.removeByIds(deleteIds);

        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.in(ExecFormalCalcResultDO::getHistoryId, deleteIds);
        execCalcResultService.remove(lambdaWrapper);
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setMsg("删除成功");
        return ret;
    }



    @Operation(summary = "计算结果转业务底表", description = "计算结果转业务底表")
    @PostMapping("/formalTurnBusinessBottomTable")
    public JsonResultVo<String> formalTurnBusinessBottomTable(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) {
        ExecFormalCalcHistoryDO execFormalCalcHistoryDO = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execFormalCalcHistoryDO.setState(StateEnum.CONVERTING);
        execCalcHistoryService.updateById(execFormalCalcHistoryDO);
        turnBusinessBottomTableDTO.setStructure(execFormalCalcHistoryDO.getSqlStructure());
        JsonResultVo<String> reportSqlStructure = rebateBaseRemote.turnBusinessBottomTable(turnBusinessBottomTableDTO);
        ExecFormalCalcHistoryDO execFormalCalcHistory = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execFormalCalcHistory.setState(StateEnum.FINISH);
        execCalcHistoryService.updateById(execFormalCalcHistory);
        return reportSqlStructure;
    }


    @Operation(summary = "获取政策计算历史列表", description = "获取政策计算历史列表")
    @PostMapping("/getExecHistoryList")
    public JsonResultVo<List<ExecFormalCalcHistoryDO>> getExecHistoryList(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<List<ExecFormalCalcHistoryDO>> ret = new JsonResultVo<>();
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecFormalCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecFormalCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType());

        List<ExecFormalCalcHistoryDO> data = execCalcHistoryService.list(queryWrapper);
        ret.setData(data);
        return ret;
    }


    @Operation(summary = "获取政策计算历史", description = "获取政策计算历史")
    @PostMapping("/getExecHistory")
    public JsonResultVo<ExecFormalCalcHistoryDO> getExecHistory(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<ExecFormalCalcHistoryDO> ret = new JsonResultVo<>();
        ExecFormalCalcHistoryDO data = execCalcHistoryService.getById(execCalcHistoryDTO.getId());
        ret.setData(data);
        return ret;
    }
}
