package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.ReportDataDO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ReportDataMapper extends QmBaseMapper<ReportDataDO> {
    /**
     * 动态sql 插入
     * @param map 参数
     * @return 返回
     */
    @Insert("${sql}")
    int insertBySql(Map<String, Object> map);

    /**
     * 动态sql 查询
     * @param map 参数
     * @return 返回
     */
    @Select("${sql}")
    List<Map<String, Object>> selectBySql(Map<String, Object> map);
}
