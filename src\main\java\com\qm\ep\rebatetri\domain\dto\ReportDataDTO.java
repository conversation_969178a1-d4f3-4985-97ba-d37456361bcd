package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Schema(description = "ReportDataDTO对象")
public class ReportDataDTO extends JsonParamDto {



    @Schema(description = "主键")
    private Long id;

    @Schema(description = "报表ID")
    private String reportId;

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "数据表字段")
    private String field1;

    @Schema(description = "数据表字段")
    private String field2;

    @Schema(description = "数据表字段")
    private String field3;

    @Schema(description = "数据表字段")
    private String field4;

    @Schema(description = "数据表字段")
    private String field5;

    @Schema(description = "数据表字段")
    private String field6;

    @Schema(description = "数据表字段")
    private String field7;

    @Schema(description = "数据表字段")
    private String field8;

    @Schema(description = "数据表字段")
    private String field9;

    @Schema(description = "数据表字段")
    private String field10;

    @Schema(description = "数据表字段")
    private String field11;

    @Schema(description = "数据表字段")
    private String field12;

    @Schema(description = "数据表字段")
    private String field13;

    @Schema(description = "数据表字段")
    private String field14;

    @Schema(description = "数据表字段")
    private String field15;

    @Schema(description = "数据表字段")
    private String field16;

    @Schema(description = "数据表字段")
    private String field17;

    @Schema(description = "数据表字段")
    private String field18;

    @Schema(description = "数据表字段")
    private String field19;

    @Schema(description = "数据表字段")
    private String field20;

    @Schema(description = "数据表字段")
    private String field21;

    @Schema(description = "数据表字段")
    private String field22;

    @Schema(description = "数据表字段")
    private String field23;

    @Schema(description = "数据表字段")
    private String field24;

    @Schema(description = "数据表字段")
    private String field25;

    @Schema(description = "数据表字段")
    private String field26;

    @Schema(description = "数据表字段")
    private String field27;

    @Schema(description = "数据表字段")
    private String field28;

    @Schema(description = "数据表字段")
    private String field29;

    @Schema(description = "数据表字段")
    private String field30;

    @Schema(description = "数据表字段")
    private String field31;

    @Schema(description = "数据表字段")
    private String field32;

    @Schema(description = "数据表字段")
    private String field33;

    @Schema(description = "数据表字段")
    private String field34;

    @Schema(description = "数据表字段")
    private String field35;

    @Schema(description = "数据表字段")
    private String field36;

    @Schema(description = "数据表字段")
    private String field37;

    @Schema(description = "数据表字段")
    private String field38;

    @Schema(description = "数据表字段")
    private String field39;

    @Schema(description = "数据表字段")
    private String field40;

    @Schema(description = "数据表字段")
    private String field41;

    @Schema(description = "数据表字段")
    private String field42;

    @Schema(description = "数据表字段")
    private String field43;

    @Schema(description = "数据表字段")
    private String field44;

    @Schema(description = "数据表字段")
    private String field45;

    @Schema(description = "数据表字段")
    private String field46;

    @Schema(description = "数据表字段")
    private String field47;

    @Schema(description = "数据表字段")
    private String field48;

    @Schema(description = "数据表字段")
    private String field49;

    @Schema(description = "数据表字段")
    private String field50;
}

