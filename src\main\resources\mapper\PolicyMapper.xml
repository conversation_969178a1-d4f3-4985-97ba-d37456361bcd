<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.PolicyMapper">
    <select id="inquirePolicyByName" resultType="com.qm.ep.rebatetri.domain.bean.PolicyDO">
        select *
        from policy
        where VPOLICYNAME = #{policyName,jdbcType=VARCHAR}
    </select>

    <select id="inquirePolicyByPolicyId" resultType="com.qm.ep.rebatetri.domain.bean.PolicyDO">
        select *
        from policy
        where id = #{policyId}
    </select>

    <select id="inquirePolicyByHistoryId" resultType="com.qm.ep.rebatetri.domain.bean.PolicyDO">
        select b.*
        from exec_formal_calc_history a
                 INNER JOIN policy b
                            ON a.policyId = b.id
        where a.id = #{historyId}
    </select>
    <select id="getCalcPolicyByType" resultType="com.qm.ep.rebatetri.domain.dto.ExecCalcDTO">
        SELECT a.ID policyId,
        a.VPOLICYNAME policyName,
        c.id objectId,
        'combination' objectType,
        c.combinationname objectName,
        b.DEALER_CODE dealerCode,
        'trial' calcType
        FROM policy a
        INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
        INNER JOIN COMBINATIONMAIN c ON c.policyid = a.id
        LEFT JOIN unreal_policy_record pr on a.id = pr.policyid COLLATE utf8mb4_0900_ai_ci
        WHERE a.canTrial = '1'
        AND a.DEND >= CONCAT(CURRENT_DATE,' 00:00:00')  -- 政策执行结束日期时，起始时间需大于等于当前日
        AND a.id not IN(SELECT DISTINCT policyId FROM exec_formal_calc_history WHERE finEntryState = '53')  --  财务兑付完毕的政策不参与试算,此政策不显示；
        AND a.VSMTTYPE not in ('2','4') -- ‘追溯’或‘预返扣回’的政策不参与试算
        AND a.VFINISHSTATE = '30'
        AND b.VISIBLE = '1'
        AND b.CALC = '1'
        AND pr.data_status in ('0','1','2')
        <if test="type != null and type != ''">
            and a.trail_type = #{type}
        </if>
        order by a.id asc
    </select>
    <select id="getDistinctPolicyIdAscByType" resultType="java.lang.String">
        SELECT distinct a.ID
        FROM policy a
                 INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
                 INNER JOIN COMBINATIONMAIN c ON c.policyid = a.id
        WHERE a.canTrial = '1'
          AND a.VFINISHSTATE = '30'
          AND b.VISIBLE = '1'
          AND b.CALC = '1'
          AND a.dbegin BETWEEN #{beginDate} and #{endDate}
          AND a.dend BETWEEN #{beginDate} and #{endDate}
          and a.trail_type = #{type}
          and b.DEALER_CODE = #{dealerCode}
        order by a.id asc
    </select>

    <select id="selectConfigPoliciesByCondition" resultType="com.qm.ep.rebatetri.domain.dto.RebateQueryResponse">
        SELECT
        a.id as policyId,
        a.DBEGIN as startTime,
        a.DEND as endTime,
        a.id,
        a.VPOLICYNAME as policyName,
        a.VPOLICYCODE as policyCode
        FROM
        policy a
        INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
        WHERE
        a.DEND >= CONCAT(CURRENT_DATE,' 00:00:00')  -- 政策执行结束日期时，起始时间需大于等于当前日
        AND a.id not IN(SELECT DISTINCT policyId FROM exec_formal_calc_history WHERE finEntryState = '53')  --  财务兑付完毕的政策不参与试算,此政策不显示；
        AND a.VSMTTYPE not in ('2','4') -- ‘追溯’或‘预返扣回’的政策不参与试算
        AND a.canTrial = '1'
        AND a.VFINISHSTATE = '30'
        AND b.VISIBLE = '1'
        AND b.CALC = '1'
        <if test="param2.dealerCode != null and param2.dealerCode != ''">
        	AND b.DEALER_CODE = #{param2.dealerCode}
        </if>
        <if test="param2.reimburseStatus!=null and param2.reimburseStatus != ''">
            And a.trail_type = #{param2.reimburseStatus}
        </if>
        <if test="request.policyCode!=null and request.policyCode != ''">
            and a.VPOLICYCODE like CONCAT('%',#{request.policyCode},'%')
        </if>
        <if test="request.policyName!=null and request.policyName != ''">
            and a.vpolicyname like CONCAT('%',#{request.policyName},'%')
        </if>
        <if test="request.startTime!=null and request.startTime != ''">
            and a.dend >= CONCAT(#{request.startTime}, ' 00:00:00')
        </if>
        <if test="request.endTime!=null and request.endTime != ''">
            and a.dend &lt;= CONCAT(#{request.endTime}, ' 23:59:59')
        </if>
        <if test="request.dealerCode != null and request.dealerCode != ''">
            AND b.dealer_code =#{request.dealerCode}
        </if>
        Group by a.id
    </select>


    <select id="queryPolicyList" resultType="com.qm.ep.rebatetri.domain.dto.RebateQueryResponse">
        SELECT
        a.id as policyId,
        a.DBEGIN as startTime,
        a.DEND as endTime,
        a.id,
        a.VPOLICYNAME as policyName,
        a.VPOLICYCODE as policyCode
        FROM
        policy a
        INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
        WHERE
        a.DEND >= CONCAT(CURRENT_DATE,' 00:00:00')
        AND a.id not IN(SELECT DISTINCT policyId FROM exec_formal_calc_history WHERE finEntryState = '53')
        AND a.VSMTTYPE not in ('2','4')
        AND a.canTrial = '1'
        AND a.VFINISHSTATE = '30'
        AND b.VISIBLE = '1'
        AND b.CALC = '1'
        <if test="reimburseStatus!=null and reimburseStatus != ''">
            And a.trail_type = #{reimburseStatus}
        </if>
        <if test="policyCode!=null and policyCode != ''">
            and a.VPOLICYCODE like CONCAT('%',#{policyCode},'%')
        </if>
        <if test="policyName!=null and policyName != ''">
            and a.vpolicyname like CONCAT('%',#{policyName},'%')
        </if>
        <if test="startTime!=null and startTime != ''">
            and a.dend >= CONCAT(#{startTime}, ' 00:00:00')
        </if>
        <if test="endTime!=null and endTime != ''">
            and a.dend &lt;= CONCAT(#{endTime}, ' 23:59:59')
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            AND b.dealer_code =#{dealerCode}
        </if>
        Group by a.id
        limit 10
    </select>

    <select id="queryPolicyByRecordStatus" resultType="com.qm.ep.rebatetri.domain.dto.RebateQueryResponse">
        SELECT
        a.id as policyId,
        a.DBEGIN as startTime,
        a.DEND as endTime,
        a.id,
        a.VPOLICYNAME as policyName,
        a.VPOLICYCODE as policyCode
        FROM
        policy a
        INNER JOIN policy_publish_config b ON a.id = b.POLICY_ID
        LEFT JOIN unreal_policy_record pr on a.id = pr.policyid COLLATE utf8mb4_0900_ai_ci
        INNER JOIN (
        SELECT policyid, dealerCode, MAX(DTSTAMP) AS latest_time
        FROM trial_unreal_result
        GROUP BY policyid, dealerCode
        ) latest ON a.id = latest.policyid AND b.DEALER_CODE = latest.dealerCode
        WHERE
        a.DEND >= CONCAT(CURRENT_DATE,' 00:00:00')  -- 政策执行结束日期时，起始时间需大于等于当前日
        AND a.id not IN(SELECT DISTINCT policyId FROM exec_formal_calc_history WHERE finEntryState = '53')  --  财务兑付完毕的政策不参与试算,此政策不显示；
        AND a.VSMTTYPE not in ('2','4') -- ‘追溯’或‘预返扣回’的政策不参与试算
        AND a.canTrial = '1'
        AND a.VFINISHSTATE = '30'
        AND b.VISIBLE = '1'
        AND b.CALC = '1'
        AND pr.data_status in ('0','1','2')
        <if test="param2.dealerCode != null and param2.dealerCode != ''">
            AND b.DEALER_CODE = #{param2.dealerCode}
        </if>
        <if test="param2.reimburseStatus!=null and param2.reimburseStatus != ''">
            And a.trail_type = #{param2.reimburseStatus}
        </if>
        <if test="request.policyCode!=null and request.policyCode != ''">
            and a.VPOLICYCODE like CONCAT('%',#{request.policyCode},'%')
        </if>
        <if test="request.policyName!=null and request.policyName != ''">
            and a.vpolicyname like CONCAT('%',#{request.policyName},'%')
        </if>
        <if test="request.startTime!=null and request.startTime != ''">
            and a.dend >= CONCAT(#{request.startTime}, ' 00:00:00')
        </if>
        <if test="request.endTime!=null and request.endTime != ''">
            and a.dend &lt;= CONCAT(#{request.endTime}, ' 23:59:59')
        </if>
        <if test="request.dealerCode != null and request.dealerCode != ''">
            AND b.dealer_code =#{request.dealerCode}
        </if>
        Group by a.id
    </select>
</mapper>
