package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.constant.Constants;
import com.qm.ep.rebatetri.domain.bean.*;
import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.ep.rebatetri.domain.vo.RebateCalDetailVO;
import com.qm.ep.rebatetri.mapper.*;
import com.qm.ep.rebatetri.service.*;
import com.qm.ep.rebatetri.utils.CalFourCalculator;
import com.qm.ep.rebatetri.utils.Calculator;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.dynamic.constant.DataSourceType;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.qm.ep.rebatetri.constant.Constants.ALL_SERIES;

/**
 * 试算器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class TrialCalcCoreServiceImpl implements TrialCalcCoreService {
    @Resource
    private PolicyMapper policyMapper;
    @Resource
    private TrailBusinessCacheService trailBusinessCacheService;
    @Resource
    private TrialCalcResultSummaryService trailUnrealResultService;

    @Value("${rebate.trial.mapMax:0}")
    private int mapMax;

    @Value("${mock.dealer.code:}")
    private String mockDealerCode;

    @Resource
    private TrailRecordMapper trailRecordMapper;
    @Resource
    private TrialCalcResultSummaryDetailService trialCalcResultSummaryDetailService;


    @Resource
    private TrialUnrealResultMapper trialUnrealResultMapper;


    @Value("${rebate.trial.extend:1.5}")
    private double trialExtend;

    @Value("${rebate.trial.oneMoreQty:1}")
    private int moreQty;

    @Resource
    private TrialCalcDataService trialCalcDataService;
    @Resource
    private ExecCalcService execCalcService;
    @Resource
    private TrialCalcResultSummaryService trialCalcResultSummaryService;
    @Resource
    private TrialCalcResultSummaryMapper trialCalcResultSummaryMapper;
    @Resource
    private TrialUnrealResultService trialUnrealResultService;
    @Resource
    private TrialUnrealLogService trialUnrealLogService;


    @Resource
    private BusinessDataMapper businessDataMapper;

    @Resource
    private TrialCalcCoreService trialCalcCoreService;


    /**
     * 获取最优点
     *
     * @param dim      昏暗
     * @param type     类型
     * @param policyId 策略id
     */
    @Override
    public void calBestPoint(String dim, String type, String policyId) {

        // 全部可参与计算的政策(policyId+dealerCode唯一)
        List<ExecCalcDTO> execList = trialCalcDataService.getCalcPolicyByType(type);
        log.info("类型为{},可参与计算的政策为:{}",type, execList.stream().map(ExecCalcDTO::getPolicyName).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(execList)) {
            log.error("未查询到可参与计算的政策,type:{}", type);
            return;
        }
        if(StringUtils.isNotBlank(policyId)){
            execList.removeIf(item -> !item.getPolicyId().equals(policyId));
        }
        // 按照经销商进行分组，value为该经销商的可参与计算的政策
        Map<String, List<ExecCalcDTO>> dealerCodeWithExecCalsMap = execList.stream().collect(Collectors.groupingBy(ExecCalcDTO::getDealerCode));

        dealerCodeWithExecCalsMap.forEach((dealerCode, execCals) -> {

            // 这里考虑到后续有混合政策计算的需求，所以使用了List套List
            List<List<ExecCalcDTO>> calExecCals = new ArrayList<>();
            for (ExecCalcDTO execCalcDTO : execCals) {
                List<ExecCalcDTO> temp = new ArrayList<>();
                temp.add(execCalcDTO);
                calExecCals.add(temp);
            }
            // 混合政策试算对象组装，如果需要请打开
            // this.mixPolicy(execCals, calExecCals);
            log.info("类型是{}当前经销商{}参与可用的计算对象大小为:{},内容为{}", type, dealerCode, calExecCals.size(), calExecCals);
            // 针对每个经销商下政策组合进行计算
            for (List<ExecCalcDTO> calExecCal : calExecCals) {
                trialCalcCoreService.trialCalByDealerCode(dim, dealerCode, calExecCal);
            }
            try {
                // 休眠0.1秒，防止不同经销商下计算数据太多导致线程池溢出
                Thread.sleep(100);
            } catch (InterruptedException e) {
                throw new QmException(e);
            }
        });
    }

    /**
     * 混合政策，试算对象幂集计算
     *
     * @param execCals    高管cals
     * @param calExecCals cal执行官
     */
    private void mixPolicy(List<ExecCalcDTO> execCals, List<List<ExecCalcDTO>> calExecCals) {
        int n = execCals.size();

        if (n == 0) {
            return;
        }


        for (ExecCalcDTO execCalcDTO : execCals) {
            List<ExecCalcDTO> temp = new ArrayList<>();
            temp.add(execCalcDTO);
            calExecCals.add(temp);
        }


        for (int r = 2; r <= n; r++) {
            combine(execCals, r, 0, new ArrayList<>(), calExecCals);
        }
    }

    private void combine(List<ExecCalcDTO> execCals, int r, int start, List<ExecCalcDTO> current, List<List<ExecCalcDTO>> result) {

        if (current.size() == r) {
            result.add(new ArrayList<>(current));
            return;
        }


        for (int i = start; i < execCals.size(); i++) {
            current.add(execCals.get(i));
            combine(execCals, r, i + 1, current, result);
            current.remove(current.size() - 1);
        }
    }


    /**
     * 经销商代码试用版
     *
     * @param dim        昏暗
     * @param dealerCode 代理商代码
     * @param calExecCal 经销商下可参与计算的政策计算对象，eg:["政策A对象","政策B对象"]
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void trialCalByDealerCode(String dim, String dealerCode, List<ExecCalcDTO> calExecCal) {
        String manual = "0";
        // 按照政策id进行正序排序，方便混合政策查询
        List<ExecCalcDTO> sortedList = calExecCal.stream()
                .sorted(Comparator.comparing(ExecCalcDTO::getPolicyId,
                        Comparator.nullsLast(Comparator.comparingLong(Long::parseLong))))
                .collect(Collectors.toList());
        // 获取当前计算对象的政策id，多政策进行逗号拼接
        List<String> policyIdsAsc = sortedList.stream().map(ExecCalcDTO::getPolicyId).distinct().sorted(Comparator.comparingLong(Long::parseLong)).collect(Collectors.toList());
        String policyIdWithComma = String.join(",", policyIdsAsc);

        // 获取经销商在单个或多个政策下的计奖数量与返利（含全系）
        List<TrialCalcResultSummaryDO> aakRbtList = trialCalcResultSummaryMapper.getSeriesRealRebate(dealerCode, policyIdsAsc);
        Map<String, TrialCalcResultSummaryDO> seriesWithSummaryMap = aakRbtList.stream()
                .collect(Collectors.toMap(TrialCalcResultSummaryDO::getSeries, Function.identity(), (original, duplicate) -> original));

        List<String> seriesList = aakRbtList.stream()
                .map(TrialCalcResultSummaryDO::getSeries).distinct()
                // TODO 全系暂时不计算,再补充全系计算逻辑
                // .filter(series -> ALL_SERIES.equals(series))
                // .filter(series -> "H9".equals(series))
                .toList();
        // 获取虚拟返利数据
        Map<String, Double> dealerSeriesNqtyWithUnrealRebateMap = trialCalcResultSummaryService.getAllUnrealRebateAmtSumByPolicyIds(dealerCode, policyIdsAsc);
        // 开始针对每个车系进行返利最优点计算
        for (String series : seriesList) {
            // 预处理计算对象
            sortedList.forEach(item -> {
                item.setDim(dim);
                item.setSeries(series);
                item.setDealerCode(dealerCode);
                item.setVr(1);
            });

            int aakOrStdAim = trailBusinessCacheService.getMaxAakStdMap(policyIdsAsc, series);
            DealerTaskDTO dealerTaskDTO = new DealerTaskDTO();
            if (aakOrStdAim == 0) {
                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "经销商该车系Map为空或者是0,给予默认值" + mapMax, policyIdWithComma);
                // 没有map就给个默认值
                dealerTaskDTO.setTaskQty(mapMax);
            } else {
                // 仍然对map进行扩容
                int finalMap = (int) (trialExtend * aakOrStdAim);
                if(ALL_SERIES.equals(series) ){
                    finalMap= 2 *finalMap;
                }

                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "经销商该车系Map为" + aakOrStdAim + "扩容后为：" + finalMap, policyIdWithComma);
                dealerTaskDTO.setTaskQty(finalMap);
            }
            // 经销商该车系目标Map
            int taskQty = dealerTaskDTO.getTaskQty();
            // aak起始点返利
            TrialCalcResultSummaryDO firstPoint = seriesWithSummaryMap.get(series);
            if (firstPoint == null) {
                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "经销商该车系实际aak和返利为空", policyIdWithComma);
                continue;
            }
            // 经销商该车系的计奖数量
            int nQty = firstPoint.getNqty();
            int maxQty = taskQty;
            if (taskQty < nQty) {
                maxQty = (int) (trialExtend * nQty);
                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "经销商该车系目标Map小于实际aak,给予默认值" + maxQty, policyIdWithComma);
            }
            log.info("经销商:{},车系:{},最优点起始点为:{},终点为{}", dealerCode, series, nQty, maxQty);

            // 该经销商在政策下的实际总返利
            double aakAmt = firstPoint.getNamt();
            double averageAmt = 0;
            if (nQty != 0) {
                averageAmt = NumberUtil.div(aakAmt, nQty, 0);
            }

            // 起始点
            HalfCalcDTO first = new HalfCalcDTO();
            first.setQty(nQty);
            first.setAmt(averageAmt);

            // TODO aak已达Map仍然计算切入点（150%）
            if (nQty >= maxQty) {
                log.info("nQty>=maxQty,模拟再计算一次");
                String msg = StrUtil.format("dealerCode:{},series:{},aak:{},扩容的map:{}。aak已经超过扩容后的Map，停止计算", dealerCode, series, nQty, maxQty);
                log.error(msg);
                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, msg, policyIdWithComma);
                this.moreOneUnrealTrail(sortedList, dealerCode, dim, series, first, aakAmt, manual);
                continue;
            }

            // 终止点
            HalfCalcDTO last = this.getLastHalfCalcDTO(dealerCode, sortedList, series, maxQty);
            log.info("起始点为:{},终止点为:{}", JSONUtil.toJsonPrettyStr(first), JSONUtil.toJsonPrettyStr(last));
            if (NumberUtil.equals(last.getAmt(), first.getAmt())) {
                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "终止点与起始点平均返利相同，为了展示模拟再计算一次，起始点平均返利为：" + first.getAmt() + "，终止点平均返利为：" + last.getAmt(), policyIdWithComma);
                this.moreOneUnrealTrail(sortedList, dealerCode, dim, series, first, aakAmt, manual);
                continue;
            }

            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            this.calFourPoint(sortedList, first, last, dealerSeriesNqtyWithUnrealRebateMap, manual);
            stopWatch.stop();
            log.info("政策:{},经销商:{},车系:{},计算耗时:{}", policyIdWithComma, dealerCode, series, stopWatch.prettyPrint());
        }
    }

    @Override
    public KanbanDTO manualCalc(RebateManualCalRequest request) {
        String series = request.getSeries();
        int saleCount = Integer.parseInt(request.getSaleCount());
        // 政策id从小到大排序适配混合政策查询
        List<String> policyIds = request.getPolicyIds().stream().sorted(Comparator.comparingLong(Long::parseLong)).sorted().toList();
        String policyIdWithComma = String.join(",", policyIds);
        // 模拟构造试算对象
        List<ExecCalcDTO> execCalcDTOList = new ArrayList<>();
        String dealerCode = request.getDealerCode();
        if (StringUtils.isNotBlank(mockDealerCode)) {
            dealerCode = mockDealerCode;
        }
        for (String policyId : policyIds) {
            ExecCalcDTO execCalcDTO = new ExecCalcDTO();
            execCalcDTO.setDealerCode(dealerCode);
            execCalcDTO.setDim("Y");
            execCalcDTO.setSeries(series);
            execCalcDTO.setPolicyId(policyId);
            execCalcDTOList.add(execCalcDTO);
        }
        List<TrialCalcResultSummaryDO> realRebateList = trialCalcResultSummaryMapper.getSeriesRealRebate(dealerCode, policyIds);
        Optional<TrialCalcResultSummaryDO> firstOptional = realRebateList.stream()
                .filter(ele -> ele.getSeries().equals(series))
                .findFirst();
        TrialCalcResultSummaryDO trialCalcResultSummaryDO = firstOptional.orElse(null);
        if (trialCalcResultSummaryDO == null) {
            throw new IllegalArgumentException("未查询到该车系的计奖数量和返利");
        }
        Double namt = trialCalcResultSummaryDO.getNamt();
        Integer nqty = trialCalcResultSummaryDO.getNqty();
        Double avgAmt = namt / nqty;
        HalfCalcDTO first = HalfCalcDTO.build(null, nqty, avgAmt);
        // 虚拟平均返利
        Double unrealAvgRebate = 0.0;
        HalfCalcDTO last = HalfCalcDTO.build(null, saleCount, unrealAvgRebate);
        if(ALL_SERIES.equals(series)){
            CalFourCalculator calFourCalculator = this.getCalFourCalculator(dealerCode, policyIds);
            calFourCalculator.reConstructFlyMap(first,last);
            last.setCalculator(calFourCalculator);
            // 获取对应的车系对应的计奖数量位置
            Map<String, Integer> seriesOldMap = calFourCalculator.getSeriesOldMap();

            BigDecimal totalQty = BigDecimal.ZERO;
            BigDecimal totalAmt = BigDecimal.ZERO;
            for (String seriesKey : seriesOldMap.keySet()) {
                Integer nQty = seriesOldMap.get(seriesKey);
                String key = dealerCode + "-" + seriesKey + "-" + nQty;
                Integer count = seriesOldMap.get(seriesKey);
                Map<String, Double> dealerSeriesNqtyWithUnrealRebateMap = trialCalcResultSummaryService.getAllUnrealRebateAmtSumByPolicyIds(dealerCode, policyIds);
                Double unrealAvgAmt = dealerSeriesNqtyWithUnrealRebateMap.get(key);

                if (unrealAvgAmt != null) {
                    totalAmt = totalAmt.add(new BigDecimal(unrealAvgAmt).multiply(BigDecimal.valueOf(count)));
                    totalQty = totalQty.add(BigDecimal.valueOf(count));
                }
            }
            // 全车系的各个车系对应的数量下的平均返利
            unrealAvgRebate=NumberUtil.div(totalAmt.doubleValue(),totalQty.doubleValue(),0);
            if(unrealAvgRebate<first.getAmt()){
                trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, "Y", series, "手动计算的终止点平均返利小于起始点平均返利，默认终止节点返利=初始节点返利。终止点平均返利为："+unrealAvgRebate+"，起始点平均返利为："+first.getAmt(), policyIdWithComma);
                unrealAvgRebate=first.getAmt();
            }
        }else{
            unrealAvgRebate = trailUnrealResultService.getUnrealRebateAmtSumByQty(saleCount, dealerCode, policyIds, series);
        }
        last.setAmt(unrealAvgRebate);

        // 额外再次计算N台车
        this.calManualPoint(execCalcDTOList, first, last);

        RebateCalQueryRequest queryRequest = new RebateCalQueryRequest();
        queryRequest.setPolicyIds(policyIds);
        queryRequest.setDealerCode(dealerCode);
        queryRequest.setSeries(Collections.singletonList(series));
        queryRequest.setReimburseStatus(policyMapper.inquirePolicyByPolicyId(policyIds.get(0)).getTrailType());
        return kanban(queryRequest).get(0);
    }

    private void calManualPoint(List<ExecCalcDTO> execCalcDTOList, HalfCalcDTO first, HalfCalcDTO finalPoint) {
        String dealerCode = execCalcDTOList.get(0).getDealerCode();
        String dim = execCalcDTOList.get(0).getDim();
        String series = execCalcDTOList.get(0).getSeries();

        // 汇总处理政策id，方便后续查询
        String policyIdComma = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).collect(Collectors.joining(","));
        List<String> policyIds = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).toList();

        String halfId = "half-optimal-" + IdUtil.simpleUUID();

        // 记录结果
        TrialUnrealResultDO trialUnrealResultDO = new TrialUnrealResultDO();
        trialUnrealResultDO.setDealerCode(dealerCode);
        trialUnrealResultDO.setDim(dim);
        trialUnrealResultDO.setSeries(series);
        trialUnrealResultDO.setBatchId(halfId);
        trialUnrealResultDO.setAakAmt(first.getAmt());
        trialUnrealResultDO.setAakQty(first.getQty());
        trialUnrealResultDO.setTotalAmt(finalPoint.getAmt() * finalPoint.getQty());
        trialUnrealResultDO.setTotalQty(finalPoint.getQty());
        trialUnrealResultDO.setBestAmt(finalPoint.getAmt());
        trialUnrealResultDO.setPolicyId(policyIdComma);
        trialUnrealResultDO.setManual("1");
        trialUnrealResultService.save(trialUnrealResultDO);
        // 重新获取最优点所需的虚拟车数量(这里是为了展示详情时候所需要)
        this.doCopyRebateCarAndCreateUnrealRebateCar(policyIds, dealerCode, series, halfId, first, finalPoint);
    }

    private void calFourPoint(List<ExecCalcDTO> execCalcDTOList, HalfCalcDTO first, HalfCalcDTO last,
                              Map<String, Double> dealerSeriesNqtyWithUnrealRebateMap, String manual) {
        int validCount = 0;
        String dealerCode = execCalcDTOList.get(0).getDealerCode();
        String dim = execCalcDTOList.get(0).getDim();
        String series = execCalcDTOList.get(0).getSeries();

        // 汇总处理政策id，方便后续查询
        String policyIdComma = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).collect(Collectors.joining(","));
        List<String> policyIds = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).toList();

        // 获取真实返利数据
        CalFourCalculator calFourCalculator = this.getCalFourCalculator(dealerCode, policyIds);
        Double prevAmt = first.getAmt();
        Double tempBestAmt = first.getAmt();
        for (int cursorQty = first.getQty() + 1; cursorQty <= last.getQty(); cursorQty++) {
            String key;
            Double amt;
            if (ALL_SERIES.equals(series)) {
                //  按照各个车系进行约分然后确定按照哪个车系来计算
                //  获取哪个车系
                String seriesOfKey = calFourCalculator.getWhichSeries(cursorQty - first.getQty());
                // 获取对应的车系对应的计奖数量位置
                Map<String, Integer> seriesOldMap = calFourCalculator.getSeriesOldMap();

                BigDecimal totalQty = BigDecimal.ZERO;
                BigDecimal totalAmt = BigDecimal.ZERO;

                for (String seriesKey : seriesOldMap.keySet()) {
                    Integer nQty = seriesOldMap.get(seriesKey);
                    key = dealerCode + "-" + seriesKey + "-" + nQty;
                    Integer count = seriesOldMap.get(seriesKey);
                    Double avgAmt = dealerSeriesNqtyWithUnrealRebateMap.get(key);

                    if (avgAmt != null) {
                        totalAmt = totalAmt.add(new BigDecimal(avgAmt).multiply(BigDecimal.valueOf(count)));
                        totalQty = totalQty.add(BigDecimal.valueOf(count));
                    }
                }
                if(totalQty.compareTo(BigDecimal.ZERO)==0) {
                    amt = 0d;
                } else {
                    amt=NumberUtil.div(totalAmt.doubleValue(),totalQty.doubleValue(),0);
                }

            } else {
                key = dealerCode + "-" + series + "-" + cursorQty;
                amt = dealerSeriesNqtyWithUnrealRebateMap.get(key);
            }
            // 如果在游标行走的过程中，出现了某个车系的虚拟返利找不到，则跳过（有可能因为初始化时候某些节点失败导致的）
            if (amt == null) {
                // trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "当前节点" + cursorQty + "游标行走的过程中，出现了某个车系的虚拟返利找不到，则跳过（有可能因为初始化时候某些节点失败导致的）", policyIdComma);
                continue;
            }


            if (amt < prevAmt) {
                log.info("当前节点的返利小于上一个节点的返利，终止计算");
                // trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "当前节点" + cursorQty + "的返利小于上一个节点的返利，终止计算", policyIdComma);
                // break;
            }
            if (amt > prevAmt) {
                if(ALL_SERIES.equals(series) && amt-tempBestAmt<200){
                    continue;
                }
                String halfId = "half-optimal-" + IdUtil.simpleUUID();

                HalfCalcDTO currentPoint = HalfCalcDTO.build(halfId, cursorQty, amt);
                currentPoint.setCalculator(calFourCalculator);
                // 记录结果
                TrialUnrealResultDO trialUnrealResultDO = new TrialUnrealResultDO();
                trialUnrealResultDO.setDealerCode(dealerCode);
                trialUnrealResultDO.setDim(dim);
                trialUnrealResultDO.setSeries(series);
                trialUnrealResultDO.setBatchId(halfId);
                trialUnrealResultDO.setAakAmt(first.getAmt());
                trialUnrealResultDO.setAakQty(first.getQty());
                trialUnrealResultDO.setTotalAmt(currentPoint.getAmt() * currentPoint.getQty());
                trialUnrealResultDO.setTotalQty(currentPoint.getQty());
                trialUnrealResultDO.setBestAmt(currentPoint.getAmt());
                trialUnrealResultDO.setPolicyId(policyIdComma);
                trialUnrealResultDO.setManual(manual);
                trialUnrealResultService.save(trialUnrealResultDO);
                tempBestAmt=currentPoint.getAmt();
                validCount++;
                // 重新获取最优点所需的虚拟车数量(这里是为了展示详情时候所需要)
                this.doCopyRebateCarAndCreateUnrealRebateCar(policyIds, dealerCode, series, halfId, first, currentPoint);
                prevAmt = amt;
            }


            if (validCount == 4) {
                break;
            }
        }


        // 不存在阶梯点,就额外构造一台车
        if (validCount == 0) {
            trialUnrealLogService.saveCommonLogNoBatchId(dealerCode, dim, series, "未找到有效的最优点validCount == 0", policyIdComma);
            this.moreOneUnrealTrail(execCalcDTOList, dealerCode, dim, series, first, first.getAmt()*first.getQty(), manual);
        }
    }

    /**
     * 获取政策ids维度下的计算类（默认对应真实返利节点对应的车系-计奖数量）
     *
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @return {@link CalFourCalculator }
     */
    @NotNull
    private CalFourCalculator getCalFourCalculator(String dealerCode, List<String> policyIds) {
        List<TrialCalcResultSummaryDO> realRebateList = trialCalcResultSummaryMapper.getSeriesRealRebate(dealerCode, policyIds);
        Map<String, Integer> seriesCounts = realRebateList.stream().filter(ele -> !ele.getSeries().equals(ALL_SERIES)).collect(Collectors.toMap(TrialCalcResultSummaryDO::getSeries, TrialCalcResultSummaryDO::getNqty, (original, duplicate) -> original));
        return new CalFourCalculator(seriesCounts);
    }

    @NotNull
    private CalFourCalculator getManualCalculator(String dealerCode, List<String> policyIds) {
        List<TrialCalcResultSummaryDO> realRebateList = trialCalcResultSummaryMapper.getSeriesRealRebate(dealerCode, policyIds);
        Map<String, Integer> seriesCounts = realRebateList.stream().filter(ele -> !ele.getSeries().equals(ALL_SERIES)).collect(Collectors.toMap(TrialCalcResultSummaryDO::getSeries, TrialCalcResultSummaryDO::getNqty, (original, duplicate) -> original));
        return new CalFourCalculator(seriesCounts);
    }

    @NotNull
    private CalFourCalculator getOneMoreCalculator(String dealerCode, List<String> policyIds) {
        List<TrialCalcResultSummaryDO> realRebateList = trialCalcResultSummaryMapper.getSeriesRealRebate(dealerCode, policyIds);
        Map<String, Integer> seriesCounts = realRebateList.stream().filter(ele -> !ele.getSeries().equals(ALL_SERIES)).collect(Collectors.toMap(TrialCalcResultSummaryDO::getSeries, TrialCalcResultSummaryDO::getNqty, (original, duplicate) -> original));
        return new CalFourCalculator(seriesCounts);
    }


    /**
     * 更多不真实足迹
     *
     * @param execCalcDTOList exec计算数据列表
     * @param dealerCode      经销商代码
     * @param dim             昏暗
     * @param series          系列
     * @param left            左节点
     * @param aakAmt          当前所处节点总返利
     * @param manual          手册
     */
    private void moreOneUnrealTrail(List<ExecCalcDTO> execCalcDTOList, String dealerCode, String dim, String series,
                                    HalfCalcDTO left, double aakAmt, String manual) {

        Double leftAvgAmt = left.getAmt();
        int leftQty = left.getQty();
        if (new BigDecimal(leftAvgAmt).compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        log.info("开始多计算一次，当前节点为:{}", JSONUtil.toJsonPrettyStr(left));
        String halfId = "half-optimal-" + IdUtil.simpleUUID();
        String batchId = execCalcDTOList.get(0).getBatchId();


        String policyIdComma = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).collect(Collectors.joining(","));
        List<String> policyIds = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).collect(Collectors.toList());

        int oneMoreQty = leftQty + moreQty;
        // 返利金额加上实际具备返利的vin的单台返利
        Double sum = 0.00;
        for (String policyId : policyIds) {
            Double temp = trialCalcResultSummaryMapper.selectAvgRebateByPolicyId(policyId, dealerCode, series);
            sum += temp;
        }

        double oneMoreAmt = aakAmt + moreQty * (sum);
        HalfCalcDTO bestOne = HalfCalcDTO.buildHalf(batchId, oneMoreQty, oneMoreAmt);
        bestOne.setCalculator(this.getOneMoreCalculator(dealerCode, policyIds));
        log.info("经销商:{},车系:{},开始多计算一次，当前节点为:{},计算结果为:{}", dealerCode, series, JSONUtil.toJsonPrettyStr(left), JSONUtil.toJsonPrettyStr(bestOne));
        // 记录结果
        TrialUnrealResultDO trialUnrealResultDO = new TrialUnrealResultDO();
        trialUnrealResultDO.setDealerCode(dealerCode);
        trialUnrealResultDO.setDim(dim);
        trialUnrealResultDO.setSeries(series);
        trialUnrealResultDO.setBatchId(halfId);
        trialUnrealResultDO.setAakAmt(leftAvgAmt);
        trialUnrealResultDO.setAakQty(leftQty);
        //
        trialUnrealResultDO.setTotalAmt(oneMoreAmt);
        trialUnrealResultDO.setTotalQty(oneMoreQty);
        //
        trialUnrealResultDO.setBestAmt(NumberUtil.div(oneMoreAmt, oneMoreQty, 0));
        trialUnrealResultDO.setPolicyId(policyIdComma);
        trialUnrealResultDO.setManual(manual);
        trialUnrealResultService.save(trialUnrealResultDO);
        // 重新获取最优点所需的虚拟车数量
        this.doCopyRebateCarAndCreateUnrealRebateCar(policyIds, dealerCode, series, halfId, left, bestOne);
    }

    /**
     * 获取最大销量点的二分对象
     */
    private HalfCalcDTO getLastHalfCalcDTO(String dealerCode, List<ExecCalcDTO> execCalcDTOList, String series, int taskQty) {

        String batchId = "get-last-" + IdUtil.simpleUUID();
        List<String> policyIds = execCalcDTOList.stream().map(ExecCalcDTO::getPolicyId).collect(Collectors.toList());
        // 终止点的平均返利
        Double amt = trialCalcResultSummaryMapper.getUnrealRebateAmtSumByQty(taskQty, dealerCode, policyIds, series);
        // 如果Map点超过了初始化返利的数据，获取不到就会为0，这时候默认取最大的初始化返利的数据
        if (amt == 0.0) {
            amt = trialCalcResultSummaryMapper.getMaxInitRebateByPolicyIds(dealerCode, policyIds, series);
        }
        return HalfCalcDTO.build(batchId, taskQty, amt);

    }


    private void doCopyRebateCarAndCreateUnrealRebateCar(List<String> policyIds, String dealerCode, String series, String halfId, HalfCalcDTO firstPoint, HalfCalcDTO bestPoint) {

        if (ALL_SERIES.equals(series)) {
            Calculator calFourCalculator = bestPoint.getCalculator();
            // 最优点时各个车系的数量占比情况
            Map<String, Integer> seriesOldMap = calFourCalculator.getSeriesOldMap();

            List<TrialCalcResultSummaryDetailPO> allDetails = new ArrayList<>();
            for (String policyId : policyIds) {
                // 从虚拟返利中获取车系对应数量的返利
                Map<String, Double> dealerSeriesNqtyWithUnrealRebateMap = trialCalcResultSummaryService.getAllUnrealRebateAmtSumByPolicyIds(dealerCode, Collections.singletonList(policyId));

                PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
                // 获取所有车系的真实返利数据（左节点）
                List<TrialCalcResultSummaryDetailPO> originalDataList = trialCalcResultSummaryMapper.selectDetailsByUniqueKey(policyId, dealerCode, series);
                // 分组
                Map<String, BigDecimal> seriesGroupedData = originalDataList.stream()
                        .collect(Collectors.toMap(
                                TrialCalcResultSummaryDetailPO::getSeries,
                                TrialCalcResultSummaryDetailPO::getNamt,
                                (existing, replacement) -> existing
                        ));


                allDetails.addAll(originalDataList);
                HashMap<String, Double> seriesMaxRebateMap = new HashMap<>(16);
                // 对原始vin的数据进一步处理
                allDetails.forEach(detail -> {
                    detail.setId(null);
                    detail.setOriginalAmt(detail.getNamt());
                    detail.setBatchId(halfId);
                    String key = dealerCode + "-" + detail.getSeries() + "-" + seriesOldMap.get(detail.getSeries());
                    Double val = dealerSeriesNqtyWithUnrealRebateMap.get(key);
                    if (val == null) {
                        val = seriesMaxRebateMap.computeIfAbsent(detail.getSeries(), k -> trialCalcResultSummaryMapper.getMaxInitRebateByPolicyIds(dealerCode, policyIds, k));
                    }
                    // 这里避免虚拟返利计算不准而出现负数的情况
                    if (new BigDecimal(val).compareTo(detail.getOriginalAmt())<0) {
                        val = detail.getOriginalAmt().doubleValue();
                    }
                    detail.setDtstamp(LocalDateTime.now());
                    detail.setNamt(BigDecimal.valueOf(val));
                    detail.setDiffAmt(detail.getNamt().subtract(detail.getOriginalAmt()));
                });


                // 求真实返利中各个车系的数量
                Map<String, Integer> seriesCountMap = originalDataList.stream()
                        .collect(Collectors.groupingBy(
                                TrialCalcResultSummaryDetailPO::getSeries,
                                Collectors.summingInt(detail -> 1)
                        ));

                // 构造计算最优点过程中各个车系需要的虚拟车数量
                seriesOldMap.forEach((seriesName, nQty) -> {
                    // 求构造虚拟车的数量
                    int moreCount = nQty - seriesCountMap.get(seriesName);
                    // 构造额外的虚拟车的返利
                    if (moreCount > 0) {
                        for (int i = 0; i < moreCount; i++) {
                            TrialCalcResultSummaryDetailPO temp = new TrialCalcResultSummaryDetailPO();
                            temp.setDealerCode(dealerCode);
                            temp.setSeries(seriesName);
                            temp.setPolicyId(policyId);
                            temp.setPolicyName(policyDO.getVpolicyname());
                            temp.setVin("unreal_vin" + IdUtil.simpleUUID());
                            String key = dealerCode + "-" + seriesName + "-" + seriesOldMap.get(seriesName);
                            Double val = dealerSeriesNqtyWithUnrealRebateMap.get(key);
                            if (val == null) {
                                val = seriesMaxRebateMap.computeIfAbsent(temp.getSeries(), k -> trialCalcResultSummaryMapper.getMaxInitRebateByPolicyIds(dealerCode, policyIds, k));
                            }
                            BigDecimal originalAmt = seriesGroupedData.get(seriesName);
                            // 这里避免虚拟返利计算不准而出现负数的情况
                            if (new BigDecimal(val).compareTo(originalAmt)<0) {
                                val = originalAmt.doubleValue();
                            }
                            temp.setNamt(BigDecimal.valueOf(val));
                            temp.setNqty(1);
                            temp.setDim("Y");
                            temp.setBatchId(halfId);
                            temp.setDtstamp(LocalDateTime.now());

                            temp.setOriginalAmt(originalAmt);
                            temp.setDiffAmt(temp.getNamt().subtract(originalAmt));
                            allDetails.add(temp);
                        }
                    }

                });
            }

            trialCalcResultSummaryDetailService.saveBatch(allDetails);

        } else {
            int moreQty = bestPoint.getQty() - firstPoint.getQty();
            for (String policyId : policyIds) {
                // 获取对应零售数量的虚拟返利
                Double unrealAvgRebate = bestPoint.getAmt();
                // 从trial_calc_result_summary_detail找到对应的真实返利车情况
                List<TrialCalcResultSummaryDetailPO> originalDataList = trialCalcResultSummaryMapper.selectDetailsByUniqueKey(policyId, dealerCode, series);
                if (originalDataList == null) {
                    continue;
                }
                originalDataList = originalDataList.stream().limit(firstPoint.getQty()).toList();
                originalDataList.forEach(ele -> {
                    ele.setId(null);
                    ele.setBatchId(halfId);
                    ele.setNqty(1);
                    ele.setOriginalAmt(BigDecimal.valueOf(firstPoint.getAmt()));
                    ele.setNamt(new BigDecimal(unrealAvgRebate));
                    ele.setDiffAmt(ele.getNamt().subtract(ele.getOriginalAmt()));
                    ele.setDtstamp(LocalDateTime.now());

                });

                List<TrialCalcResultSummaryDetailPO> newDetails = new ArrayList<>(originalDataList);

                log.info("政策id:{},真实点的返利均值为:{}", policyId, originalDataList);
                // 真实车返利均值
                TrialCalcResultSummaryDetailPO originalCar = originalDataList.get(0);
                BigDecimal originalRebate = originalCar.getOriginalAmt();
                // nqty=N时的虚拟车返利均值
                // String unrealAvgRebate = trialCalcResultSummaryMapper.selectUnrealRebateByPolicyIdAndQtyAndDealerCode(policyId, dealerCode,series, bestPoint.getQty());
                for (int i = 0; i < moreQty; i++) {
                    TrialCalcResultSummaryDetailPO unrealCar = new TrialCalcResultSummaryDetailPO();
                    BeanUtils.copyProperties(originalCar, unrealCar);
                    unrealCar.setId(null);
                    unrealCar.setVin("unreal_vin" + originalCar.getVin() + i);
                    unrealCar.setBatchId(halfId);
                    unrealCar.setNqty(1);
                    unrealCar.setNamt(new BigDecimal(unrealAvgRebate));
                    unrealCar.setOriginalAmt(originalRebate);
                    unrealCar.setDiffAmt(unrealCar.getNamt().subtract(originalRebate));
                    unrealCar.setDtstamp(LocalDateTime.now());
                    newDetails.add(unrealCar);
                }
                trialCalcResultSummaryDetailService.saveBatch(newDetails);
            }
        }

    }




    @Override
    public List<KanbanDTO> kanban(RebateCalQueryRequest request) {
        //
        String dealerCode = request.getDealerCode();
        if (StringUtils.isNotBlank(mockDealerCode)) {
            dealerCode = mockDealerCode;
        }
        // 必须正序排序,适配最优点计算
        List<String> policyIds = request.getPolicyIds().stream().sorted(Comparator.comparingLong(Long::parseLong)).sorted().toList();

        List<String> series = request.getSeries();


        String policyIdComma = String.join(",", policyIds);

        // 记录查询的经销商代码，用于部门绩效统计
        this.saveRebateTrailViewRecord(dealerCode);

        // 获取经销商车系真实返利数据(经销商-车系-返利金额-计奖数量)
        List<TrialCalcResultSummaryDO> realRebateList = trialCalcResultSummaryMapper.getSeriesRealRebate(dealerCode, policyIds);
        // 如果只含有单车系列，则不展示全系汇总情况
        if(realRebateList.size() == Constants.TWO){
            realRebateList.removeIf(ele -> ele.getSeries().equals(ALL_SERIES));
        }
        // 如仅有一个系列，则无需展示全系汇总情况
        if (realRebateList.size() == 1) {
            realRebateList.removeIf(ele -> ele.getSeries().equals(ALL_SERIES));
        }
        // 如果前端指定了车系，则过滤车系
        if (CollectionUtils.isNotEmpty(series)) {
            realRebateList = realRebateList.stream().filter(ele -> series.contains(ele.getSeries())).toList();
        }
        List<KanbanDTO> kanbanDTOList = new ArrayList<>();
        // 按照车系开始组装行数据
        for (TrialCalcResultSummaryDO summary : realRebateList) {
            // 1.封装左节点数据
            KanbanDTO leftKanban = new KanbanDTO();
            leftKanban.setDealerCode(dealerCode);
            leftKanban.setSeries(summary.getSeries());
            // 计奖数量
            leftKanban.setTotalQty(summary.getNqty());
            // 预计返利
            leftKanban.setTotalAmt(summary.getNamt());
            int realCount = leftKanban.getTotalQty();
            if (realCount == 0) {
                log.info("计奖数量为0,series:{},dealerCode:{}", summary.getSeries(), dealerCode);
                leftKanban.setAvgAmt(0.0);
            } else {
                leftKanban.setAvgAmt(NumberUtil.div(leftKanban.getTotalAmt().doubleValue(), realCount, 0));
            }

            int realAakQty = trailBusinessCacheService.getActualAakStdInvoiceCountByPolicyIds(policyIds, dealerCode, summary.getSeries());

            leftKanban.setRealAakQty(realAakQty);

            // 2.封装右节点数据

            // 获取最优返利点信息
            List<KanbanDTO> unRealList = trialUnrealResultMapper.getKanbanResult(dealerCode, summary.getSeries(), policyIdComma);

            // 跑最优返利点时，有可能跑出来重复的数据，针对重复的数据做去重处理
            List<KanbanDTO> distinctList = unRealList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator
                                    .comparing(KanbanDTO::getDealerCode)
                                    .thenComparing(KanbanDTO::getSeries)
                                    .thenComparing(KanbanDTO::getTotalQty)
                                    .thenComparing(KanbanDTO::getTotalAmt)
                                    .thenComparing(KanbanDTO::getAvgAmt))),
                            ArrayList::new));

            if (CollectionUtils.isEmpty(distinctList)) {
                log.info("没有最优点信息,series:{},dealerCode:{}", summary.getSeries(), dealerCode);
                continue;
            }
            // 填充最优点所有信息
            this.putOtherInformationToUnrealList(leftKanban, distinctList, policyIds);
            leftKanban.setUnRealList(distinctList);

            kanbanDTOList.add(leftKanban);
        }

        return kanbanDTOList;
    }


    private void saveRebateTrailViewRecord(String dealerCode) {
        String time = DateUtil.format(new Date(), "yyyy-MM");
        LambdaQueryWrapper<TrailRecordPO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.eq(StringUtils.isNoneBlank(dealerCode), TrailRecordPO::getDealerCode, dealerCode);
        lambdaWrapper.eq(TrailRecordPO::getSealDate, time);
        List<TrailRecordPO> res = trailRecordMapper.selectList(lambdaWrapper);
        if (CollUtil.isEmpty(res)) {
            TrailRecordPO trailRecordPO = new TrailRecordPO();
            trailRecordPO.setSealDate(time);
            trailRecordPO.setDealerCode(dealerCode);
            trailRecordMapper.insert(trailRecordPO);
        }
    }



    private void putOtherInformationToUnrealList(KanbanDTO leftKanban, List<KanbanDTO> unRealList, List<String> policyIds) {
        String dealerCode = leftKanban.getDealerCode();
        String series = leftKanban.getSeries();
        Double maxAmt = 0.0;
        KanbanDTO goldItem = null;
        for (KanbanDTO unrealItem : unRealList) {
            // 设置差值
            unrealItem.setDiffTotalAmt(NumberUtil.sub(unrealItem.getTotalAmt(), leftKanban.getTotalAmt()));
            unrealItem.setDiffAvgAmt(NumberUtil.sub(unrealItem.getAvgAmt(), leftKanban.getAvgAmt()));
            unrealItem.setDiffQty(unrealItem.getTotalQty() - leftKanban.getTotalQty());
            unrealItem.setTotalAmt(unrealItem.getTotalAmt());
            unrealItem.setAvgAmt(unrealItem.getAvgAmt());
            // 设置aak达标百分比
            int aakMapCount = trailBusinessCacheService.getAakStdMap(policyIds, dealerCode, series);
            if(aakMapCount == 0){
                leftKanban.setMapCount((int) (leftKanban.getTotalQty()*1.5));
                unrealItem.setMapCount((int) (leftKanban.getTotalQty()*1.5));
            }else{
                leftKanban.setMapCount((int) (aakMapCount*1.5));
                unrealItem.setMapCount((int) (aakMapCount*1.5));
            }


            BigDecimal realAakQty = new BigDecimal(unrealItem.getTotalQty());
            BigDecimal aakMapValue = new BigDecimal(aakMapCount);
            if (BigDecimal.ZERO.compareTo(aakMapValue) == 0) {
                unrealItem.setAakPercent("-");
            } else {
                BigDecimal percentage = realAakQty.divide(aakMapValue, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                unrealItem.setAakPercent(percentage.toPlainString());
            }
            // 找出单台奖励均值最高的 KanbanDTO
            if (unrealItem.getAvgAmt() > maxAmt) {
                maxAmt = unrealItem.getAvgAmt();
                goldItem = unrealItem;
            }
        }
        //  金牌展示
        if (goldItem != null) {
            goldItem.setFlag("1");
        }
    }


    @Override
    public JsonResultVo<RebateCalDetailVO> kanbanDetail(RebateCalDetailQueryRequest request) {
        String series = request.getSeriesName();
        String batchId = request.getBatchId();
       
        String dealerCode = request.getDealerCode();
        if (StringUtils.isNotBlank(mockDealerCode)) {
            dealerCode = mockDealerCode;
        }
        // Top3返利政策
        List<KanbanPolicyTopDTO> policyTop = trailUnrealResultService.getRebatePolicyTop3(series, dealerCode, batchId);

        // 查询二分点的车集合
        List<TrialCalcResultSummaryDetailPO> details = trialCalcResultSummaryDetailService.selectListByBatchId(batchId);

        List<KanbanDetailDTO> dataList = new ArrayList<>();

        List<KanbanDetailDTO> unrealDataList = new ArrayList<>();
        List<KanbanDetailDTO> normalDataList = new ArrayList<>();

        int unrealCount = 1;

        for (TrialCalcResultSummaryDetailPO detail : details) {
            KanbanDetailDTO kanbanDetailDTO = new KanbanDetailDTO();
            kanbanDetailDTO.setPolicyId(detail.getPolicyId());
            kanbanDetailDTO.setSeriesName(detail.getSeries());
            kanbanDetailDTO.setVin(detail.getVin());
            kanbanDetailDTO.setAmt(detail.getNamt() + "");
            kanbanDetailDTO.setOriginalAmt(detail.getOriginalAmt() + "");
            kanbanDetailDTO.setDiffAmt(detail.getDiffAmt() + "");

            if (detail.getVin().startsWith("unreal")) {
                // 如果vin是以unreal开头，设置试算车名称并加入unrealDataList
                kanbanDetailDTO.setVin("试算车" + unrealCount++);
                unrealDataList.add(kanbanDetailDTO);
            } else {
                // 否则，加入normalDataList
                normalDataList.add(kanbanDetailDTO);
            }
        }

        dataList.addAll(unrealDataList);
        dataList.addAll(normalDataList);


        RebateCalDetailVO result = new RebateCalDetailVO();
        result.setKanbanPolicyTop3(policyTop);
        result.setKanbanDetail(dataList);

        JsonResultVo<RebateCalDetailVO> ret = new JsonResultVo<>();
        ret.setData(result);
        return ret;
    }


    @Override
    public Map<String, Integer> getDealerYearAakTask(String year) {
        // 获取经销商目标aak
        List<DealerTaskDTO> dealerTask = businessDataMapper.getDealerYearAAKTask(year);
        return dealerTask.stream().collect(Collectors.toMap(DealerTaskDTO::getSeries, DealerTaskDTO::getAakMap));
    }


    @Override
    public int getDealerLastYearAllSeriesAakMaxTask() {

        int year = DateUtil.year(new Date()) - 1;
        int mapMax = 0;
        Map<String, Integer> seriesVsAakMap = this.getDealerYearAakTask(year + "");
        for (Integer aakMap : seriesVsAakMap.values()) {
            if (aakMap > mapMax) {
                mapMax = aakMap;
            }
        }
        return mapMax;
    }

    /**
     * 异步维度：计算对象=政策+经销店代码组成
     *
     * @param execCalList 对应时间维度下的计算对象（政策+经销商代码组成）
     */
    @Override
    public void trailRealRebate(List<ExecCalcDTO> execCalList, String type) {
        String dim = "Y";

        Map<String, ExecCalcDTO> policyExecCalcDTO = execCalList.stream()
                .collect(Collectors.toMap(ExecCalcDTO::getPolicyId, Function.identity(), (o, n) -> o));
        log.info("计算真实返利的线程数：{}", policyExecCalcDTO.size());
        // 开始计算真实返利
        policyExecCalcDTO.forEach((policyId, execCalcDTO) -> {
            execCalcDTO.setDim(dim);
            execCalcDTO.setBatchId("AAKAMT");
            execCalcDTO.setVflag(1);
            execCalcDTO.setTrailType(type);
            log.info("参与计算真实返利的计算对象为：{}", execCalcDTO);
            execCalcService.startCalcForUnreal(execCalcDTO);
        });


    }



    private void saveAakStdRealSaleAndAim(List<ExecCalcDTO> execCalcDTOS, String type, String dim) {
        // 插入实际零售AAK和STD
        List<String> dealerCodes = execCalcDTOS.stream().map(ExecCalcDTO::getDealerCode).distinct().collect(Collectors.toList());
        // TODO:获取对应时间维度下经销商各车系的实际aak和std
        List<DealerAAKDTO> realAak = trialCalcDataService.getDealerAAK(dim, dealerCodes);
        List<DealerSTDDTO> realStd = trialCalcDataService.getDealerSTD(dim, dealerCodes);
        // 经销商目标
        List<DealerTaskDTO> aakAim = trialCalcDataService.getDealerTask(null, dim);
        List<DealerTaskDTO> stdAim = trialCalcDataService.getDealerSTDTask(null, dim);

        Map<String, DealerTaskDTO> aakMap = aakAim.stream()
                .collect(Collectors.toMap(
                        ele -> ele.getDealerCode() + "-" + ele.getSeries(),
                        Function.identity()
                ));
        Map<String, DealerTaskDTO> stdMap = stdAim.stream()
                .collect(Collectors.toMap(
                        ele -> ele.getDealerCode() + "-" + ele.getSeries(),
                        Function.identity()
                ));
        Map<String, DealerSTDDTO> codeSeriesWithStdMap = realStd.stream()
                .collect(Collectors.toMap(
                        ele -> ele.getDealerCode() + "-" + ele.getSeries(),
                        Function.identity()
                ));

        List<TrialCalcResultSummaryDO> saveList = new ArrayList<>();
        // 经销商+车系构成唯一
        // 统计aak
        for (DealerAAKDTO aakDTO : realAak) {
            TrialCalcResultSummaryDO trialCalcResultSummaryDO = new TrialCalcResultSummaryDO();
            trialCalcResultSummaryDO.setDealerCode(aakDTO.getDealerCode());
            trialCalcResultSummaryDO.setHistoryId("0");
            trialCalcResultSummaryDO.setSeries(aakDTO.getSeries());
            trialCalcResultSummaryDO.setNamt(0.0);
            trialCalcResultSummaryDO.setDim(dim);
            trialCalcResultSummaryDO.setPolicyId("0");
            String key = aakDTO.getDealerCode() + "-" + aakDTO.getSeries();

            // 设置实际aak
            trialCalcResultSummaryDO.setAakQty(aakDTO.getNqty());

            // 设置aakMap
            DealerTaskDTO task = aakMap.get(key);
            trialCalcResultSummaryDO.setAakMap(task != null ? task.getTaskQty() : 0);

            // 设置实际std
            trialCalcResultSummaryDO.setStdQty(codeSeriesWithStdMap.get(key) != null ? codeSeriesWithStdMap.get(key).getStd() : 0);
            trialCalcResultSummaryDO.setVrqty(0);
            trialCalcResultSummaryDO.setBatchId("AAK");

            // 设置stdMap
            DealerTaskDTO task2 = stdMap.get(key);
            trialCalcResultSummaryDO.setStdMap(task2 != null ? task2.getTaskQty() : 0);
            trialCalcResultSummaryDO.setTrailType(type);
            saveList.add(trialCalcResultSummaryDO);
        }

        trialCalcResultSummaryService.saveBatch(saveList);
    }


}
