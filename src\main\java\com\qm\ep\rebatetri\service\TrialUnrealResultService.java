package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO;
import com.qm.ep.rebatetri.domain.dto.KanbanDTO;
import com.qm.tds.api.service.IQmBaseService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TrialUnrealResultService extends IQmBaseService<TrialUnrealResultDO> {

    List<KanbanDTO> getKanbanResult(String dim, String dealerCode, String series,  List<String> policyId);
}
