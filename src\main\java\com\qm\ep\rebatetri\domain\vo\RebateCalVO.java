package com.qm.ep.rebatetri.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "返利 Cal Vo")
@Data
public class RebateCalVO {
    @Schema(description = "总数量")
    private int totalQty;
    @Schema(description = "总AMT")
    private int totalAmt;
    @Schema(description = "平均 AMT")
    private int avgAmt;

    @Schema(description = "系列")
    private String series;

    @Schema(description = "联合国真实名单")
    private List<Extra> unRealList;

    @Schema(description = "额外")
    @Data
    public static class Extra {
        @Schema(description = "总数量")
        private int totalQty;
        @Schema(description = "总AMT")
        private int totalAmt;
        @Schema(description = "平均 AMT")
        private int avgAmt;
        @Schema(description = "差异数量")
        private int diffQty;
        @Schema(description = "系列")
        private String series;
        @Schema(description = "差异总 AMT")
        private int diffTotalAmt;
        @Schema(description = "diff avg amt")
        private int diffAvgAmt;

    }
}
