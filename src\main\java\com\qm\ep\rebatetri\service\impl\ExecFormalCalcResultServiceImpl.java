package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcResultDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebatetri.mapper.ExecFormalCalcResultMapper;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.ExecFormalCalcResultService;
import com.qm.ep.rebatetri.service.SystemConfigService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecFormalCalcResultServiceImpl extends QmBaseServiceImpl<ExecFormalCalcResultMapper, ExecFormalCalcResultDO> implements ExecFormalCalcResultService {


    @Resource
    private SystemConfigService systemConfigService;
    @Resource
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;
    @Resource
    private ExecFormalCalcResultMapper execFormalCalcResultMapper;
    @Resource
    private RebateBaseRemote rebateBaseRemote;

    @Override
    public QmPage<ExecFormalCalcResultDO> queryCalcResult(ExecCalcResultDTO execCalcResultDTO) {
        LoginKeyDO userInfo = BootAppUtil.getLoginKey();
        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcResultDO::getHistoryId, execCalcResultDTO.getHistoryId());

        ExecFormalCalcHistoryDO execCalcHistoryDO = execFormalCalcHistoryMapper.selectById(execCalcResultDTO.getHistoryId());
        String company = systemConfigService.getValueByCode("company");
        if("bx".equals(company)){
            JsonResultVo<Boolean> isBxAdminVO = rebateBaseRemote.checkPersonAtTopLevel(userInfo.getOperatorId());
            if(isBxAdminVO.getCode() != 200){
                log.error(isBxAdminVO.getMsg());
                throw new QmException("系统发生错误，请联系管理员");
            }
            boolean isBxAdmin = isBxAdminVO.getData();
            if(!isBxAdmin){
                // 如果不是管理员
                if(execCalcHistoryDO != null) {
                    SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
                    List<String> fields = sqlStructureVO.getFields();
                    List<String> sumFields = new ArrayList<>();
                    // 查询是否包含经销商代码字段，
                    String dealerCodeField = null;
                    for(int i = 1; i <= fields.size(); i++){
                        if("经销商代码".equals(fields.get(i-1))){
                            dealerCodeField = "field" + i;
                        }
                        sumFields.add("field" + i);
                    }
                    // 如果存在经销商代码字段，则需要通过经销商代码进行过滤
                    if(CharSequenceUtil.isNotEmpty(dealerCodeField)){
                        JsonResultVo<List<String>> leafOrgVO = rebateBaseRemote.getLeafOrg(userInfo.getOperatorId());
                        if(isBxAdminVO.getCode() != 200){
                            log.error(isBxAdminVO.getMsg());
                            throw new QmException("系统发生错误，请联系管理员");
                        }
                        List<String> dealerCodeList = leafOrgVO.getData();
                        if(CollUtil.isNotEmpty(dealerCodeList)){
                            queryWrapper.in(dealerCodeField, dealerCodeList);
                            QmPage<ExecFormalCalcResultDO> page = table(queryWrapper, execCalcResultDTO);

                            Map<String, Object> sumResult = execFormalCalcResultMapper.selectCalcResultSum(execCalcResultDTO.getHistoryId(), sumFields, dealerCodeField, dealerCodeList);
                            page.setExtension(sumResult);
                            return page;
                        }else{
                            QmPage<ExecFormalCalcResultDO> page = new QmPage<>();
                            page.setItems(Collections.emptyList());
                            page.setExtension(Collections.emptyMap());
                            return page;
                        }
                    }
                }
            }
        }
        QmPage<ExecFormalCalcResultDO> list = table(queryWrapper, execCalcResultDTO);
        if(execCalcHistoryDO != null){
            String sumResult = execCalcHistoryDO.getSumResult();
            if(BootAppUtil.isnotNullOrEmpty(sumResult)) {
                list.setExtension(JSONUtils.jsonToMap(sumResult));
            }
        }
        return list;
    }

    /**
     * 通过SQL 保存
     *
     * @param map 参数
     * @return 返回
     */
    @Override
    public int saveBySql(Map<String, Object> map) {
        return baseMapper.insertBySql(map);
    }

    /**
     * 通过SQL执行
     *
     * @param map 参数
     * @return 返回
     */
    @Override
    public List<Map> getBySql(Map<String, Object> map) {
        return baseMapper.selectBySql(map);
    }

    @Override
    public List<Integer> getAllId(ExecFormalCalcResultDO tempDTO) {
        return execFormalCalcResultMapper.getAllId(tempDTO.getHistoryId());
    }


}
