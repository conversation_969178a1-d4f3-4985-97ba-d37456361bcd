package com.qm.ep.rebatetri.domain.dto;

import cn.hutool.core.util.NumberUtil;
import com.qm.ep.rebatetri.utils.Calculator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 半计算数据
 *
 * <AUTHOR>
 * @date 2024/12/14
 */
@Data
public class HalfCalcDTO implements Serializable {

    private Calculator calculator;

    @Schema(description = "数量")
    private int qty;
    /**
     * 平均返利
     */
    @Schema(description = "平均返利")
    private Double amt;
    @Schema(description = "批次 ID")
    private String batchId;

    public static HalfCalcDTO buildHalf(String batchId, int taskQty, Double amt) {
        HalfCalcDTO halfCalcDTO = new HalfCalcDTO();
        double averageAmt = NumberUtil.div(amt.doubleValue(), taskQty, 0);
        halfCalcDTO.setQty(taskQty);
        halfCalcDTO.setAmt(averageAmt);
        halfCalcDTO.setBatchId(batchId);
        return halfCalcDTO;
    }

    public static HalfCalcDTO build(String batchId, int taskQty, Double avgAmt) {
        HalfCalcDTO halfCalcDTO = new HalfCalcDTO();
        halfCalcDTO.setQty(taskQty);
        halfCalcDTO.setAmt(avgAmt);
        halfCalcDTO.setBatchId(batchId);
        return halfCalcDTO;
    }

}
