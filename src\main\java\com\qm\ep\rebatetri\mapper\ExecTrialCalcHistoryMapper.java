package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExecTrialCalcHistoryMapper extends QmBaseMapper<ExecTrialCalcHistoryDO> {

    /**
     * 获取计算对象的试算历史
     * @param calcObjectId 计算对象ID
     * @return 返回
     */
    List<ExecTrialCalcHistoryDO> getExecCalcHistory(String calcObjectId);

    List<ExecTrialCalcHistoryDO> getExecCalcHistoryByPolicyIdAndObjectType(@Param("policyId") String policyId,
                                                                           @Param("objectType") String objectType);

    /**
     * 获取计算对象的试算历史
     * @param batchId 计算对象ID
     * @return 返回
     */
    List<ExecTrialCalcHistoryDO> getExecCalcHistoryByBatchId(String batchId);

    /**
     * 导入计算计算结果，方便后面统计
     *
     * @param sumaryId
     * @param realEField
     * @param seriesField
     * @param historyId
     * @param vinField
     * @param trailType
     */
    void groupResult(
            @Param("realEField") String realEField,
            @Param("seriesField") String seriesField,
            @Param("dealerCodeField") String dealerCodeField,
            @Param("historyId") String historyId,
            @Param("policyId") String policyId,
            @Param("dim") String dim,
            @Param("vrseries") String vrseries,
            @Param("vrqty") int vrqty,
            @Param("batchId") String batchId,
            @Param("series") String series,
            @Param("vinField") String vinField,
            @Param("trailType") String trailType);
    int deleteByObjectType(@Param("objectType") String objectType);

    List<DealerSeriesVinDTO> selectSimpleRebateResult(String historyId, String rebateAmountField, String seriesField, String vinField, String dealerCodeField);

    ExecTrialCalcHistoryDO selectSimpleById(String historyId);

    ExecTrialCalcHistoryDO selectByCondition(String policyId, String objectId, String objectType);

    List<DealerSeriesVinDTO> selectAllRebateResult(String historyId, String rebateAmountField, String seriesField, String vinField, String dealerCodeField);

    String selectPolicyNameById(String policyId);

    void groupResultDetail(String realEField, String seriesField, String dealerField, String policyId, String policyName, String dim, String series, String vinField, String historyId);
}
