package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("unreal_calc_result")
@Schema(description = "unreal_calc_result对象")
public class UnrealCalcResultDO implements Serializable {



    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "计算对象试算历史表主键")
    @TableField("historyId")
    private String historyId;

    @Schema(description = "field1")
    @TableField("field1")
    private String field1;

    @Schema(description = "field2")
    @TableField("field2")
    private String field2;

    @Schema(description = "field3")
    @TableField("field3")
    private String field3;

    @Schema(description = "field4")
    @TableField("field4")
    private String field4;

    @Schema(description = "field5")
    @TableField("field5")
    private String field5;

    @Schema(description = "field6")
    @TableField("field6")
    private String field6;

    @Schema(description = "field7")
    @TableField("field7")
    private String field7;

    @Schema(description = "field8")
    @TableField("field8")
    private String field8;

    @Schema(description = "field9")
    @TableField("field9")
    private String field9;

    @Schema(description = "field10")
    @TableField("field10")
    private String field10;

    @Schema(description = "field11")
    @TableField("field11")
    private String field11;

    @Schema(description = "field12")
    @TableField("field12")
    private String field12;

    @Schema(description = "field13")
    @TableField("field13")
    private String field13;

    @Schema(description = "field14")
    @TableField("field14")
    private String field14;

    @Schema(description = "field15")
    @TableField("field15")
    private String field15;

    @Schema(description = "field16")
    @TableField("field16")
    private String field16;

    @Schema(description = "field17")
    @TableField("field17")
    private String field17;

    @Schema(description = "field18")
    @TableField("field18")
    private String field18;

    @Schema(description = "field19")
    @TableField("field19")
    private String field19;

    @Schema(description = "field20")
    @TableField("field20")
    private String field20;

    @Schema(description = "field21")
    @TableField("field21")
    private String field21;

    @Schema(description = "field22")
    @TableField("field22")
    private String field22;

    @Schema(description = "field23")
    @TableField("field23")
    private String field23;

    @Schema(description = "field24")
    @TableField("field24")
    private String field24;

    @Schema(description = "field25")
    @TableField("field25")
    private String field25;

    @Schema(description = "field26")
    @TableField("field26")
    private String field26;

    @Schema(description = "field27")
    @TableField("field27")
    private String field27;

    @Schema(description = "field28")
    @TableField("field28")
    private String field28;

    @Schema(description = "field29")
    @TableField("field29")
    private String field29;

    @Schema(description = "field30")
    @TableField("field30")
    private String field30;

    @Schema(description = "field31")
    @TableField("field31")
    private String field31;

    @Schema(description = "field32")
    @TableField("field32")
    private String field32;

    @Schema(description = "field33")
    @TableField("field33")
    private String field33;

    @Schema(description = "field34")
    @TableField("field34")
    private String field34;

    @Schema(description = "field35")
    @TableField("field35")
    private String field35;

    @Schema(description = "field36")
    @TableField("field36")
    private String field36;

    @Schema(description = "field37")
    @TableField("field37")
    private String field37;

    @Schema(description = "field38")
    @TableField("field38")
    private String field38;

    @Schema(description = "field39")
    @TableField("field39")
    private String field39;

    @Schema(description = "field40")
    @TableField("field40")
    private String field40;

    @Schema(description = "field41")
    @TableField("field41")
    private String field41;

    @Schema(description = "field42")
    @TableField("field42")
    private String field42;

    @Schema(description = "field43")
    @TableField("field43")
    private String field43;

    @Schema(description = "field44")
    @TableField("field44")
    private String field44;

    @Schema(description = "field45")
    @TableField("field45")
    private String field45;

    @Schema(description = "field46")
    @TableField("field46")
    private String field46;

    @Schema(description = "field47")
    @TableField("field47")
    private String field47;

    @Schema(description = "field48")
    @TableField("field48")
    private String field48;

    @Schema(description = "field49")
    @TableField("field49")
    private String field49;

    @Schema(description = "field50")
    @TableField("field50")
    private String field50;

}
