package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * dms返利预测查询请求
 *
 * <AUTHOR>
 * @date 2023/10/12
 */
@Schema(description = "dms返利预测查询请求")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RebateCalQueryRequest {


    /**
     * 政策ID列表
     */
    @Schema(description = "政策ID列表")
    @NotEmpty(message = "政策ID列表不能为空")
    private List<String> policyIds;

    /**
     * 经销商代码
     */
    @Schema(description = "经销商代码")
    @NotBlank(message = "经销商代码不能为空")
    private String dealerCode;

    /**
     * 车系
     */
    @Schema(description = "车系")
    private List<String> series;

    @Schema(description = "最优点批次id")
    private String batchId;



    @Schema(description = "试算类型-aak,std,invoice")
    private String reimburseStatus;

}
