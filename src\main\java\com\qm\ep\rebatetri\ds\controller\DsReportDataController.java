package com.qm.ep.rebatetri.ds.controller;

import com.qm.ep.rebatetri.domain.bean.ReportDataDO;
import com.qm.ep.rebatetri.domain.dto.ReportDataDTO;
import com.qm.ep.rebatetri.service.ReportDataService;
import com.qm.ep.rebatetri.service.TenantDealerService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;

@RestController
@RequestMapping("/ds/reportData")
@Tag(name = "报表数据", description = "报表数据")
@Slf4j
public class DsReportDataController extends BaseController {

    @Autowired
    private ReportDataService reportDataService;
    @Autowired
    private TenantDealerService tenantDealerService;
    @Autowired
    private DataSource dataSource;


    @Operation(summary = "查询报表数据", description = "查询报表数据")
    @PostMapping("/getDataList")
    public JsonResultVo<QmPage<ReportDataDO>> getDataList(@RequestBody ReportDataDTO paramDTO){
        JsonResultVo<QmPage<ReportDataDO>> result = new JsonResultVo<>();
        String tenantName = tenantDealerService.getDsName(paramDTO.getDealerCode());
        QmPage<ReportDataDO> list = reportDataService.queryTable(tenantName, paramDTO);
        result.setData(list);
        return result;
    }



}
