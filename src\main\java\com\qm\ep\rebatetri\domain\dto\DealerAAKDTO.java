package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 经销商AAK DTO
 * <AUTHOR>
 * @date 2023年9月9日
 */
@Schema(description = "数据AAKDTO")
@Data
public class DealerAAKDTO {

    
    

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "已零售数量")
    private Integer nqty;

    @Schema(description = "系列")
    private String series;

    @Schema(description = "维度 Y年度 Q季度 M月度")
    private String dim;

    @Schema(description = "政策ID")
    private String policyId;
}