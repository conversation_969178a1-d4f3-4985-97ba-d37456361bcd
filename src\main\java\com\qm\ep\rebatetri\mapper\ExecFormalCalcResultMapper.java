package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ExecFormalCalcResultMapper extends QmBaseMapper<ExecFormalCalcResultDO> {

    /**
     * 动态sql 插入
     * @param map 参数
     * @return 返回
     */
    @Insert("${sql}")
    int insertBySql(Map<String, Object> map);

    /**
     * 动态sql 查询
     * @param map 参数
     * @return 返回
     */
    @Select("${sql}")
    List<Map> selectBySql(Map<String, Object> map);

    Map<String, Object> selectCalcResultSum(@Param("historyId") String historyId, @Param("fields") List<String> fields,
                                            @Param("dealerCodeField") String dealerCodeField, @Param("dealerCodeList") List<String> dealerCodeList);

    List<Integer> getAllId(String historyId);

}
