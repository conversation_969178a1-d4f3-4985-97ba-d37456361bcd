package com.qm.ep.rebatetri.utils;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OneMorelCalculator extends Calculator {

    public OneMorelCalculator(Map<String, Integer> seriesCounts) {
        super(seriesCounts);
    }


    /**
     * 获取最简比例关系并按比例从高到低排序
     *
     * @return {@link List }<{@link Map.Entry }<{@link String }, {@link Integer }>>
     */
    private List<Map.Entry<String, Integer>> getSortedReducedRatio() {
        int[] reducedRatio = getReducedRatio();
        String[] seriesArray = seriesCounts.keySet().toArray(new String[0]);

        List<Map.Entry<String, Integer>> sortedSeries = new ArrayList<>();
        for (int i = 0; i < seriesArray.length; i++) {
            sortedSeries.add(new AbstractMap.SimpleEntry<>(seriesArray[i], reducedRatio[i]));
        }

        // 按比例从高到低排序
        sortedSeries.sort((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()));
        return sortedSeries;
    }


    /**
     * 获取最简比例关系
     *
     * @return {@link int[] }
     */
    private int[] getReducedRatio() {
        List<Integer> countsList = new ArrayList<>(seriesCounts.values());
        int gcd = countsList.get(0);
        for (int i = 1; i < countsList.size(); i++) {
            gcd = findGcd(gcd, countsList.get(i));
        }
        int[] reducedRatio = new int[countsList.size()];
        for (int i = 0; i < countsList.size(); i++) {
            reducedRatio[i] = countsList.get(i) / gcd;
        }
        return reducedRatio;
    }


    private int findGcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }


    public String getWhichSeries(int cursorQty) {
        List<Map.Entry<String, Integer>> sortedSeries = getSortedReducedRatio();
        int totalCycles = sortedSeries.stream().mapToInt(Map.Entry::getValue).sum();

        int cycleIndex = (cursorQty - 1) % totalCycles;
        int cumulativeSum = 0;
        for (Map.Entry<String, Integer> entry : sortedSeries) {
            cumulativeSum += entry.getValue();
            if (cycleIndex < cumulativeSum) {
                String series = entry.getKey();
                seriesOldMap.put(series, seriesOldMap.get(series) + 1);
                return series;
            }
        }
        return null;
    }
}











