package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 政策试算数据待初始化表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Schema(description = "<p> 政策试算数据待初始化表 </p>")
@Getter
@Setter
@TableName("unreal_policy_record")
public class UnrealPolicyRecordPO implements Serializable {

    
    

    @Schema(description = "同上")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "策略 ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "创建")
    @TableField("createon")
    private LocalDateTime createon;

    @Schema(description = "数据状态：0-待初始化，1-正在初始化，2-初始化完成")
    @TableField("data_status")
    private String dataStatus;


    @Schema(description = "策略名称")
    @TableField("policyName")
    private String policyName;

}
