package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "数据:返利查询响应")
@Data
public class RebateQueryResponse {


    @Schema(description = "主键")
    private String id;

    /**
     * 政策id
     */
    @Schema(description = "政策id")
    private String policyId;


    /**
     * 政策代码
     */
    @Schema(description = "政策代码")
    private String policyCode;

    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 车型
     */
    @Schema(description = "车型")
    private String series;

    /**
     * 底盘号
     */
    @Schema(description = "底盘号")
    private String chassisNumber;

    /**
     * 返利金额
     */
    @Schema(description = "返利金额")
    private String rebateAmount;


    /**
     * 红旗伙伴提示
     */
    @Schema(description = "红旗伙伴提示")
    private String dealerRemark;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createby;

    /**
     * 创建者部门
     */
    @Schema(description = "创建者部门")
    private String createPart;


    /**
     * 政策文件路径
     */
    @Schema(description = "政策文件路径")
    private String filePath;

    @Schema(description = "入口完成")
    private String entryFinish;

    /**
     * 财务兑付的状态
     */
    @Schema(description = "财务兑付的状态")
    private Integer payStatus;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
}