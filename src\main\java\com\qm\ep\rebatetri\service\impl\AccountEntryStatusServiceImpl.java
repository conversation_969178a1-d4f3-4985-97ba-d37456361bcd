package com.qm.ep.rebatetri.service.impl;


import com.qm.ep.rebatetri.domain.bean.AccountEntryStatusDO;
import com.qm.ep.rebatetri.mapper.AccountEntryStatusMapper;
import com.qm.ep.rebatetri.service.AccountEntryStatusService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountEntryStatusServiceImpl extends QmBaseServiceImpl<AccountEntryStatusMapper, AccountEntryStatusDO> implements AccountEntryStatusService {

}