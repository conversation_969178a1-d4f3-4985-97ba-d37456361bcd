package com.qm.ep.rebatetri.domain.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车系额度对象")
public class SeriesQuotaDO implements Serializable {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "车系")
    private String series;

    @Schema(description = "额度")
    private String quota;

    @Schema(description = "是否停用")
    private String ifStop;

}
