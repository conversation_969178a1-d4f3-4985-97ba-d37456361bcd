package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.domain.bean.BusinessDataDO;
import com.qm.ep.rebatetri.domain.bean.BusinessDataUnrealDO;
import com.qm.ep.rebatetri.domain.dto.PolicyPublishConfigDTO;
import com.qm.ep.rebatetri.service.BusinessdataUnrealService;
import com.qm.ep.rebatetri.service.TrailCalAsyncTask;
import com.qm.ep.rebatetri.service.TrialCalcDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TrailCalAsyncTaskImpl implements TrailCalAsyncTask {

    @Autowired
    private BusinessdataUnrealService businessdataUnrealService;

    @Autowired
    private TrialCalcDataService trialCalcDataService;

    @Override
    // @DS(DataSourceType.W)
    // @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void doInsertCarIntoUnrealBusinessdata(List<PolicyPublishConfigDTO> policyPublishConfigList, int finalMapMax, String policyId, Map<String, List<BusinessDataDO>> table) {
        log.info("开始插入车辆数据到虚拟表中");
        List<BusinessDataUnrealDO> businessDataUnrealDOList = new ArrayList<>();

        for (PolicyPublishConfigDTO policyPublishConfigInfo : policyPublishConfigList) {

            String dealerCode = policyPublishConfigInfo.getDealerCode();


            List<BusinessDataDO> vinData = table.get(dealerCode);
            // 如果虚拟车表没有车可能是因为计算结果中没有该车，可能是经销商还没有卖车或者是这个经销商就不参与该政策
            if (vinData == null) {
                log.info("该经销商在合并方案的试算结果中没有返利数据:{},可能是经销商还没有卖车或者是这个经销商就不参与该政策，所以没有构造虚拟车", dealerCode);
                continue;
            }

            // 按照vin分组，其实这里就是零售和批发
            Map<Object, List<BusinessDataDO>> uuidVinCache = vinData.stream().collect(Collectors.groupingBy(BusinessDataDO::getField20));
            // 这一家经销商下所有车系
            uuidVinCache.forEach((vin, car) -> {
                for (int i = 0; i < finalMapMax; i++) {
                    // 虚拟车还是采用真实的vin+后缀i
                    for (BusinessDataDO businessDataDO : car) {
                        BusinessDataUnrealDO newBusinessDataUnrealDO = new BusinessDataUnrealDO();
                        BeanUtils.copyProperties(businessDataDO, newBusinessDataUnrealDO);
                        newBusinessDataUnrealDO.setField81(policyId);
                        newBusinessDataUnrealDO.setId(UUID.randomUUID().toString());
                        newBusinessDataUnrealDO.setTablename("经销商提车统计");
                        newBusinessDataUnrealDO.setField20(newBusinessDataUnrealDO.getField20() + i);
                        businessDataUnrealDOList.add(newBusinessDataUnrealDO);
                    }
                }
            });
            if (businessDataUnrealDOList.size() >= 1000) {
                businessdataUnrealService.saveBatchTran(businessDataUnrealDOList);
                businessDataUnrealDOList.clear();
            }
        }
        if (!businessDataUnrealDOList.isEmpty()) {
            businessdataUnrealService.saveBatchTran(businessDataUnrealDOList);
        }

    }

    @Override
    public void doInsertInvoiceIntoUnrealBusinessdata(List<PolicyPublishConfigDTO> policyPublishConfigList, int finalMapMax, String policyId, Map<String, List<BusinessDataDO>> table) {
        log.info("开始插入发票数据到虚拟表中");
        List<BusinessDataUnrealDO> businessDataUnrealDOList = new ArrayList<>();

        for (PolicyPublishConfigDTO policyPublishConfigInfo : policyPublishConfigList) {

            String dealerCode = policyPublishConfigInfo.getDealerCode();

            List<BusinessDataDO> invoiceData = table.get(dealerCode);
            // 如果虚拟车表没有车可能是因为计算结果中没有该车，可能是经销商还没有卖车或者是这个经销商就不参与该政策
            if (invoiceData == null) {
                log.info("该经销商在合并方案的试算结果中没有返利数据:{},可能是经销商还没有卖车或者是这个经销商就不参与该政策，所以没有构造虚拟车", dealerCode);
                continue;
            }
            // 每一家经销商下所有车系
            for (BusinessDataDO invoice : invoiceData) {
                for (int i = 0; i < finalMapMax; i++) {
                    // 虚拟车还是采用真实的vin
                    BusinessDataUnrealDO newBusinessDataUnrealDO = new BusinessDataUnrealDO();
                    BeanUtils.copyProperties(invoice, newBusinessDataUnrealDO);
                    newBusinessDataUnrealDO.setField81(policyId);
                    newBusinessDataUnrealDO.setId(UUID.randomUUID().toString());
                    newBusinessDataUnrealDO.setTablename("终端发票查询");
                    businessDataUnrealDOList.add(newBusinessDataUnrealDO);
                }
            }

            if (businessDataUnrealDOList.size() >= 1000) {
                businessdataUnrealService.saveBatchTran(businessDataUnrealDOList);
                businessDataUnrealDOList.clear();
            }
        }
        if (!businessDataUnrealDOList.isEmpty()) {
            businessdataUnrealService.saveBatchTran(businessDataUnrealDOList);
        }
    }
}
