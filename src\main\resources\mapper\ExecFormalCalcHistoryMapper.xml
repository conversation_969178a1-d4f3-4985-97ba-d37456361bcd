<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.ExecFormalCalcHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <id column="id" property="id" />
        <result column="policyId" property="policyId" />
        <result column="objectId" property="objectId" />
        <result column="objectType" property="objectType" />
        <result column="execType" property="execType" />
        <result column="calcVersion" property="calcVersion" />
        <result column="state" property="state" />
        <result column="applyEntryState" property="applyEntryState" />
        <result column="mqState" property="mqState" />
        <result column="begin" property="begin" />
        <result column="end" property="end" />
        <result column="sqlStructure" property="sqlStructure" />
        <result column="log" property="log" />
        <result column="sumResult" property="sumResult" />
        <result column="executorThreadName" property="executorThreadName" />
        <result column="mqLog" property="mqLog" />
        <result column="createon" property="createOn" />
        <result column="createby" property="createBy" />
        <result column="updateon" property="updateOn" />
        <result column="updateby" property="updateBy" />
        <result column="isDeleted" property="isDeleted" />
        <result column="recordVersion" property="recordVersion" />
        <result column="autoTurnTable" property="autoTurnTable" />
        <result column="tableName" property="tableName" />
        <result column="autoEnterAccount" property="autoEnterAccount" />
        <result column="extra" property="extra" />
        <result column="dtstamp" property="dtstamp" />
    </resultMap>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.policyId,
                a.objectId,
                a.objectType,
                a.execType,
                a.calcVersion,
                a.state,
                a.applyEntryState,
                a.mqState,
                a.begin,
                a.end,
                TIMESTAMPDIFF(SECOND,a.begin,a.end) AS cost,
                a.sqlStructure,
                a.log,
                a.sumResult,
                a.executorThreadName,
                a.mqLog,
                a.createon,
                a.createby,
                b.vtext createByName,
                a.updateon,
                a.updateby,
                c.vtext updateByName,
                a.isDeleted,
                a.recordVersion,
                a.id,
                a.autoTurnTable,
                a.tableName,
                a.autoEnterAccount,
                a.extra,
                a.dtstamp
            from exec_formal_calc_history a
             left join SYSC000_M b on a.createby = b.NMAINID
             left join SYSC000_M c on a.updateby = c.NMAINID
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" /> where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=","> #{item} </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null"> ${k} IS NULL </when>
                        <otherwise> ${k} = #{v} </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from ( <include refid="QuerySQL" />${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="getExecCalcHistory" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO">
        select  id, policyId, objectId, objectType, execType, calcVersion, state, begin, end, sqlStructure,
            log, sumResult, executorThreadName, createon, createby, updateon, updateby, isDeleted, recordVersion, dtstamp
        from exec_formal_calc_history
        where objectId = #{calcObjectId}
    </select>
</mapper>
