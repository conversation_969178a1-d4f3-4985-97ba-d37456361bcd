package com.qm.ep.rebatetri.monitor.threadpool.processor;

import cn.hutool.core.util.StrUtil;
import com.qm.ep.rebatetri.monitor.threadpool.annotation.MonitorThreadPool;
import com.qm.ep.rebatetri.monitor.threadpool.config.MonitorCache;
import com.qm.ep.rebatetri.monitor.threadpool.executor.MonitoredThreadPoolTaskExecutor;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.ReflectionUtils;

import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
public class ThreadPoolMonitorProcessor implements MonitorProcessor {

    @Override
    public void process(Object bean) {
        ReflectionUtils.doWithFields(bean.getClass(), field -> {
            if(AnnotatedElementUtils.hasAnnotation(field, MonitorThreadPool.class)) {
                MonitorThreadPool monitorThreadPool = AnnotatedElementUtils.findMergedAnnotation(field, MonitorThreadPool.class);
                String threadPoolExecutorName = StrUtil.emptyToDefault(monitorThreadPool.value(), UUID.randomUUID().toString());
                ReflectionUtils.makeAccessible(field);
                Object filedValue = ReflectionUtils.getField(field, bean);
                if (filedValue == null) {
                    return;
                }
                Object value = AopProxyUtils.getSingletonTarget(filedValue);
                processExecutor(value, threadPoolExecutorName);
            }
        });
        ReflectionUtils.doWithMethods(bean.getClass(), method -> {
            if(AnnotatedElementUtils.hasAnnotation(method, MonitorThreadPool.class) && AnnotatedElementUtils.hasAnnotation(method, Bean.class)) {
                MonitorThreadPool monitorThreadPool = AnnotatedElementUtils.findMergedAnnotation(method, MonitorThreadPool.class);
                String threadPoolExecutorName = StrUtil.emptyToDefault(monitorThreadPool.value(), UUID.randomUUID().toString());
                Bean beanAnnotation = AnnotatedElementUtils.findMergedAnnotation(method, Bean.class);
                String[] beanNames = beanAnnotation.value();
                String beanName = method.getName();
                if(beanNames.length>0) {
                    beanName = beanNames[0];
                }
                Object value = SpringContextHolder.getBean(beanName);
                processExecutor(value, threadPoolExecutorName);
            }
        });
    }

    private void processExecutor(Object executorObj, String poolName) {
        Executor executor;
        if (executorObj instanceof ThreadPoolExecutor) {
            executor = (ThreadPoolExecutor)executorObj;
        } else if (executorObj instanceof ThreadPoolTaskExecutor) {
            executor = ((ThreadPoolTaskExecutor) executorObj).getThreadPoolExecutor();
        } else if (executorObj instanceof MonitoredThreadPoolTaskExecutor) {
            executor = (MonitoredThreadPoolTaskExecutor) executorObj;
            ((MonitoredThreadPoolTaskExecutor) executorObj).setPoolName(poolName);
        } else {
            log.error("添加线程池监控失败, 需要 {}, 或者 {}, 或者 {}", ThreadPoolExecutor.class.getName(), ThreadPoolTaskExecutor.class.getName(), MonitoredThreadPoolTaskExecutor.class.getName());
            return;
        }
        MonitorCache.executorMap.put(poolName, executor);
    }

}

