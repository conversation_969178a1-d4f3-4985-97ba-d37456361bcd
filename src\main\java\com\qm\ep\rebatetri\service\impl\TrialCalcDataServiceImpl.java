package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.date.DateUtil;
import com.qm.ep.rebatetri.domain.bean.PolicyDO;
import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.ep.rebatetri.mapper.BusinessDataMapper;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.service.DealerCalcService;
import com.qm.ep.rebatetri.service.TrialCalcDataService;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 试算数据服务接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TrialCalcDataServiceImpl implements TrialCalcDataService {

    @Autowired
    private PolicyMapper policyMapper;
    public static final String Q = "Q";
    public static final String M = "M";
    public static final String Y = "Y";
    @Autowired
    private BusinessDataMapper businessDataMapper;
    @Autowired
    private DealerCalcService dealerCalcService;

    /**
     * 查询经销商MAP目标
     *
     * @param dealerCode 经销商代码
     * @param dim        维护 Y年度 Q季度 M月度
     * @return
     */
    @Override
    public List<DealerTaskDTO> getDealerTask(String dealerCode, String dim) {
        String year = "1900";
        List<String> ymList = new ArrayList<>();
        Date sysDate = DateUtil.date();
        switch (dim) {
            case Y:
                year = DateUtil.format(sysDate, "yyyy");
                break;
            case Q:
                int quarter = DateUtil.quarter(sysDate);
                if (quarter == 1) {
                    ymList.add(DateUtil.format(sysDate, "yyyy01"));
                    ymList.add(DateUtil.format(sysDate, "yyyy02"));
                    ymList.add(DateUtil.format(sysDate, "yyyy03"));
                } else if (quarter == 2) {
                    ymList.add(DateUtil.format(sysDate, "yyyy04"));
                    ymList.add(DateUtil.format(sysDate, "yyyy05"));
                    ymList.add(DateUtil.format(sysDate, "yyyy06"));
                } else if (quarter == 3) {
                    ymList.add(DateUtil.format(sysDate, "yyyy07"));
                    ymList.add(DateUtil.format(sysDate, "yyyy08"));
                    ymList.add(DateUtil.format(sysDate, "yyyy09"));
                } else if (quarter == 4) {
                    ymList.add(DateUtil.format(sysDate, "yyyy10"));
                    ymList.add(DateUtil.format(sysDate, "yyyy11"));
                    ymList.add(DateUtil.format(sysDate, "yyyy12"));
                }
                break;
            case M:
                ymList.add(DateUtil.format(DateUtil.date(), "yyyyMM"));
                break;
            default:
                throw new QmException("dim参数无效");
        }
        return businessDataMapper.getDealerAAKTask(dealerCode, dim, ymList, year);
    }

    /**
     * 获取全部可计算的商务政策信息，作为正式计算的入参
     *
     * @param dim 维护 Y年度 Q季度 M月度
     * @return
     */
    @Override
    public List<ExecCalcDTO> getCalcPolicy(String dim) {
        Date sysDate = DateUtil.date();
        Date beginDate;
        Date endDate;
        switch (dim) {
            case Y:
                beginDate = DateUtil.beginOfYear(sysDate);
                endDate = DateUtil.endOfYear(sysDate);
                break;
            case Q:
                beginDate = DateUtil.beginOfQuarter(sysDate);
                endDate = DateUtil.endOfQuarter(sysDate);
                break;
            case M:
                beginDate = DateUtil.beginOfMonth(sysDate);
                endDate = DateUtil.endOfMonth(sysDate);
                break;
            default:
                throw new QmException("dim参数无效");
        }

        return businessDataMapper.getCalcPolicy(DateUtil.formatDateTime(beginDate), DateUtil.formatDateTime(endDate));
    }

    /**
     * a
     * 获取经销商AAK
     *
     * @param dim 维护 Y年度 Q季度 M月度
     * @param
     * @return
     */
    @Override
    public List<DealerAAKDTO> getDealerAAK(String dim, List<String> dealerCodes) {
        Date sysDate = DateUtil.date();
        String year = DateUtil.format(sysDate, "yyyy");
        ;
        List<String> months = new ArrayList<>();
        switch (dim) {
            case Y:
                break;
            case Q:
                int quarter = DateUtil.quarter(sysDate);
                if (quarter == 1) {
                    months.add("01");
                    months.add("02");
                    months.add("03");
                } else if (quarter == 2) {
                    months.add("04");
                    months.add("05");
                    months.add("06");
                } else if (quarter == 3) {
                    months.add("07");
                    months.add("08");
                    months.add("09");
                } else if (quarter == 4) {
                    months.add("10");
                    months.add("11");
                    months.add("12");
                }
                break;
            case M:
                months.add(DateUtil.format(sysDate, "MM"));
                break;
            default:
                throw new QmException("dim参数无效");
        }
        return businessDataMapper.getDealerAAK(year, months, dim, dealerCodes);
    }

    @Override
    public List<DealerTaskDTO> getDealerSTDTask(String dealerCode, String dim) {
        String year = "1900";
        List<String> ymList = new ArrayList<>();
        Date sysDate = DateUtil.date();
        switch (dim) {
            case Y:
                year = DateUtil.format(sysDate, "yyyy");
                break;
            case Q:
                int quarter = DateUtil.quarter(sysDate);
                if (quarter == 1) {
                    ymList.add(DateUtil.format(sysDate, "yyyy01"));
                    ymList.add(DateUtil.format(sysDate, "yyyy02"));
                    ymList.add(DateUtil.format(sysDate, "yyyy03"));
                } else if (quarter == 2) {
                    ymList.add(DateUtil.format(sysDate, "yyyy04"));
                    ymList.add(DateUtil.format(sysDate, "yyyy05"));
                    ymList.add(DateUtil.format(sysDate, "yyyy06"));
                } else if (quarter == 3) {
                    ymList.add(DateUtil.format(sysDate, "yyyy07"));
                    ymList.add(DateUtil.format(sysDate, "yyyy08"));
                    ymList.add(DateUtil.format(sysDate, "yyyy09"));
                } else if (quarter == 4) {
                    ymList.add(DateUtil.format(sysDate, "yyyy10"));
                    ymList.add(DateUtil.format(sysDate, "yyyy11"));
                    ymList.add(DateUtil.format(sysDate, "yyyy12"));
                }
                break;
            case M:
                ymList.add(DateUtil.format(DateUtil.date(), "yyyyMM"));
                break;
            default:
                throw new QmException("dim参数无效");
        }
        return businessDataMapper.getDealerSTDTask(dealerCode, dim, ymList, year);
    }

    @Override
    public List<DealerSTDDTO> getDealerSTD(String dim, List<String> dealerCodes) {
        Date sysDate = DateUtil.date();
        String year = DateUtil.format(sysDate, "yyyy");
        ;
        List<String> months = new ArrayList<>();
        switch (dim) {
            case Y:
                break;
            case Q:
                int quarter = DateUtil.quarter(sysDate);
                if (quarter == 1) {
                    months.add("01");
                    months.add("02");
                    months.add("03");
                } else if (quarter == 2) {
                    months.add("04");
                    months.add("05");
                    months.add("06");
                } else if (quarter == 3) {
                    months.add("07");
                    months.add("08");
                    months.add("09");
                } else if (quarter == 4) {
                    months.add("10");
                    months.add("11");
                    months.add("12");
                }
                break;
            case M:
                months.add(DateUtil.format(sysDate, "MM"));
                break;
            default:
                throw new QmException("dim参数无效");
        }
        return businessDataMapper.getDealerSTD(year, months, dim, dealerCodes);
    }

    @Override
    public List<String> isVisible(List<String> policyIds) {
        return businessDataMapper.isVisible(policyIds);
    }

    @Override
    public void truncateData() {
        List<String> tables = Arrays.asList("trial_calc_result_summary", "trial_unreal_log", "trial_unreal_result", "trial_calc_result_summary_detail","trail_business_cache");
        businessDataMapper.truncateData(tables);
    }

    @Override
    public List<ExecCalcDTO> getCalcPolicyByType(String type) {
        List<ExecCalcDTO> calcPolicyByType = policyMapper.getCalcPolicyByType(type);
        QmPage<RebateQueryResponse> rebateQueryResponseQmPage = dealerCalcService.queryPolicy(new RebateQueryRequest());
        Set<String> mustHavePolicyIds = rebateQueryResponseQmPage.getItems().stream().map(RebateQueryResponse::getPolicyId).collect(Collectors.toSet());
        // 过滤掉不参与的政策
        calcPolicyByType = calcPolicyByType.stream().filter(calc -> mustHavePolicyIds.contains(calc.getPolicyId())).collect(Collectors.toList());
        return calcPolicyByType;
    }

    @Override
    public List<String> getDistinctPolicyIdAscByType(String dim, String dealerCode, String type) {
        Date sysDate = DateUtil.date();
        Date beginDate;
        Date endDate;
        switch (dim) {
            case Y:
                beginDate = DateUtil.beginOfYear(sysDate);
                endDate = DateUtil.endOfYear(sysDate);
                break;
            case Q:
                beginDate = DateUtil.beginOfQuarter(sysDate);
                endDate = DateUtil.endOfQuarter(sysDate);
                break;
            case M:
                beginDate = DateUtil.beginOfMonth(sysDate);
                endDate = DateUtil.endOfMonth(sysDate);
                break;
            default:
                throw new QmException("dim参数无效");
        }

        return policyMapper.getDistinctPolicyIdAscByType(DateUtil.formatDateTime(beginDate), DateUtil.formatDateTime(endDate), dealerCode, type);
    }

    @Override
    public Map<String, Integer> getRealAakStdInvoiceCountByMonths(List<String> policyIds, String dealerCode, String trailType) {
        Map<String, Integer> res = new HashMap<>();

        List<String> aakMonths = new ArrayList<>();
        List<String> stdMonths = new ArrayList<>();
        List<String> invoiceMonths = new ArrayList<>();
        // 获取所有月份
        for (String policyId : policyIds) {
            PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
            Date dbegin = policyDO.getDbegin();
            Date dend = policyDO.getDend();
            // 获取dbegin和dend之间的月份
            if (trailType.equals("aak")){
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!aakMonths.contains(mm)) {
                        aakMonths.add(mm);
                    }
                }
            }
            if (trailType.equals("std")){
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    String mm = DateUtil.format(date, "MM");
                    if (!stdMonths.contains(mm)) {
                        stdMonths.add(mm);
                    }
                }
            }
            if (trailType.equals("invoice")){
                for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
                    invoiceMonths.add(DateUtil.format(date, "MM"));
                }
            }
        }
        if (trailType.equals("aak")){
            List<DealerAAKDTO> aakDTO = businessDataMapper.getDealerAAK(DateUtil.year(new Date()) + "", aakMonths, "Y", Collections.singletonList(dealerCode));
            if (aakDTO!= null && !aakDTO.isEmpty()) {
                res = aakDTO.stream().collect(Collectors.toMap(DealerAAKDTO::getSeries, DealerAAKDTO::getNqty, (a, b) -> b));

            }
        }
        if (trailType.equals("std")){
            List<DealerSTDDTO> stdDTO = businessDataMapper.getDealerSTD(DateUtil.year(new Date()) + "", stdMonths, "Y", Collections.singletonList(dealerCode));
            if (stdDTO!= null && !stdDTO.isEmpty()) {
                res = stdDTO.stream().collect(Collectors.toMap(DealerSTDDTO::getSeries, DealerSTDDTO::getStd, (a, b) -> b));
            }
        }
        if (trailType.equals("invoice")){
            List<DealerAAKDTO> invoiceDTO = businessDataMapper.getDealerInvoice(DateUtil.year(new Date()) + "", invoiceMonths, "Y", Collections.singletonList(dealerCode));
            if (invoiceDTO!= null && !invoiceDTO.isEmpty()) {
                res = invoiceDTO.stream().collect(Collectors.toMap(DealerAAKDTO::getSeries, DealerAAKDTO::getNqty, (a, b) -> b));
            }
        }
        return res;
    }

    public static void main(String[] args) {
        List<String> months = new ArrayList<>();
        Date dbegin= DateUtil.parseDate("2021-05-02");
        Date dend= DateUtil.parseDate("2021-01-31");
        for (Date date = dbegin; date.before(dend); date = DateUtil.offsetMonth(date, 1)) {
            months.add(DateUtil.format(date, "MM"));
        }
        System.out.println(months);
    }

}
