<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.BusinessDataUnrealMapper">

    <resultMap id="BusinessDataMap" type="com.qm.ep.rebatetri.domain.bean.BusinessDataDO">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="TABLENAME" property="tableName" jdbcType="VARCHAR"/>
        <result column="FIELD1" property="field1" jdbcType="VARCHAR"/>
        <result column="FIELD2" property="field2" jdbcType="VARCHAR"/>
        <result column="FIELD3" property="field3" jdbcType="VARCHAR"/>
        <result column="FIELD4" property="field4" jdbcType="VARCHAR"/>
        <result column="FIELD5" property="field5" jdbcType="VARCHAR"/>
        <result column="FIELD6" property="field6" jdbcType="VARCHAR"/>
        <result column="FIELD7" property="field7" jdbcType="VARCHAR"/>
        <result column="FIELD8" property="field8" jdbcType="VARCHAR"/>
        <result column="FIELD9" property="field9" jdbcType="VARCHAR"/>
        <result column="FIELD10" property="field10" jdbcType="VARCHAR"/>
        <result column="FIELD11" property="field11" jdbcType="VARCHAR"/>
        <result column="FIELD12" property="field12" jdbcType="VARCHAR"/>
        <result column="FIELD13" property="field13" jdbcType="VARCHAR"/>
        <result column="FIELD14" property="field14" jdbcType="VARCHAR"/>
        <result column="FIELD15" property="field15" jdbcType="VARCHAR"/>
        <result column="FIELD16" property="field16" jdbcType="VARCHAR"/>
        <result column="FIELD17" property="field17" jdbcType="VARCHAR"/>
        <result column="FIELD18" property="field18" jdbcType="VARCHAR"/>
        <result column="FIELD19" property="field19" jdbcType="VARCHAR"/>
        <result column="FIELD20" property="field20" jdbcType="VARCHAR"/>
        <result column="FIELD21" property="field21" jdbcType="VARCHAR"/>
        <result column="FIELD22" property="field22" jdbcType="VARCHAR"/>
        <result column="FIELD23" property="field23" jdbcType="VARCHAR"/>
        <result column="FIELD24" property="field24" jdbcType="VARCHAR"/>
        <result column="FIELD25" property="field25" jdbcType="VARCHAR"/>
        <result column="FIELD26" property="field26" jdbcType="VARCHAR"/>
        <result column="FIELD27" property="field27" jdbcType="VARCHAR"/>
        <result column="FIELD28" property="field28" jdbcType="VARCHAR"/>
        <result column="FIELD29" property="field29" jdbcType="VARCHAR"/>
        <result column="FIELD30" property="field30" jdbcType="VARCHAR"/>
        <result column="FIELD31" property="field31" jdbcType="VARCHAR"/>
        <result column="FIELD32" property="field32" jdbcType="VARCHAR"/>
        <result column="FIELD33" property="field33" jdbcType="VARCHAR"/>
        <result column="FIELD34" property="field34" jdbcType="VARCHAR"/>
        <result column="FIELD35" property="field35" jdbcType="VARCHAR"/>
        <result column="FIELD36" property="field36" jdbcType="VARCHAR"/>
        <result column="FIELD37" property="field37" jdbcType="VARCHAR"/>
        <result column="FIELD38" property="field38" jdbcType="VARCHAR"/>
        <result column="FIELD39" property="field39" jdbcType="VARCHAR"/>
        <result column="FIELD40" property="field40" jdbcType="VARCHAR"/>
        <result column="FIELD41" property="field41" jdbcType="VARCHAR"/>
        <result column="FIELD42" property="field42" jdbcType="VARCHAR"/>
        <result column="FIELD43" property="field43" jdbcType="VARCHAR"/>
        <result column="FIELD44" property="field44" jdbcType="VARCHAR"/>
        <result column="FIELD45" property="field45" jdbcType="VARCHAR"/>
        <result column="FIELD46" property="field46" jdbcType="VARCHAR"/>
        <result column="FIELD47" property="field47" jdbcType="VARCHAR"/>
        <result column="FIELD48" property="field48" jdbcType="VARCHAR"/>
        <result column="FIELD49" property="field49" jdbcType="VARCHAR"/>
        <result column="FIELD50" property="field50" jdbcType="VARCHAR"/>
        <result column="FIELD51" property="field51" jdbcType="VARCHAR" />
        <result column="FIELD52" property="field52" jdbcType="VARCHAR" />
        <result column="FIELD53" property="field53" jdbcType="VARCHAR" />
        <result column="FIELD54" property="field54" jdbcType="VARCHAR" />
        <result column="FIELD55" property="field55" jdbcType="VARCHAR" />
        <result column="FIELD56" property="field56" jdbcType="VARCHAR" />
        <result column="FIELD57" property="field57" jdbcType="VARCHAR" />
        <result column="FIELD58" property="field58" jdbcType="VARCHAR" />
        <result column="FIELD59" property="field59" jdbcType="VARCHAR" />
        <result column="FIELD60" property="field60" jdbcType="VARCHAR" />
        <result column="FIELD61" property="field61" jdbcType="VARCHAR" />
        <result column="FIELD62" property="field62" jdbcType="VARCHAR" />
        <result column="FIELD63" property="field63" jdbcType="VARCHAR" />
        <result column="FIELD64" property="field64" jdbcType="VARCHAR" />
        <result column="FIELD65" property="field65" jdbcType="VARCHAR" />
        <result column="FIELD66" property="field66" jdbcType="VARCHAR" />
        <result column="FIELD67" property="field67" jdbcType="VARCHAR" />
        <result column="FIELD68" property="field68" jdbcType="VARCHAR" />
        <result column="FIELD69" property="field69" jdbcType="VARCHAR" />
        <result column="FIELD70" property="field70" jdbcType="VARCHAR" />
        <result column="FIELD71" property="field71" jdbcType="VARCHAR" />
        <result column="FIELD72" property="field72" jdbcType="VARCHAR" />
        <result column="FIELD73" property="field73" jdbcType="VARCHAR" />
        <result column="FIELD74" property="field74" jdbcType="VARCHAR" />
        <result column="FIELD75" property="field75" jdbcType="VARCHAR" />
        <result column="FIELD76" property="field76" jdbcType="VARCHAR" />
        <result column="FIELD77" property="field77" jdbcType="VARCHAR" />
        <result column="FIELD78" property="field78" jdbcType="VARCHAR" />
        <result column="FIELD79" property="field79" jdbcType="VARCHAR" />
        <result column="FIELD80" property="field80" jdbcType="VARCHAR" />
        <result column="FIELD81" property="field81" jdbcType="VARCHAR" />
        <result column="FIELDID" property="fieldId" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 公共查询 -->

    <insert id="batchInsert" parameterType="java.util.List">
        insert into businessdata_unreal
        (id,
        TABLENAME,
        field1,
        field2,
        field3,
        field4,
        field5,
        field6,
        field7,
        field8,
        field9,
        field10,
        field11,
        field12,
        field13,
        field14,
        field15,
        field16,
        field17,
        field18,
        field19,
        field20,
        field21,
        field22,
        field23,
        field24,
        field25,
        field26,
        field27,
        field28,
        field29,
        field30,
        field31,
        field32,
        field33,
        field34,
        field35,
        field36,
        field37,
        field38,
        field39,
        field40,
        field41,
        field42,
        field43,
        field44,
        field45,
        field46,
        field47,
        field48,
        field49,
        field50,
        field51,
        field52,
        field53,
        field54,
        field55,
        field56,
        field57,
        field58,
        field59,
        field60,
        field61,
        field62,
        field63,
        field64,
        field65,
        field66,
        field67,
        field68,
        field69,
        field70,
        field71,
        field72,
        field73,
        field74,
        field75,
        field76,
        field77,
        field78,
        field79,
        field80,
        field81,
        fieldId)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.tablename},
            #{item.field1},
            #{item.field2},
            #{item.field3},
            #{item.field4},
            #{item.field5},
            #{item.field6},
            #{item.field7},
            #{item.field8},
            #{item.field9},
            #{item.field10},
            #{item.field11},
            #{item.field12},
            #{item.field13},
            #{item.field14},
            #{item.field15},
            #{item.field16},
            #{item.field17},
            #{item.field18},
            #{item.field19},
            #{item.field20},
            #{item.field21},
            #{item.field22},
            #{item.field23},
            #{item.field24},
            #{item.field25},
            #{item.field26},
            #{item.field27},
            #{item.field28},
            #{item.field29},
            #{item.field30},
            #{item.field31},
            #{item.field32},
            #{item.field33},
            #{item.field34},
            #{item.field35},
            #{item.field36},
            #{item.field37},
            #{item.field38},
            #{item.field39},
            #{item.field40},
            #{item.field41},
            #{item.field42},
            #{item.field43},
            #{item.field44},
            #{item.field45},
            #{item.field46},
            #{item.field47},
            #{item.field48},
            #{item.field49},
            #{item.field50},
            #{item.field51},
            #{item.field52},
            #{item.field53},
            #{item.field54},
            #{item.field55},
            #{item.field56},
            #{item.field57},
            #{item.field58},
            #{item.field59},
            #{item.field60},
            #{item.field61},
            #{item.field62},
            #{item.field63},
            #{item.field64},
            #{item.field65},
            #{item.field66},
            #{item.field67},
            #{item.field68},
            #{item.field69},
            #{item.field70},
            #{item.field71},
            #{item.field72},
            #{item.field73},
            #{item.field74},
            #{item.field75},
            #{item.field76},
            #{item.field77},
            #{item.field78},
            #{item.field79},
            #{item.field80},
            #{item.field81},
            #{item.fieldId}
                )
        </foreach>
    </insert>
    <update id="updateUnrealPolicyStatusById">
        update unreal_policy_record set data_status = #{status} WHERE policyId = #{policyId}
    </update>

    <delete id="deleteAll">
        delete from businessdata_unreal
    </delete>
    <delete id="deleteBatchByPolicyId">
        delete from businessdata_unreal where tablename= #{tableName}' and field81 = #{policyId}
    </delete>
    <delete id="deleteRebateSummaryByPolicyId">
        delete from unreal_car_rebate_summary where policyId = #{policyId}
    </delete>

    <select id="selectCombinationByPolicyId" resultType="com.qm.ep.rebatetri.domain.bean.CombinationMainDO">
        SELECT * FROM `combinationmain` WHERE policyid = #{policyId}
    </select>
    <select id="selectPolicyById" resultType="com.qm.ep.rebatetri.domain.dto.PolicyDTO">
        select * from policy where id = #{policyId}
    </select>
</mapper>
