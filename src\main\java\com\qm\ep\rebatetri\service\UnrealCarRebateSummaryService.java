package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryPO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <p>
 * 返利跑批数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface UnrealCarRebateSummaryService extends IService<UnrealCarRebateSummaryPO> {

    /**
     * 保存批处理异步
     *
     * @param saveList 保存列表
     */
    @Async("realRebateCalAsync")
    void saveBatchAsync(List<UnrealCarRebateSummaryPO> saveList);

    /**
     * 异步保存批量更新
     *
     * @param saveList 保存列表
     */
    @Async("realRebateCalAsync")
    void saveBatchUpdateAsync(List<UnrealCarRebateSummaryPO> saveList);
}
