package com.qm.ep.rebatetri.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import com.qm.ep.rebatetri.mapper.TrialCalcResultSummaryDetailMapper;
import com.qm.ep.rebatetri.mapper.TrialCalcResultSummaryMapper;
import com.qm.ep.rebatetri.service.TrialCalcResultSummaryDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 试算结果汇总表-试算器用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Service
public class TrialCalcResultSummaryDetailServiceImpl extends ServiceImpl<TrialCalcResultSummaryDetailMapper, TrialCalcResultSummaryDetailPO> implements TrialCalcResultSummaryDetailService {
    @Resource
    private TrialCalcResultSummaryMapper trialCalcResultSummaryMapper;
    @Resource
    private TrialCalcResultSummaryDetailMapper trialCalcResultSummaryDetailMapper;

    @Override
    public List<TrialCalcResultSummaryDetailPO> selectListByBatchId(String batchId) {
        return trialCalcResultSummaryDetailMapper.selectListByBatchId(batchId);
    }

    @Override
    public List<KanbanPolicyTopDTO> getKanbanPolicyTop(String series, String dealerCode) {
        return trialCalcResultSummaryDetailMapper.getKanbanPolicyTop(series, dealerCode);
    }
}
