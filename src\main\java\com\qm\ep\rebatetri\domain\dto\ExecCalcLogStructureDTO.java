package com.qm.ep.rebatetri.domain.dto;

import com.qm.ep.rebatetri.constant.Constants;
import com.qm.tds.util.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ExecCalcLogStructureDTO {

    @Schema(description = "时间")
    @Builder.Default
    private String time = DateUtils.getSysdateStr();

    @Schema(description = "内容")
    private String content;

    @Schema(description = "地位")
    @Builder.Default
    private String status = Constants.SUCCESS_STATUS;
}
