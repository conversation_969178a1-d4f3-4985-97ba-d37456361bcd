package com.qm.ep.rebatetri.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("trial_unreal_result")
@Schema(description = "数据TrialUnrealResultDO对象")
public class TrialUnrealResultDO implements Serializable {



    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "经销商代码")
    @TableField("dealerCode")
    private String dealerCode;

    @Schema(description = "车系")
    @TableField("series")
    private String series;

    @Schema(description = "总返利平均金额")
    @TableField("totalAmt")
    private Double totalAmt;

    @Schema(description = "总数量")
    @TableField("totalQty")
    private Integer totalQty;

    @Schema(description = "维度")
    @TableField("dim")
    private String dim;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "aak返利平均金额")
    @TableField("aakAmt")
    private Double aakAmt;

    @Schema(description = "aak数量")
    @TableField("aakQty")
    private Integer aakQty;

    @TableField("batchId")
    private String batchId;

    @TableField("policyId")
    private String policyId;

    @TableField("bestAmt")
    private Double bestAmt;

    @TableField("manual")
    private String manual;

}
