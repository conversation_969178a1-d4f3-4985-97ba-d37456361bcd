package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO;
import com.qm.ep.rebatetri.domain.dto.KanbanDTO;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.mapper.TrialUnrealResultMapper;
import com.qm.ep.rebatetri.service.TrialCalcDataService;
import com.qm.ep.rebatetri.service.TrialUnrealResultService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class TrialUnrealResultServiceImpl extends QmBaseServiceImpl<TrialUnrealResultMapper, TrialUnrealResultDO> implements TrialUnrealResultService {
    @Resource
    private PolicyMapper policyMapper;
    @Resource
    private TrialUnrealResultMapper trialUnrealResultMapper;
    @Autowired
    private TrialCalcDataService trialCalcDataService;

    /**
     * 存在某一个政策实际没有参与试算，试算结果表没有该政策。但是全系计算政策id逗号拼接考虑了改政策，所以不能取出所有政策进行拼接。
     * 为了便于区分全政策和混合政策，采用逗号和横线进行拼接
     *
     * @param dim
     * @param dealerCode
     * @param series
     * @param policyIds
     * @return
     */
    @Override
    public List<KanbanDTO> getKanbanResult(String dim, String dealerCode, String series, List<String> policyIds) {

        List<String> aakPolicyIds = trialCalcDataService.getDistinctPolicyIdAscByType(dim, dealerCode, "aak");
        // List<String> stdPolicyIds = trialCalcDataService.getDistinctPolicyIdAscByType(dim, dealerCode, "std");
        // List<String> invoicePolicyIds = trialCalcDataService.getDistinctPolicyIdAscByType(dim, dealerCode, "invoice");
        String policyIdComma = "";
        // 前端未选-全选
        if (CollectionUtils.isEmpty(policyIds)) {
            // 首次进来未选默认是返回aak全政策计算结果
            policyIdComma = String.join(",", aakPolicyIds);
        }
        // 单选政策
        else if (policyIds.size() == 1) {
            // 单政策
            policyIdComma = policyIds.get(0);
        }
        // 混合和全选政策
        else {
            policyIdComma = String.join(",", policyIds);
        }
        // // 混合
        // else if (policyIds.size() > 1) {
        //     String tempId = policyIds.get(0);
        //     PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(tempId);
        //     String trailType = policyDO.getTrailType();
        //     // 混合政策
        //     if (trailType.equals("aak") && policyIds.size() < aakPolicyIds.size()) {
        //         // 将policyIds正序排序并逗号拼接
        //         policyIdComma = String.join(",", policyIds);
        //     }
        //     // 混合政策
        //     if (trailType.equals("std") && policyIds.size() < stdPolicyIds.size()) {
        //         policyIdComma = String.join(",", policyIds);
        //     }
        //     // 混合政策
        //     if (trailType.equals("invoice") && policyIds.size() < invoicePolicyIds.size()) {
        //         policyIdComma = String.join(",", policyIds);
        //     }
        // }
        // // 全选政策
        // else {
        //     policyIdComma = String.join(",", policyIds);
        // }

        return trialUnrealResultMapper.getKanbanResult(dealerCode, series, policyIdComma);
    }

}
