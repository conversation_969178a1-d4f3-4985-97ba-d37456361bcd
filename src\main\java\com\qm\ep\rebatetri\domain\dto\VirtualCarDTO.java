package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "虚拟汽车 DTO")
@Data
@Builder
public class VirtualCarDTO {

    @Schema(description = "历史记录 ID")
    private String historyId;

    @Schema(description = "经销商代码字段")
    private String dealerCodeField;

    @Schema(description = "汽车系列领域")
    private String carSeriesField;

    @Schema(description = "VIN 字段")
    private String vinField;

    @Schema(description = "type 字段")
    private String typeField;

    @Schema(description = "数据源")
    private String dataSource;

}
