package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.domain.bean.TrialCalcResultVrSummaryDO;
import com.qm.ep.rebatetri.mapper.TrialCalcResultVrSummaryMapper;
import com.qm.ep.rebatetri.service.TrialCalcResultVrSummaryService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 试算结果汇总表-虚拟车试算用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Service
public class TrialCalcResultVrSummaryServiceImpl extends QmBaseServiceImpl<TrialCalcResultVrSummaryMapper, TrialCalcResultVrSummaryDO> implements TrialCalcResultVrSummaryService {

}
