package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 商务政策的计算状态
 * <AUTHOR>
 */
@Schema(description = "商务政策的计算状态")
@Getter
public enum PolicyCalculateStatusEnum {
    @Schema(description = "无计算") NONE_CALCULATE("01", "未计算"),
    @Schema(description = "计算") CALCULATED("02", "已计算");

    PolicyCalculateStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

}
