package com.qm.ep.rebatetri;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@EnableHystrix
@SpringBootApplication(scanBasePackages = "com.qm")
@EnableFeignClients(basePackages = {"com.qm"})
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan({"com.qm.tds.dynamic.mapper", "com.qm.tds.base.mapper", "com.qm.ep.rebatetri.mapper","com.qm.tds.base.tdsfile.mapper"})
public class EpRebateTriApplication {

    public static void main(String[] args) {
        SpringApplication.run(EpRebateTriApplication.class, args);
    }
}
