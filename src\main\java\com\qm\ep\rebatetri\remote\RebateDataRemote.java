package com.qm.ep.rebatetri.remote;

import com.qm.ep.rebatetri.config.AsyncFeignConfig;
import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(contextId= "tds-service-rebate-data",name = "tds-service-rebate-data",configuration = AsyncFeignConfig.class)
public interface RebateDataRemote {

    /**
     * 入账
     * @param execCalcDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/EpData/applyEntryAccount", produces = "application/json")
    JsonResultVo<Object> applyEntryAccount(@RequestBody ExecFormalCalcHistoryDO execCalcDTO);



}
