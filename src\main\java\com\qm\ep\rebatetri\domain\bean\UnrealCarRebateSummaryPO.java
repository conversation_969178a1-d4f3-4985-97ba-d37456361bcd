package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 返利跑批数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("unreal_car_rebate_summary")
public class UnrealCarRebateSummaryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 政策ID
     */
    @TableField("policyId")
    private String policyId;

    /**
     * 计算历史id
     */
    @TableField("historyId")
    private String historyId;

    /**
     * 经销商代码
     */
    @TableField("dealerCode")
    private String dealerCode;

    /**
     * 车系
     */
    @TableField("series")
    private String series;

    /**
     * 返利金额
     */
    @TableField("namt")
    private BigDecimal namt;

    /**
     * 数量
     */
    @TableField("nqty")
    private Integer nqty;

    /**
     * 维度
     */
    @TableField("dim")
    private String dim;

    /**
     * 时间戳
     */
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;

    /**
     * 虚拟车数量
     */
    @TableField("vrqty")
    private Integer vrqty;

    /**
     * 虚拟车车系
     */
    @TableField("vrseries")
    private String vrseries;

    /**
     * AAK：统计实际aak；AAKMAT：统计实际返利；
     */
    @TableField("batchId")
    private String batchId;

    /**
     * 月度：二分计算的batchId
     */
    @TableField("m_batchId")
    private String mBatchid;

    /**
     * 季度：二分计算的batchId
     */
    @TableField("q_batchId")
    private String qBatchid;

    /**
     * 年度：二分计算的batchId
     */
    @TableField("y_batchId")
    private String yBatchid;


}
