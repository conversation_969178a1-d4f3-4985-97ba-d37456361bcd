package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "计算对象类型枚举")
public enum CalcObjectTypeEnum {
    
    @Schema(description = "基本")
    BASIC("basic", "基础底表"),
    @Schema(description = "因素")
    FACTOR("factor", "计算因子"),
    @Schema(description = "公式")
    FORMULA("formula", "计算公式"),
    @Schema(description = "部分")
    SECTION("section", "计算区间"),
    @Schema(description = "前提")
    PREMISE("premise", "前提条件"),
    @Schema(description = "计划")
    PLAN("plan", "计算方案"),
    @Schema(description = "组合") COMBINATION("combination", "计算方案合并"),
    @Schema(description = "梯子") LADDER("ladder", "阶梯"),
    @Schema(description = "免费举报") REPORT_FREE("report_free", "自由报表-主机厂"),
    @Schema(description = "报告 C1") REPORT_C1("report_c1", "个性化报表1-经销商集团"),
    
    @Schema(description = "目标树") TARGET_TREE("target_tree", "指标树"),
  
    @Schema(description = "虚幻") UNREAL("unreal", "虚拟车");

    CalcObjectTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

}
