package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "复制Remote接收数据")
@Data
public class CopyRemoteReceiveDTO {
    @Schema(description = "计算对象 ID")
    private String calcObjectId;
    @Schema(description = "calc 对象 ID 新增")
    private String calcObjectIdNew;
    @Schema(description = "策略 ID 新增")
    private String policyIdNew;
}