package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REDFLAGTRIALMAIN")
@Schema(description = "红旗伙伴试算数据配置主表对象")
public class RedFlagTrialMainDO implements Serializable {



    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "政策主键")
    @TableField("POLICYID")
    private String policyId;

    @Schema(description = "试算数据名称")
    @TableField("FACTORNAME")
    private String factorName;

    @Schema(description = "主数据源")
    @TableField("MAINTABLE")
    private String mainTable;

    @Schema(description = "计奖")
    private String reward;

    @Schema(description = "计量")
    private String metering;

    @Schema(description = "创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
