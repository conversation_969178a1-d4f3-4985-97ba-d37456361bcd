package com.qm.ep.rebatetri.controller;

import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebatetri.domain.dto.TurnBusinessBottomTableDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.extern.slf4j.Slf4j;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class ExecFormalCalcHistoryControllerTest extends BaseTestController<ExecFormalCalcHistoryController> {

    @Before
    public void beforeMethod() {
        this.initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
     * 覆盖dto
     */
    @Test
    public void moduleDtoTest() {
        EqualsVerifier.simple().forClass(ExecCalcHistoryDTO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    /**
     * 覆盖vo
     */
    @Test
    public void moduleVoTest() {
        EqualsVerifier.simple().forClass(SqlStructureVO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    /**
     * 覆盖do
     */
    @Test
    public void moduleDoTest() {
        EqualsVerifier.simple().forClass(ExecFormalCalcHistoryDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    @Test
    public void table() {
        ExecCalcHistoryDTO dto = this.moduleEmpty(ExecCalcHistoryDTO.class);
        JsonResultVo<QmPage<ExecFormalCalcHistoryDO>> resultVo;

        resultVo = this.testController.table(dto);
        this.assertJsonResultVo(resultVo);
        Assert.assertEquals("查询到记录！", 0, resultVo.getData().getTotal());

        dto = this.moduleFill(ExecCalcHistoryDTO.class);
        resultVo = this.testController.table(dto);
        this.assertJsonResultVo(resultVo);
        Assert.assertEquals("查询到记录！", 0, resultVo.getData().getTotal());
    }

    @Test
    public void getFields() {
        ExecCalcHistoryDTO dto = this.moduleEmpty(ExecCalcHistoryDTO.class);
        JsonResultVo<List<String>> resultVo;
        try {
            resultVo = this.testController.getFields(dto);
            this.assertJsonResultVo(resultVo);
            Assert.assertTrue("查询到记录！", resultVo.getData().size() > 0);

            dto = this.moduleFill(ExecCalcHistoryDTO.class);
            resultVo = this.testController.getFields(dto);
            this.assertJsonResultVo(resultVo);
            Assert.assertEquals("查询到记录！", 0, resultVo.getData().size());
        } catch (Exception e) {
            Assert.assertEquals("未找到计算历史记录！", e.getMessage());
        }
    }


    @Test
    public void formalTurnBusinessBottomTable(){
        TurnBusinessBottomTableDTO turnBusinessBottomTableDTO=new TurnBusinessBottomTableDTO();
        JsonResultVo<String> resultVo;
        resultVo = this.testController.formalTurnBusinessBottomTable(turnBusinessBottomTableDTO);
        this.assertJsonResultVoFalse(resultVo);
    }
}