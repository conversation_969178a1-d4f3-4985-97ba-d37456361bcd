package com.qm.ep.rebatetri.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebatetri.domain.dto.TurnBusinessBottomTableDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.enumerate.StateEnum;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.ExecTrialCalcHistoryService;
import com.qm.ep.rebatetri.service.ExecTrialCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execTrialCalcHistory")
@Tag(name = "计算对象试算历史", description = "计算对象试算历史")
@Slf4j
public class ExecTrialCalcHistoryController extends BaseController {
    @Resource
    private ExecTrialCalcHistoryService execCalcHistoryService;

    @Resource
    private ExecTrialCalcResultService execCalcResultService;

    @Resource
    private RebateBaseRemote rebateBaseRemote;


    @Operation(summary = "查询计算对象试算历史列表", description = "查询计算对象试算历史列表")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecTrialCalcHistoryDO>> table(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(BootAppUtil.isnotNullOrEmpty(execCalcHistoryDTO.getPolicyId()), ExecTrialCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecTrialCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecTrialCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType());
        QmPage<ExecTrialCalcHistoryDO> list = execCalcHistoryService.table(queryWrapper, execCalcHistoryDTO);
        JsonResultVo<QmPage<ExecTrialCalcHistoryDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }


    @Operation(summary = "查询计算对象试算字段列表", description = "查询计算对象试算字段列表")
    @PostMapping("/fields")
    public JsonResultVo<List<String>> getFields(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecTrialCalcHistoryDO::getId, execCalcHistoryDTO.getId());

        ExecTrialCalcHistoryDO execCalcHistoryDO = execCalcHistoryService.getOne(lambdaWrapper);
        if(execCalcHistoryDO==null) {
            throw new QmException("未找到试算历史记录！");
        }
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
        ret.setData(sqlStructureVO.getFields());
        return ret;
    }


    @Operation(summary = "删除计算对象试算历史记录", description = "删除计算对象试算历史记录")
    @PostMapping("/deleteList")
    public JsonResultVo<String> deleteList(@RequestBody List<String> deleteIds) {
        if(deleteIds.isEmpty()) {
            throw new QmException("未选择删除记录！");
        }
        execCalcHistoryService.removeByIds(deleteIds);

        QmQueryWrapper<ExecTrialCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.in(ExecTrialCalcResultDO::getHistoryId, deleteIds);
        execCalcResultService.remove(lambdaWrapper);
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setMsg("删除成功");
        return ret;
    }

    @Operation(summary = "通过政策id，删除计算因子试算历史记录", description = "通过政策id，删除计算因子试算历史记录")
    @PostMapping("/deleteByHistoryPolicyId")
    public boolean deleteByHistoryPolicyId(@RequestBody String policyId) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> historyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> wrapper = historyWrapper.lambda();
        wrapper.eq(ExecTrialCalcHistoryDO::getPolicyId, policyId);
        return execCalcHistoryService.remove(wrapper);
    }



    @Operation(summary = "计算结果转业务底表", description = "计算结果转业务底表")
    @PostMapping("/turnBusinessBottomTable")
    public JsonResultVo<String> turnBusinessBottomTable(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) {
        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execTrialCalcHistoryDO.setState(StateEnum.CONVERTING);
        execCalcHistoryService.updateById(execTrialCalcHistoryDO);
        turnBusinessBottomTableDTO.setStructure(execTrialCalcHistoryDO.getSqlStructure());
        JsonResultVo<String> reportSqlStructure = rebateBaseRemote.turnBusinessBottomTable(turnBusinessBottomTableDTO);
        ExecTrialCalcHistoryDO execTrialCalcHistory = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execTrialCalcHistory.setState(StateEnum.FINISH);
        execCalcHistoryService.updateById(execTrialCalcHistory);
        return reportSqlStructure;
    }


    @Operation(summary = "获取最新的已计算完成的试算历史信息", description = "获取最新的已计算完成的试算历史信息")
    @PostMapping("/latestCompletedHistory")
    public JsonResultVo<ExecTrialCalcHistoryDO> getLatestCompletedHistory(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(StrUtil.isNotBlank(execCalcHistoryDTO.getPolicyId()), ExecTrialCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecTrialCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecTrialCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType())
                .eq(ExecTrialCalcHistoryDO::getState, StateEnum.FINISH)
                .orderByDesc(ExecTrialCalcHistoryDO::getCalcVersion).last("limit 1");
        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execCalcHistoryService.getOne(lambdaWrapper);
        JsonResultVo<ExecTrialCalcHistoryDO> ret = new JsonResultVo<>();
        ret.setData(execTrialCalcHistoryDO);
        return ret;
    }
}
