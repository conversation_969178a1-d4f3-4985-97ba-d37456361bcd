package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 看板数据
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
@Schema(description = "数据KanbanDTO")
public class KanbanDTO implements Serializable {
    @Schema(description = "经销商Code")
    private String dealerCode;
    @Schema(description = "系列")
    private String series;
    @Schema(description = "已零售(批发，开票)数量")
    private Integer realAakQty;
    @Schema(description = "计奖数量")
    private Integer totalQty;
    @Schema(description = "预计返利")
    private Double totalAmt;
    @Schema(description = "单台奖励均值")
    private Double avgAmt;
    @Schema(description = "再卖数量")
    private Integer diffQty;

    @Schema(description = "预计返利增加的值")
    private Double diffTotalAmt;
    @Schema(description = "单台奖励均值增加值")
    private Double diffAvgAmt;

    @Schema(description = "最优点点批次ID")
    private String batchId;

    @Schema(description = "目标达成Aak")
    private String aakPercent;

    @Schema(description = "推荐（1-推荐，其他-正常）")
    private String flag;

    @Schema(description = "推荐（1-手动输入，其他-正常）")
    private String manual;

    @Schema(description = "车系Map目标")
    private Integer mapCount;


    @Schema(description = "列表")
    private List<KanbanDTO> unRealList;
}
