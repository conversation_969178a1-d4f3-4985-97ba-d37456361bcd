package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO;
import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO;
import com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryDTO;
import com.qm.ep.rebatetri.domain.dto.DealerTaskDTO;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 试算结果汇总表-试算器用 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
public interface TrialCalcResultSummaryMapper extends QmBaseMapper<TrialCalcResultSummaryDO> {


    /**
     * 获取系列列表
     *
     * @param dim 昏暗
     * @return {@link List }<{@link String }>
     */
    List<String> getSeriesList(@Param("dim")String dim);

    /**
     * 按批次id获取试用cal amt总和
     *
     * @param halfId     半id
     * @param dealerCode 代理商代码
     * @return {@link Double }
     */
    Double getTrialCalAmtSumByBatchId(String halfId, String dealerCode);

    /**
     * 按数量获得不真实返佣金额
     *
     * @param qty        数量
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @param series     系列
     * @return {@link Double }
     */
    Double getUnrealRebateAmtSumByQty(int qty, String dealerCode, List<String> policyIds, String series);


    /**
     * 获得返利政策top3
     *
     * @param series     系列
     * @param dealerCode 代理商代码
     * @param batchId    批次id
     * @return {@link List }<{@link KanbanPolicyTopDTO }>
     */
    List<KanbanPolicyTopDTO> getRebatePolicyTop3(@Param("series")String series,
                                                 @Param("dealerCode")String dealerCode,
                                                 @Param("batchId")String batchId);

    /**
     * 按暗选择全部
     *
     * @param dim 昏暗
     * @return {@link List }<{@link TrialCalcResultSummaryDO }>
     */
    List<TrialCalcResultSummaryDO> selectAllByDim(String dim);

    /**
     * 按经销商代码选择dim
     *
     * @param dealerCode 代理商代码
     * @param dim        昏暗
     * @return {@link List }<{@link String }>
     */
    List<String> selectByDealerCodeDim(String dealerCode, String dim);

    /**
     * 获取经销商aak任务
     *
     * @param dealerCode 代理商代码
     * @return {@link List }<{@link DealerTaskDTO }>
     */
    List<DealerTaskDTO> getDealerAakTask(String dealerCode);


    /**
     * 获取经销商std目标
     *
     * @param dealerCode 代理商代码
     * @return {@link List }<{@link DealerTaskDTO }>
     */
    List<DealerTaskDTO> getDealerStdTask(String dealerCode);

    /**
     * 查询单个政策下经销商对应车系的返利均值（nqty=是1, batchid is null）
     *
     * @param policyId   策略id
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return {@link List }<{@link TrialCalcResultSummaryDetailPO }>
     */
    List<TrialCalcResultSummaryDetailPO> selectDetailsByUniqueKey(String policyId, String dealerCode, String series);


    /**
     * 按保单id、数量和经销商代码选择不真实返利
     *
     * @param policyId   策略id
     * @param dealerCode 代理商代码
     * @param series     系列
     * @param qty        数量
     * @return {@link String }
     */
    String selectUnrealRebateByPolicyIdAndQtyAndDealerCode(String policyId, String dealerCode, String series, int qty);

    /**
     * 获得系列真实返利,含全系和各个车系
     *
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @return {@link List }<{@link TrialCalcResultSummaryDO }>
     */
    List<TrialCalcResultSummaryDO> getSeriesRealRebate(@Param("dealerCode") String dealerCode, @Param("policyIds")List<String> policyIds);

    /**
     * 按保单id选择平均返利
     *
     * @param policyId   策略id
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return {@link Double }
     */
    Double selectAvgRebateByPolicyId(@Param("policyId")String policyId, @Param("dealerCode") String dealerCode, @Param("series") String series);

    /**
     * 通过策略ID获取最大初始化返佣
     *
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @param series     系列
     * @return {@link Double }
     */
    Double getMaxInitRebateByPolicyIds(String dealerCode, List<String> policyIds, String series);

    /**
     * 通过政策下所有车系所有数量的平均返利
     *
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @return {@link List }<{@link UnrealCarRebateSummaryDTO }>
     */
    List<UnrealCarRebateSummaryDTO> getSeriesUnrealRebateAmtSumByPolicyIds(String dealerCode, List<String> policyIds);

    /**
     * 选择实际aak计数
     *
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return int
     */
    int selectRealAakCount(@Param("dealerCode") String dealerCode, @Param("series") String series);

    /**
     * 选择aak地图计数
     *
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return int
     */
    int selectAakMapCount(@Param("dealerCode") String dealerCode, @Param("series") String series);

    /**
     * 通过政策下全系所有数量的平均返利
     *
     * @param dealerCode 代理商代码
     * @param policyIds  策略ID
     * @return {@link List }<{@link UnrealCarRebateSummaryDTO }>
     */
    List<UnrealCarRebateSummaryDTO> getAllSeriesUnrealRebateAmtSumByPolicyIds(String dealerCode, List<String> policyIds);

    /**
     * 获取真实返利数据
     *
     * @param policyId 策略id
     * @return {@link List }<{@link TrialCalcResultSummaryDO }>
     */
    List<TrialCalcResultSummaryDO> getRealRebateByPolicyId(String policyId);
}
