<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.ExecFormalCalcResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <id column="id" property="id" />
        <result column="historyId" property="historyId" />
        <result column="accountEntryStatus" property="accountEntryStatus" />
        <result column="field1" property="field1" />
        <result column="field2" property="field2" />
        <result column="field3" property="field3" />
        <result column="field4" property="field4" />
        <result column="field5" property="field5" />
        <result column="field6" property="field6" />
        <result column="field7" property="field7" />
        <result column="field8" property="field8" />
        <result column="field9" property="field9" />
        <result column="field10" property="field10" />
        <result column="field11" property="field11" />
        <result column="field12" property="field12" />
        <result column="field13" property="field13" />
        <result column="field14" property="field14" />
        <result column="field15" property="field15" />
        <result column="field16" property="field16" />
        <result column="field17" property="field17" />
        <result column="field18" property="field18" />
        <result column="field19" property="field19" />
        <result column="field20" property="field20" />
        <result column="field21" property="field21" />
        <result column="field22" property="field22" />
        <result column="field23" property="field23" />
        <result column="field24" property="field24" />
        <result column="field25" property="field25" />
        <result column="field26" property="field26" />
        <result column="field27" property="field27" />
        <result column="field28" property="field28" />
        <result column="field29" property="field29" />
        <result column="field30" property="field30" />
        <result column="field31" property="field31" />
        <result column="field32" property="field32" />
        <result column="field33" property="field33" />
        <result column="field34" property="field34" />
        <result column="field35" property="field35" />
        <result column="field36" property="field36" />
        <result column="field37" property="field37" />
        <result column="field38" property="field38" />
        <result column="field39" property="field39" />
        <result column="field40" property="field40" />
        <result column="field41" property="field41" />
        <result column="field42" property="field42" />
        <result column="field43" property="field43" />
        <result column="field44" property="field44" />
        <result column="field45" property="field45" />
        <result column="field46" property="field46" />
        <result column="field47" property="field47" />
        <result column="field48" property="field48" />
        <result column="field49" property="field49" />
        <result column="field50" property="field50" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, historyId, field1, field2, field3, field4, field5, field6, field7, field8, field9, field10, field11, field12,
    field13, field14, field15, field16, field17, field18, field19, field20, field21, field22, field23, field24, field25,
    field26, field27, field28, field29, field30, field31, field32, field33, field34, field35, field36, field37, field38,
    field39, field40, field41, field42, field43, field44, field45, field46, field47, field48, field49, field50
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.historyId,
                a.field1,
                a.field2,
                a.field3,
                a.field4,
                a.field5,
                a.field6,
                a.field7,
                a.field8,
                a.field9,
                a.field10,
                a.field11,
                a.field12,
                a.field13,
                a.field14,
                a.field15,
                a.field16,
                a.field17,
                a.field18,
                a.field19,
                a.field20,
                a.field21,
                a.field22,
                a.field23,
                a.field24,
                a.field25,
                a.field26,
                a.field27,
                a.field28,
                a.field29,
                a.field30,
                a.field31,
                a.field32,
                a.field33,
                a.field34,
                a.field35,
                a.field36,
                a.field37,
                a.field38,
                a.field39,
                a.field40,
                a.field41,
                a.field42,
                a.field43,
                a.field44,
                a.field45,
                a.field46,
                a.field47,
                a.field48,
                a.field49,
                a.field50,
                a.id,
                if(aes.dataId is null, '', '已入账') as accountEntryStatus
            from exec_formal_calc_result a
            left join account_entry_status aes on a.id = aes.dataId
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" /> where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=","> #{item} </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null"> ${k} IS NULL </when>
                        <otherwise> ${k} = #{v} </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from ( <include refid="QuerySQL" />${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.ExecFormalCalcResultDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>


    <select id="selectCalcResultSum" resultType="Map">
        select
        <foreach collection="fields" item="field" separator=",">
            ifnull(sum(${field}), 0) as ${field}
        </foreach>
        from
        exec_formal_calc_result
        where
        historyId = #{historyId}
        and ${dealerCodeField} in
        <foreach collection="dealerCodeList" item="dealerCode" open="(" close=")" separator=",">
            #{dealerCode}
        </foreach>
    </select>
    <select id="getAllId" resultType="java.lang.Integer">
        select id from exec_formal_calc_result a
        left join account_entry_status aes on a.id = aes.dataId
        where a.historyId = #{historyId} and aes.dataId is null

    </select>
</mapper>
