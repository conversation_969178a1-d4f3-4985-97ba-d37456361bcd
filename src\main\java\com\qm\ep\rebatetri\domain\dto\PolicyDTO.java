package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 政策基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "政策基础信息表")
@Data
public class PolicyDTO extends JsonParamDto {

    
    
    @Schema(description = "同上")
    private String id;
    @Schema(description = "策略 ID")
    private String policyId;
    @Schema(description = "政策代码")
    private String vpolicycode;
    @Schema(description = "政策名称")
    private String vpolicyname;
    @Schema(description = "政策描述")
    private String vpolicydesc;
    @Schema(description = "创建开始时间")
    private Date startTime;
    @Schema(description = "创建结束时间")
    private Date endTime;
    @Schema(description = "关联车型")
    private String vapplyprdt;
    @Schema(description = "关联经销商")
    private String vapplyorg;
    @Schema(description = "特殊车辆设定")
    private String vtscartype;
    @Schema(description = "完成状态")
    private List<String> vfinishstates;
    @Schema(description = "结算类型")
    private List<String> vsmttypes;
    @Schema(description = "政策类型")
    private List<String> policyTypes;
    @Schema(description = "日期类型")
    private String datetype;

    @Schema(description = "组织代码")
    private String orgCode;
    @Schema(description = "AIM版本")
    private String aimVersion;
    @Schema(description = "审核级别")
    private String auditLevel;
    @Schema(description = "搜索自我")
    private String searchSelf;

    @Schema(description = "政策时间轴-节点状态")
    private List<String> nodeStatus;
    @Schema(description = "开始时间dbegin")
    private String dbegin;
    @Schema(description = "登德")
    private String dend;
    @Schema(description = "编码vpolicycodehq")
    private String vpolicycodehq;

    @Schema(description = "用户代码")
    private String userCode;
    @Schema(description = "用户类型")
    private String userType;
    // 点击配置完成，判断是否跳过校验必填字段，为空则不跳过，为”skip"则跳过
    @Schema(description = "跳过必填校验")
    private String skipFlag;

    @Schema(description = "预估年月-bx")
    private  String predictMonth;
    @Schema(description = "品牌-bx")
    private List<String> brands;
    @Schema(description = "创建人-bx")
    private  String createByName;

    @Schema(description = "经销商编码")
    private  String dealerCode;

    @Schema(description = "跟踪状态")
    private String trailStatus;

    @Schema(description = "试算类型（aak-std-invoice）")
    private String trailType;
}