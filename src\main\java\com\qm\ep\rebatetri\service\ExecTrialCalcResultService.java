package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcResultDTO;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface ExecTrialCalcResultService extends IQmBaseService<ExecTrialCalcResultDO> {

    QmPage<ExecTrialCalcResultDO> queryCalcResult(ExecCalcResultDTO execCalcResultDTO);
    /**
     * 删除
     * @param policyId
     * @return
     */
    int deleteExecCalcResult(String policyId);

    /**
     * 动态sql 插入
     * @param map 参数
     * @return 返回
     */
    int saveBySql(Map<String, Object> map);

    /**
     * 动态sql 查询
     * @param map 参数
     * @return 返回
     */
    List<Map> getBySql(Map<String, Object> map);
}
