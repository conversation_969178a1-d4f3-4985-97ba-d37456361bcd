package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "计算类型")
public enum CalcTypeEnum {
    /**
     * 计算类型：试算
     */
    @Schema(description = "计算类型：试算") TRIAL("trial", "试算"),

    /**
     * 计算类型：正式计算
     */
    @Schema(description = "计算类型：正式计算") FORMAL("formal", "计算");

    CalcTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final String code;
    @Schema(description = "描述")
    private final String desc;

}
