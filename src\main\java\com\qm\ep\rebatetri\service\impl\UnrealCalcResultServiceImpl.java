package com.qm.ep.rebatetri.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.*;
import com.qm.ep.rebatetri.domain.dto.ExecCalcResultDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.mapper.UnrealCalcHistoryMapper;
import com.qm.ep.rebatetri.mapper.UnrealCalcResultMapper;
import com.qm.ep.rebatetri.mapper.UnrealPolicyRecordMapper;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.SystemConfigService;
import com.qm.ep.rebatetri.service.TrialCalcResultSummaryService;
import com.qm.ep.rebatetri.service.UnrealCalcResultService;
import com.qm.ep.rebatetri.service.UnrealCarRebateSummaryService;
import com.qm.ep.rebatetri.utils.CollUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UnrealCalcResultServiceImpl extends QmBaseServiceImpl<UnrealCalcResultMapper, UnrealCalcResultDO> implements UnrealCalcResultService {
    @Resource
    private TrialCalcResultSummaryService trialCalcResultSummaryService;
    @Resource
    private UnrealPolicyRecordMapper unrealPolicyRecordMapper;
    @Resource
    private PolicyMapper policyMapper;
    @Resource
    private UnrealCarRebateSummaryService unrealCarRebateSummaryService;
    @Resource
    private SystemConfigService systemConfigService;
    @Resource
    private UnrealCalcHistoryMapper unrealCalcHistoryMapper;
    @Resource
    private UnrealCalcResultMapper unrealCalcResultMapper;
    @Resource
    private RebateBaseRemote rebateBaseRemote;
    @Value("${rebate.trial.re-correct-size:300}")
    private int reCorrectSize;
    @Override
    public QmPage<UnrealCalcResultDO> queryCalcResult(ExecCalcResultDTO execCalcResultDTO) {
        LoginKeyDO userInfo = BootAppUtil.getLoginKey();
        QmQueryWrapper<UnrealCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<UnrealCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(UnrealCalcResultDO::getHistoryId, execCalcResultDTO.getHistoryId());

        UnrealCalcHistoryDO execCalcHistoryDO = unrealCalcHistoryMapper.selectById(execCalcResultDTO.getHistoryId());
        String company = systemConfigService.getValueByCode("company");
        if ("bx".equals(company)) {
            JsonResultVo<Boolean> isBxAdminVO = rebateBaseRemote.checkPersonAtTopLevel(userInfo.getOperatorId());
            if (isBxAdminVO.getCode() != 200) {
                log.error(isBxAdminVO.getMsg());
                throw new QmException("系统发生错误，请联系管理员");
            }
            boolean isBxAdmin = isBxAdminVO.getData();
            if (!isBxAdmin) {
                // 如果不是管理员
                if (execCalcHistoryDO != null) {
                    SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
                    List<String> fields = sqlStructureVO.getFields();
                    List<String> sumFields = new ArrayList<>();
                    // 查询是否包含经销商代码字段，
                    String dealerCodeField = null;
                    for (int i = 1; i <= fields.size(); i++) {
                        if ("经销商代码".equals(fields.get(i - 1))) {
                            dealerCodeField = "field" + i;
                        }
                        sumFields.add("field" + i);
                    }
                    // 如果存在经销商代码字段，则需要通过经销商代码进行过滤
                    if (CharSequenceUtil.isNotEmpty(dealerCodeField)) {
                        JsonResultVo<List<String>> leafOrgVO = rebateBaseRemote.getLeafOrg(userInfo.getOperatorId());
                        if (isBxAdminVO.getCode() != 200) {
                            log.error(isBxAdminVO.getMsg());
                            throw new QmException("系统发生错误，请联系管理员");
                        }
                        List<String> dealerCodeList = leafOrgVO.getData();
                        if (CollUtil.isNotEmpty(dealerCodeList)) {
                            queryWrapper.in(dealerCodeField, dealerCodeList);
                            QmPage<UnrealCalcResultDO> page = table(queryWrapper, execCalcResultDTO);

                            Map<String, Object> sumResult = unrealCalcResultMapper.selectCalcResultSum(execCalcResultDTO.getHistoryId(), sumFields, dealerCodeField, dealerCodeList);
                            page.setExtension(sumResult);
                            return page;
                        } else {
                            QmPage<UnrealCalcResultDO> page = new QmPage<>();
                            page.setItems(Collections.emptyList());
                            page.setExtension(Collections.emptyMap());
                            return page;
                        }
                    }
                }
            }
        }
        QmPage<UnrealCalcResultDO> list = table(queryWrapper, execCalcResultDTO);
        if (execCalcHistoryDO != null) {
            String sumResult = execCalcHistoryDO.getSumResult();
            if (BootAppUtil.isnotNullOrEmpty(sumResult)) {
                list.setExtension(JSONUtils.jsonToMap(sumResult));
            }
        }
        return list;
    }

    @Override
    public int deleteExecCalcResult(String policyId) {
        QmQueryWrapper<UnrealCalcHistoryDO> historyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<UnrealCalcHistoryDO> historylambdaWrapper = historyWrapper.lambda();
        historylambdaWrapper.eq(UnrealCalcHistoryDO::getPolicyId, policyId);
        List<UnrealCalcHistoryDO> historyList = unrealCalcHistoryMapper.selectList(historylambdaWrapper);
        List<String> historyId = new ArrayList<>();
        for (UnrealCalcHistoryDO list : historyList) {
            historyId.add(list.getId());
        }
        if (!historyId.isEmpty()) {
            QmQueryWrapper<UnrealCalcResultDO> resultWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<UnrealCalcResultDO> resultlambdaWrapper = resultWrapper.lambda();
            resultlambdaWrapper.eq(UnrealCalcResultDO::getHistoryId, historyId);
            return unrealCalcResultMapper.delete(resultlambdaWrapper);
        }
        return 0;
    }

    /**
     * 通过SQL 保存
     *
     * @param map 参数
     * @return 返回
     */
    @Override
    public int saveBySql(Map<String, Object> map) {
        return baseMapper.insertBySql(map);
    }

    /**
     * 通过SQL执行
     *
     * @param map 参数
     * @return 返回
     */
    @Override
    public List<Map> getBySql(Map<String, Object> map) {
        return baseMapper.selectBySql(map);
    }

    @Override
    public boolean deleteExecCalcResultByHistoryId(String historyId) {
        List<String> ids = unrealCalcResultMapper.selectIdsByHistoryId(historyId);
        return removeBatchByIds(ids);
    }

    @Override
    @Transactional(rollbackFor=Exception.class,propagation = Propagation.REQUIRES_NEW,isolation = Isolation.READ_UNCOMMITTED)
    @DS(DataSourceType.W)
    @SneakyThrows
    public void correctUnrealCarRebateSummary(String policyId) {

        log.info("-------------政策id-----------------:{}", policyId);

        Thread.sleep(5000);
        // 只对已经完成的政策进行处理
        List<UnrealPolicyRecordPO> list = unrealPolicyRecordMapper.selectByPolicyId(policyId, Arrays.asList("0", "1"));

        log.info("-------------list记录数-----------------:{}", list.size());

        if (CollUtil.isEmpty(list)) {
            // 获取虚拟返利数据
            List<UnrealCarRebateSummaryPO> unrealList = unrealCalcResultMapper.selectUnrealCarRebateSummaryByPolicyId(policyId);
            log.info("-------------groupedUnrealList记录数-----------------:{}", unrealList.size());
            Map<String, List<UnrealCarRebateSummaryPO>> groupedUnrealList = unrealList.stream()
                    .collect(Collectors.groupingBy(
                            temp -> temp.getDealerCode() + "-" + temp.getSeries()
                    ));
            // 获取真实返利数据
            List<TrialCalcResultSummaryDO> realList = trialCalcResultSummaryService.getRealRebateByPolicyId(policyId);
            log.info("-------------realList记录数-----------------:{}", realList.size());
            Map<String, Double> keyWithRealRebateMap = realList.stream()
                    .filter(temp -> temp.getNqty() != null && temp.getNqty() != 0)
                    .collect(Collectors.toMap(
                            temp -> temp.getDealerCode() + "-" + temp.getSeries(),
                            temp -> temp.getNamt() / temp.getNqty()
                    ));

            Map<String, Integer> keyWithRealCountMap = realList.stream()
                    .filter(temp -> temp.getNqty() != null && temp.getNqty() != 0)
                    .collect(Collectors.toMap(
                            temp -> temp.getDealerCode() + "-" + temp.getSeries(),
                            TrialCalcResultSummaryDO::getNqty
                    ));

            List<UnrealCarRebateSummaryPO> saveList = new ArrayList<>();
            List<UnrealCarRebateSummaryPO> updateList = new ArrayList<>();
            for (Map.Entry<String, List<UnrealCarRebateSummaryPO>> entry : groupedUnrealList.entrySet()) {

                List<UnrealCarRebateSummaryPO> groupedList = entry.getValue();
                groupedList.sort(Comparator.comparingInt(UnrealCarRebateSummaryPO::getNqty));
                String series = groupedList.get(0).getSeries();
                String dealerCode = groupedList.get(0).getDealerCode();
                BigDecimal currentAmt = BigDecimal.valueOf(0.0);
                for (int i = 1; i <= reCorrectSize; i++) {
                    boolean found = false;
                    for (UnrealCarRebateSummaryPO dto : groupedList) {
                        if (dto.getNqty() == i) {
                            String key = dto.getDealerCode() + "-" + dto.getSeries();

                            currentAmt = dto.getNamt();
                            Double realRebate = keyWithRealRebateMap.get(key);
                            // 确保大于真实计奖数量的返利如果小于真实返利就置为一致，避免在手动计算出现负数
                            if (i >= keyWithRealCountMap.get(key) && currentAmt.compareTo(BigDecimal.valueOf(realRebate))<0) {
                            // if (currentAmt.compareTo(BigDecimal.valueOf(realRebate))<0) {
                                dto.setDim(String.valueOf(dto.getNamt()));
                                dto.setNamt(BigDecimal.valueOf(realRebate));
                                currentAmt = BigDecimal.valueOf(realRebate);
                                dto.setNamt(BigDecimal.valueOf(realRebate));
                                dto.setVrqty(0);
                                updateList.add(dto);
                            }
                            // 确保小于于真实计奖数量的返利如果大于真实返利就置为一致，避免在手动计算出现负数
                            if (i <= keyWithRealCountMap.get(key) && currentAmt.compareTo(BigDecimal.valueOf(realRebate))>0) {
                                // if (currentAmt.compareTo(BigDecimal.valueOf(realRebate))<0) {
                                dto.setDim(String.valueOf(dto.getNamt()));
                                dto.setNamt(BigDecimal.valueOf(realRebate));
                                currentAmt = BigDecimal.valueOf(realRebate);
                                dto.setNamt(BigDecimal.valueOf(realRebate));
                                dto.setVrqty(0);
                                updateList.add(dto);
                            }


                            found = true;
                            break;
                        }
                    }
                    // 避免虚拟返利nqty出现空的情况
                    if (!found && i > 1) {
                        // 未找到，则使用上一个的平均返利构建虚拟数据
                        UnrealCarRebateSummaryPO unrealCarRebateSummaryPO = new UnrealCarRebateSummaryPO();
                        unrealCarRebateSummaryPO.setPolicyId(policyId);
                        unrealCarRebateSummaryPO.setHistoryId("");
                        unrealCarRebateSummaryPO.setDealerCode(dealerCode);
                        unrealCarRebateSummaryPO.setSeries(series);
                        unrealCarRebateSummaryPO.setNamt(currentAmt);
                        unrealCarRebateSummaryPO.setNqty(i);
                        unrealCarRebateSummaryPO.setDtstamp(LocalDateTime.now());
                        unrealCarRebateSummaryPO.setVrqty(0);
                        unrealCarRebateSummaryPO.setVrseries("");
                        unrealCarRebateSummaryPO.setBatchId(policyId + "-" + i);
                        saveList.add(unrealCarRebateSummaryPO);

                        groupedList.add(unrealCarRebateSummaryPO);

                    }
                }
            }

            // 分片大小
            int batchSize = 1000;

            // 使用 CollUtil.partition 进行分片
            List<List<UnrealCarRebateSummaryPO>> savePartitionedLists = CollUtils.partition(saveList, batchSize);

            // 现在你可以遍历 savePartitionedLists 并对每个分片进行处理
            for (List<UnrealCarRebateSummaryPO> batch : savePartitionedLists) {
                // 对每个批次执行保存操作
                unrealCarRebateSummaryService.saveBatchAsync(batch);

            }
            List<List<UnrealCarRebateSummaryPO>> updatePartitionedLists = CollUtils.partition(updateList, batchSize);

            for (List<UnrealCarRebateSummaryPO> batch : updatePartitionedLists) {
                // 对每个批次执行保存操作
                unrealCarRebateSummaryService.saveBatchUpdateAsync(batch);
            }

        }
    }


}
