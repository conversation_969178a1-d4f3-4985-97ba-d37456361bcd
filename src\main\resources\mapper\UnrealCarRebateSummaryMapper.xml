<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.UnrealCarRebateSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryPO">
        <id column="id" property="id" />
        <result column="policyId" property="policyId" />
        <result column="historyId" property="historyId" />
        <result column="dealerCode" property="dealerCode" />
        <result column="series" property="series" />
        <result column="namt" property="namt" />
        <result column="nqty" property="nqty" />
        <result column="dim" property="dim" />
        <result column="DTSTAMP" property="dtstamp" />
        <result column="vrqty" property="vrqty" />
        <result column="vrseries" property="vrseries" />
        <result column="batchId" property="batchId" />
        <result column="m_batchId" property="mBatchid" />
        <result column="q_batchId" property="qBatchid" />
        <result column="y_batchId" property="yBatchid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, policyId, historyId, dealerCode, series, namt, nqty, dim, DTSTAMP, vrqty, vrseries, batchId, m_batchId, q_batchId, y_batchId
    </sql>

</mapper>
