package com.qm.ep.rebatetri.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.ep.rebatetri.domain.bean.BusinessDataUnrealDO;
import com.qm.ep.rebatetri.mapper.BusinessDataUnrealMapper;
import com.qm.ep.rebatetri.service.BusinessdataUnrealService;
import com.qm.tds.dynamic.constant.DataSourceType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务数据底表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Service
public class BusinessdataUnrealServiceImpl extends ServiceImpl<BusinessDataUnrealMapper, BusinessDataUnrealDO> implements BusinessdataUnrealService {

    @Override
    @DS(DataSourceType.W)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveBatchTran(List<BusinessDataUnrealDO> businessDataUnrealDOList) {
        this.saveBatch(businessDataUnrealDOList);
    }

    @Override
    public void deleteByTableNameAndPolicyId(String tableName, String policyId) {
        LambdaQueryWrapper<BusinessDataUnrealDO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.eq(BusinessDataUnrealDO::getTablename,tableName);
        lambdaWrapper.eq(BusinessDataUnrealDO::getField81,policyId);
        List<BusinessDataUnrealDO> list = list(lambdaWrapper);
        if (CollUtil.isNotEmpty(list)) {
            List<String> ids = list.stream().map(BusinessDataUnrealDO::getId).collect(Collectors.toList());
            removeBatchByIds(ids);
        }

    }
}
