package com.qm.ep.rebatetri.enumerate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "状态枚举")
public enum StateEnum {
    /**
     * 状态：进行中
     */
    @Schema(description = "状态：进行中") PROCESS(0, "进行中"),

    /**
     * 状态：出错
     */
    @Schema(description = "状态：出错") ERROR(1, "出错"),

    /**
     * 状态：已完成
     */
    @Schema(description = "状态：已完成") FINISH(2, "已完成"),

    /**
     * 状态：转换中
     */
    @Schema(description = "状态：转换中") CONVERTING(3, "转换中");

    StateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Schema(description = "法典")
    @EnumValue
    @JsonValue
    private final int code;
    @Schema(description = "描述")
    private final String desc;

}
