package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.UnrealPolicyRecordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 政策试算数据待初始化表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Mapper
public interface UnrealPolicyRecordMapper extends BaseMapper<UnrealPolicyRecordPO> {

    UnrealPolicyRecordPO selectTheEarliestPolicy();

    void updateUnrealPolicyStatusById(Integer id, String status);

    List<String> selectCalMainTableName(String policyId);

    /**
     * 根据状态和政策id查询数据
     *
     * @param policyId 策略id
     * @param list     列表
     * @return {@link List }<{@link UnrealPolicyRecordPO }>
     */
    List<UnrealPolicyRecordPO> selectByPolicyId(String policyId, List<String> list);
}
