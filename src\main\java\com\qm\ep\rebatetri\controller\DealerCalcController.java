package com.qm.ep.rebatetri.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.*;
import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.ep.rebatetri.domain.vo.RebateCalDetailVO;
import com.qm.ep.rebatetri.enumerate.RebateTypeEnum;
import com.qm.ep.rebatetri.enumerate.TrailTypeEnum;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.mapper.TrailRecordMapper;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dealerCalc")
@Tag(name = "返利计算器", description = "返利计算器")
@Slf4j
@RefreshScope
public class DealerCalcController extends BaseController {

    public static final String TWO = "2";
    public static final String FOUR = "4";
    public static final String CAR = "经销商提车统计";
    public static final String INVOICE = "终端发票查询";
    @Resource
    UnrealCalcResultService unrealCalcResultService;
    @Resource
    private BusinessdataUnrealService businessdataUnrealService;

    @Resource
    private TrailRecordMapper trailRecordMapper;
    @Resource
    private PolicyMapper policyMapper;
    @Resource
    private UnrealPolicyRecordService unrealPolicyRecordService;

    @Value("${mock.dealer.code:}")
    private String mockDealerCode;


    @Resource
    private DealerCalcService dealerCalcService;

    @Resource
    private TrialCalcDataService trialCalcDataService;

    @Resource
    private TrialCalcCoreService trialCalcCoreService;

    @Resource
    private RebateBaseRemote rebateBaseRemote;
    @Resource
    private TrailBusinessCacheService trailBusinessCacheService;

    @Resource
    private VirtualCarService virtualCarService;

    @Operation(summary="返利试算车系查询", description = "返利试算车系查询")
    @GetMapping("/seriesList")
    public JsonResultVo<List<SeriesQuotaDO>> seriesList(){
        return rebateBaseRemote.seriesList();
    }

    @Operation(summary = "试算政策查询", description = "试算政策查询")
    @PostMapping("/queryPolicy")
    public JsonResultVo<QmPage<RebateQueryResponse>> queryPolicy(@RequestBody RebateQueryRequest request) {
        JsonResultVo<QmPage<RebateQueryResponse>> result = new JsonResultVo<>();
        if (StringUtils.isNotBlank(mockDealerCode)) {
            request.setDealerCode(mockDealerCode);
        }
        result.setData(dealerCalcService.queryPolicyByRecordStatus(request));
        return result;
    }


    @Operation(summary = "看板数据-前端查询", description = "看板数据-前端查询")
    @PostMapping("/kanban")
    public JsonResultVo<List<KanbanDTO>> kanban(@Valid @RequestBody RebateCalQueryRequest request) {
        JsonResultVo<List<KanbanDTO>> jsonResultVo = new JsonResultVo<>();
        List<KanbanDTO> list = trialCalcCoreService.kanban(request);
        jsonResultVo.setData(list);
        return jsonResultVo;
    }


    @Operation(summary = "看板数据-前端查询明细", description = "看板数据-前端查询明细")
    @PostMapping("/kanbanDetail")
    public JsonResultVo<RebateCalDetailVO> kanbanDetail(@Valid @RequestBody RebateCalDetailQueryRequest request) {
        return trialCalcCoreService.kanbanDetail(request);
    }

    @Operation(summary = "看板数据-手动计算返利数据", description = "看板数据-手动计算返利数据")
    @PostMapping("/manualCalc")
    public JsonResultVo<KanbanDTO> manualCalc(@RequestBody RebateManualCalRequest request) {
        JsonResultVo<KanbanDTO> jsonResultVo = new JsonResultVo<>();
        KanbanDTO dto = trialCalcCoreService.manualCalc(request);
        jsonResultVo.setData(dto);
        return jsonResultVo;
    }


    @Operation(summary = "核心步骤Final.1.制造虚拟车并初始化返利数据", description = "核心步骤Final.1.制造虚拟车并初始化返利数据")
    @PostMapping("/completeProcessAndInitRebateData")
    public JsonResultVo<String> completeProcessAndInitRebateData() {
        log.info("-------------------核心步骤Final.1.制造虚拟车并初始化返利数据开始执行------------------");
        JsonResultVo<String> data = new JsonResultVo<>();
        UnrealPolicyRecordPO recordPO = unrealPolicyRecordService.selectTheEarliestPolicy();
        // 没有需要计算的政策
        if (recordPO == null) {
            log.info("-----------------------沒有需要跑批的政策-----------");
            return data;
        }
        String policyId = recordPO.getPolicyId();
        PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
        String trailType = policyDO.getTrailType();

        log.info("-----------------------completeProcessAndInitRebateData开始执行，需要虚拟的政策id{}-----------", policyId);
        unrealPolicyRecordService.updateUnrealPolicyStatusById(recordPO.getId(), "1");
        try {

            // 核心步骤：模拟合并方案计算，逻辑一样，不过是插入的表是trail_开头的表，因为返利试算器采用了分库，不能操作主库的表
            List<BusinessDataDO> rebateSeriesVin = virtualCarService.reCalCombination(policyId);

            // 删除原有的虚拟车
            businessdataUnrealService.deleteByTableNameAndPolicyId(TrailTypeEnum.getDescByCode(trailType), policyId);

            // 核心步骤：根据上述计算结果选车并复制，并初始化返利数据
            virtualCarService.copyVinAndInitRebateDataAsync(rebateSeriesVin, recordPO, trailType);

        } catch (Exception e) {

            log.info("-----------------------completeProcessAndInitRebateData执行出错，需要虚拟的政策id{}-----------", policyId);

            unrealPolicyRecordService.updateUnrealPolicyStatusById(recordPO.getId(), e.getMessage());

            return data;
        }
        log.info("-----------------------completeProcessAndInitRebateData结束执行，需要虚拟的政策id{}-----------", policyId);

        unrealPolicyRecordService.updateUnrealPolicyStatusById(recordPO.getId(), TWO);

        return data;
    }

    @Operation(summary = "核心步骤Final.2.开始计算真实返利", description = "核心步骤Final.2.开始计算真实返利")
    @GetMapping("/trailRealRebate")
    public JsonResultVo<String> trailRealRebate() {
        log.info("-------------------开始计算真实返利，进入trailRealRebate方法-------------");
        trialCalcDataService.truncateData();

        List<String> types = Arrays.asList("aak", "std", "invoice");

        // 缓存底表数据(aak,std,发票,销售目标)
        this.cacheBusinessData(DateUtil.year(new Date()) + "");

        for (String type : types) {
            log.info("-------------------开始计算真实返利，进入trailRealRebate方法，类型为:{}-------------",type);
            // 可参与计算的政策
            List<ExecCalcDTO> execList = trialCalcDataService.getCalcPolicyByType(type);
            log.info("类型为{},可参与计算的政策为:{}",type, execList.stream().map(ExecCalcDTO::getPolicyName).collect(Collectors.toSet()));
            if (CollUtil.isEmpty(execList)) {
                log.info("可参与计算的政策为空，类型为:{}",  type);
                continue;
            }
            log.info("-------------------核心步骤Final.2.开始计算真实返利, 类型为{}, 可参与计算的政策为:{}-------------", type, execList);
            // 计算真实返利
            trialCalcCoreService.trailRealRebate(execList, type);
        }
        log.info("-------------------结束计算真实返利，退出trailRealRebate方法-------------");
        return new JsonResultVo<>();
    }

    private void cacheBusinessData(String year) {
        // 缓存实际的aak和std数据和发票数据

        trailBusinessCacheService.cacheBusinessAakData(year);
        trailBusinessCacheService.cacheBusinessStdData(year);
        trailBusinessCacheService.cacheBusinessInvoiceData(year);
        // 缓存销售目标数据
        trailBusinessCacheService.cacheBusinessAakAimData(year);
        trailBusinessCacheService.cacheBusinessStdAimData(year);
    }

    @Operation(summary = "核心步骤Final.3.试算器-二分法计算最优点", description = "核心步骤Final.3.试算器-二分法计算最优点")
    @GetMapping("/trialV2")
    public JsonResultVo<String> trialV2(String policyId) {
        log.info("-------------------开始计算最优点，进入Final.3方法-开始");
        List<String> types = Arrays.asList("aak", "std", "invoice");
        //List<String> types = Arrays.asList("invoice");
        for (String type : types) {
            log.info("-------------------开始计算最优点，进入Final.3方法-开始，执行的类型为:{}",type);
            trialCalcCoreService.calBestPoint("Y", type,policyId);
        }
        log.info("-------------------开始计算最优点，进入Final.3方法-结束");
        return new JsonResultVo<>();
    }

    @Operation(summary = "核心步骤Final.4.修正虚拟车的返利汇总数据", description = "核心步骤Final.4.试算器-修正虚拟车的返利汇总数据")
    @GetMapping("/correctUnrealCarRebateSummary")
    public JsonResultVo<String> correctUnrealCarRebateSummary(String policyId) {

        unrealCalcResultService.correctUnrealCarRebateSummary(policyId);
        return new JsonResultVo<>();
    }




    @Operation(summary = "部门绩效-查询所有经销商当月点击次数", description = "部门绩效-查询所有经销商当月点击次数")
    @PostMapping("/getTrailRecord")
    public JsonResultVo<List<TrailRecordPO>> getTrailRecord(String yearMonth) {
        log.info("-------------------部门绩效-查询所有经销商当月点击次数-{}", yearMonth);
        JsonResultVo<List<TrailRecordPO>> res = new JsonResultVo<>();
        LambdaQueryWrapper<TrailRecordPO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.eq(TrailRecordPO::getSealDate, DateUtil.format(new Date(), "yyyy-MM"));
        res.setData(trailRecordMapper.selectList(lambdaWrapper));
        return res;
    }

    @Deprecated
    @Operation(summary = "根据红旗伙伴试算配置制造虚拟车-返利试算器最早的实现思路-胡晨实现-留着备用吧！！", description = "根据红旗伙伴试算配置制造虚拟车-返利试算器最早的实现思路-胡晨实现-留着备用吧！！")
    @PostMapping("/completeProcess")
    public JsonResultVo<String> completeProcess() {
        log.info("-------------------进入构造虚拟车方法-------------");
        JsonResultVo<String> data = new JsonResultVo<>();
        virtualCarService.completeProcess();
        log.info("-------------------构造虚拟车方法结束-------------");
        return data;
    }




    @Operation(summary = "步骤Final.0.存试算器政策", description = "步骤Final.0.存试算器政策")
    @PostMapping("/saveTrailPolicy")
    public JsonResultVo<String> saveTrailPolicy(@RequestBody PolicyDTO policyDTO) {

        String policyId = policyDTO.getPolicyId();
        PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(policyId);
        log.info("-------------------policy的值----------------------{}", JSON.toJSONString(policyDO));
        String trailType=policyDO.getTrailType();

        log.info("步骤Final.0.存试算器政策开始执行，需要存入的政策id{}-,政策名称{}", policyId, policyDO.getVpolicyname());
        // 初始化实体
        UnrealPolicyRecordPO unrealPolicyRecordPO = new UnrealPolicyRecordPO();
        unrealPolicyRecordPO.setPolicyId(policyId);
        unrealPolicyRecordPO.setCreateon(LocalDateTime.now());
        unrealPolicyRecordPO.setDataStatus("0");
        unrealPolicyRecordPO.setPolicyName(policyDTO.getVpolicyname());
        // 校验结束时间和结算类型
        if (policyDO.getDend().before(new Date()) || TWO.equals(policyDO.getVsmttype()) || FOUR.equals(policyDO.getVsmttype())) {
            unrealPolicyRecordPO.setDataStatus("政策已结束或结算类型不符合，不允许存入试算器");
        }
        // 试算类型
        if(!(trailType.equals(TrailTypeEnum.AAK.getCode()) || trailType.equals(TrailTypeEnum.STD.getCode())|| trailType.equals(TrailTypeEnum.INVOICE.getCode()))){
            unrealPolicyRecordPO.setDataStatus("政策试算类型"+trailType+"不符合，不允许存入试算器");
        }
        // 备件不参与
        if (RebateTypeEnum.SPT_REBATE.getCode().equals(policyDO.getBusinessType())) {
            unrealPolicyRecordPO.setDataStatus("政策含有备件，不允许存入试算器");
        }

        // 如果没有配置返利试算器，则不执行
        RebateQueryListRequest request =new RebateQueryListRequest();
        request.setPolicyName(policyDO.getVpolicyname());
        request.setReimburseStatus(trailType);

        log.info("-------------------传递的参数是----------------------{}", JSON.toJSONString(request));

        List<RebateQueryResponse> rebateQueryResponseList = dealerCalcService.queryPolicyList(request);

        // 把rebateQueryResponseQmPage转成json字符串，log打印出来
        log.info("-------------------查询记录----------------------{}", JSON.toJSONString(rebateQueryResponseList));

        if (CollectionUtils.isEmpty(rebateQueryResponseList)) {
            unrealPolicyRecordPO.setDataStatus("政策未配置返利试算器，不允许存入试算器");
        }

        // 如果主数据源没有配置“经销商提车统计”就不参与计算
        Set<String> tableNames = unrealPolicyRecordService.selectCalMainTableName(policyId);


        if (StringUtils.containsAny(trailType, TrailTypeEnum.AAK.getCode(), TrailTypeEnum.STD.getCode()) && !tableNames.contains(CAR)) {
            unrealPolicyRecordPO.setDataStatus("政策主数据源无经销商提车统计");
        }

        if (StringUtils.containsAny(trailType, TrailTypeEnum.INVOICE.getCode()) && !tableNames.contains(INVOICE)) {
            unrealPolicyRecordPO.setDataStatus("政策主数据源无终端发票查询");
        }

        unrealPolicyRecordService.save(unrealPolicyRecordPO);
        return new JsonResultVo<>();
    }

    @Operation(summary = "步骤Final.0.删除试算器政策", description = "步骤Final.0.删除试算器政策")
    @PostMapping("/deleteTrailPolicy")
    public JsonResultVo<String> deleteTrailPolicy(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<String> data = new JsonResultVo<>();
        String policyId = policyDTO.getPolicyId();
        if (StringUtils.isNotBlank(policyId)) {
            LambdaQueryWrapper<UnrealPolicyRecordPO> lambdaWrapper = new LambdaQueryWrapper<>();
            lambdaWrapper.eq(UnrealPolicyRecordPO::getPolicyId, policyId);
            lambdaWrapper.eq(UnrealPolicyRecordPO::getDataStatus, "0");
            unrealPolicyRecordService.remove(lambdaWrapper);
        }
        return data;
    }
}
