package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
@Data
@Schema(description = "数据传输对象")
public class KanbanPolicyTopDTO implements Serializable {

    
    
    /**
     * 政策名字
     */
    @Schema(description = "政策名字")
    private String policyName;
    /**
     *返利总额度
     */
    @Schema(description = "返利总额度")
    private double totalAmt;
    /**
     * 单台返利
     */
    @Schema(description = "单台返利")
    private double avgAmt;


    @Schema(description = "策略 ID")
    private String policyId;
    @Schema(description = "底盘号VIN")
    private String vin;

    @Schema(description = "总")
    private int total;
}
