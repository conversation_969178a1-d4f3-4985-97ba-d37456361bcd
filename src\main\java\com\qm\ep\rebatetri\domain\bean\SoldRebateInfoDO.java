package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sold_rebate_info")
@Schema(description = "SoldRebateInfo对象")
public class SoldRebateInfoDO {

    @Schema(description = "数据id")
    @TableField("id")
    private Long id;

    @Schema(description = "政策id")
    @TableField("policy_id")
    private String policyId;

    @Schema(description = "车系")
    @TableField("car_series")
    private String carSeries;

    @Schema(description = "经销商编码")
    @TableField("dealer_code")
    private String dealerCode;

    @Schema(description = "统计时间范围:月month;季度quarter;年year")
    @TableField("statistic_time_range")
    private String statisticTimeRange;

    @Schema(description = "已零售")
    @TableField("aak")
    private Integer aak;

    @Schema(description = "预计返利金额")
    @TableField("expected_rebate_amount")
    private Double expectedRebateAmount;

    @Schema(description = "单台返利均值")
    @TableField("single_unit_rebate")
    private Double singleUnitRebate;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

}