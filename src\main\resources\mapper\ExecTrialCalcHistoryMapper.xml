<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.ExecTrialCalcHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <id column="id" property="id" />
        <result column="policyId" property="policyId" />
        <result column="objectId" property="objectId" />
        <result column="objectType" property="objectType" />
        <result column="execType" property="execType" />
        <result column="calcVersion" property="calcVersion" />
        <result column="state" property="state" />
        <result column="begin" property="begin" />
        <result column="end" property="end" />
        <result column="sqlStructure" property="sqlStructure" />
        <result column="log" property="log" />
        <result column="sumResult" property="sumResult" />
        <result column="executorThreadName" property="executorThreadName" />
        <result column="createon" property="createOn" />
        <result column="createby" property="createBy" />
        <result column="updateon" property="updateOn" />
        <result column="updateby" property="updateBy" />
        <result column="isDeleted" property="isDeleted" />
        <result column="recordVersion" property="recordVersion" />
        <result column="dtstamp" property="dtstamp" />
    </resultMap>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.policyId,
                a.objectId,
                a.objectType,
                a.execType,
                a.calcVersion,
                a.state,
                a.begin,
                a.end,
                TIMESTAMPDIFF(SECOND,a.begin,a.end) AS cost,
                a.sqlStructure,
                a.log,
                a.sumResult,
                a.executorThreadName,
                a.createon,
                a.createby,
                b.vtext createByName,
                a.updateon,
                a.updateby,
                c.vtext updateByName,
                a.isDeleted,
                a.recordVersion,
                a.id,
                a.dtstamp,
                a.batchId
            from exec_trial_calc_history a
             left join SYSC000_M b on a.createby = b.NMAINID
             left join SYSC000_M c on a.updateby = c.NMAINID
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" /> where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=","> #{item} </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null"> ${k} IS NULL </when>
                        <otherwise> ${k} = #{v} </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from ( <include refid="QuerySQL" />${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="getExecCalcHistory" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        select  id, policyId, objectId, objectType, execType, calcVersion, state, begin, end, sqlStructure,
            log, sumResult,executorThreadName, createon, createby, updateon, updateby, isDeleted, recordVersion,dtstamp
        from exec_trial_calc_history
        where objectId = #{calcObjectId}
    </select>
    <select id="getExecCalcHistoryByBatchId" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        select id, sqlStructure, batchId
        from exec_trial_calc_history
        where batchId = #{batchId}
    </select>
    <insert id="groupResult">
    insert into trial_calc_result_summary ( historyId, dealerCode, series, namt, nqty,dim,policyId, DTSTAMP,vrqty,vrseries,batchId,trail_type)
        select
         historyId,
         ${dealerCodeField},
         ${seriesField},
         sum(${realEField}) as namt,
        count(*) as nqty,
         #{dim} as dim,
         #{policyId} as policyId,
         sysdate() as DTSTAMP,
         count(*) as vrqty,
         #{vrseries} as vrseries,
         #{batchId} as batchId,
        '${trailType}' as trail_type
         from unreal_calc_result
        WHERE historyId = #{historyId}
        <if test="series!=null and series!= ''">
            and ${seriesField}=#{series}
        </if>
        group by ${dealerCodeField},${seriesField}
    </insert>
    <delete id="deleteByObjectType">
        delete from exec_trial_calc_history where objectType = #{objectType}
    </delete>
    <select id="getExecCalcHistoryByPolicyIdAndObjectType" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        select *
        from exec_trial_calc_history
        <where>
            <if test="policyId!=null and policyId!= ''">
                and policyId = #{policyId}
            </if>
            <if test="objectType!=null and objectType!= ''">
                and objectType = #{objectType}
            </if>
        </where>
    </select>

    <select id="selectSimpleRebateResult" resultType="com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO">
            SELECT ${dealerCodeField} as dealerCode,${seriesField} as series,${vinField} as vin
            from exec_trial_calc_result WHERE historyId = #{historyId} AND ${rebateAmountField} >= '0.00'
            GROUP BY ${dealerCodeField},${seriesField}
    </select>

    <select id="selectSimpleById" resultMap="BaseResultMap">
        SELECT * FROM exec_trial_calc_history WHERE id = #{historyId}
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT * FROM exec_trial_calc_history WHERE
            policyId = #{policyId} AND objectId = #{objectId} AND objectType = #{objectType} ORDER BY calcVersion desc limit 1
    </select>

    <select id="selectAllRebateResult" resultType="com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO">
        SELECT ${dealerCodeField} as dealerCode,${seriesField} as series,${vinField} as vin
        from exec_trial_calc_result WHERE historyId = #{historyId}
        GROUP BY ${dealerCodeField},${seriesField}
    </select>
    <select id="selectPolicyNameById" resultType="java.lang.String">
        SELECT VPOLICYNAME from policy WHERE id = #{policyId}
    </select>
    <select id="groupResultDetail">
        insert into trial_calc_result_summary_detail ( dealerCode, series, namt, nqty,dim,policyId, DTSTAMP,policyName,vin)
        select
        ${dealerField} as dealerCode,
        ${seriesField} as series,
        ${realEField} as namt,
        1 as nqty,
        #{dim} as dim,
        #{policyId} as policyId,
        sysdate() as DTSTAMP,
        #{policyName} as policyName,
        ${vinField} as vin
        from unreal_calc_result
        WHERE historyId = #{historyId}
        <if test="series!=null and series!= ''">
            and ${seriesField}=#{series}
        </if>
    </select>
</mapper>
