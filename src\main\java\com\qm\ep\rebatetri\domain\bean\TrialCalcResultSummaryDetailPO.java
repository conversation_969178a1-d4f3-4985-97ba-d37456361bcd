package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 试算结果汇总表-试算器用
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Schema(description = "试算结果汇总表-试算器用 </p>")
@Data
@TableName("trial_calc_result_summary_detail")
public class TrialCalcResultSummaryDetailPO implements Serializable {

    
    

    @Schema(description = "同上")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商代码
     */
    @Schema(description = "经销商代码")
    @TableField("dealerCode")
    private String dealerCode;

    /**
     * 车系
     */
    @Schema(description = "车系")
    @TableField("series")
    private String series;

    /**
     * 政策id
     */
    @Schema(description = "政策id")
    @TableField("policyId")
    private String policyId;

    /**
     * 政策名
     */
    @Schema(description = "政策名")
    @TableField("policyName")
    private String policyName;

    /**
     * vin码
     */
    @Schema(description = "vin码")
    @TableField("vin")
    private String vin;

    /**
     * 返利金额
     */
    @Schema(description = "返利金额")
    @TableField("namt")
    private BigDecimal namt;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @TableField("nqty")
    private Integer nqty;

    /**
     * 维度
     */
    @Schema(description = "维度")
    @TableField("dim")
    private String dim;

    @Schema(description = "批次 ID")
    @TableField("batchId")
    private String batchId;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private LocalDateTime dtstamp;

    @Schema(description = "原始 AMT")
    @TableField("originalAmt")
    private BigDecimal originalAmt;

    @Schema(description = "差异 AMT")
    @TableField("diffAmt")
    private BigDecimal diffAmt;



}
