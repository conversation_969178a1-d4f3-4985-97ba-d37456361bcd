package com.qm.ep.rebatetri.mapper;


import com.qm.ep.rebatetri.domain.bean.BusinessDataUnrealDO;
import com.qm.ep.rebatetri.domain.bean.CombinationMainDO;
import com.qm.ep.rebatetri.domain.dto.PolicyDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface BusinessDataUnrealMapper extends QmBaseMapper<BusinessDataUnrealDO> {

    int batchInsert(List<BusinessDataUnrealDO> list);

    int deleteAll();

    List<CombinationMainDO> selectCombinationByPolicyId(String policyId);

    PolicyDTO selectPolicyById(String policyId);

    void updateUnrealPolicyStatusById(String policyId,String status);

    void deleteBatchByPolicyId(@Param("tableName") String tableName, @Param("policyId") String policyId);

    void deleteRebateSummaryByPolicyId(String policyId);

}
