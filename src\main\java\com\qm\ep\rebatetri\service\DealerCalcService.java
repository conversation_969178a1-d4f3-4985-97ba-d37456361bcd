package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.dto.RebateQueryListRequest;
import com.qm.ep.rebatetri.domain.dto.RebateQueryRequest;
import com.qm.ep.rebatetri.domain.dto.RebateQueryResponse;
import com.qm.tds.api.mp.pagination.QmPage;

import java.util.List;

/**
 * 经销商计算服务
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
public interface DealerCalcService {

    /**
     * 查询参与试算的政策列表
     *
     * @param request 请求
     * @return {@link QmPage }<{@link RebateQueryResponse }>
     */
    QmPage<RebateQueryResponse> queryPolicy(RebateQueryRequest request);

    /**
     * 查询参与试算的政策列表
     * @param request
     * @return
     */
    List<RebateQueryResponse> queryPolicyList(RebateQueryListRequest request);

    /**
     * 查询参与试算的政策列表
     *
     * @param request 请求
     * @return {@link QmPage }<{@link RebateQueryResponse }>
     */
    QmPage<RebateQueryResponse> queryPolicyByRecordStatus(RebateQueryRequest request);
}
