package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO;
import com.qm.ep.rebatetri.domain.dto.KanbanDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TrialUnrealResultMapper extends QmBaseMapper<TrialUnrealResultDO> {

    /**
     * 获取看板结果
     *
     * @param dealerCode 代理商代码
     * @param series     系列
     * @param policyId   策略id
     * @return {@link List }<{@link KanbanDTO }>
     */
    List<KanbanDTO> getKanbanResult(String dealerCode, String series, String policyId);

    /**
     * 按条件列出
     *
     * @param dim        昏暗
     * @param dealerCode 代理商代码
     * @param series     系列
     * @return {@link List }<{@link TrialUnrealResultDO }>
     */
    List<TrialUnrealResultDO> listAllByCondition(String dim, String dealerCode, String series);

    /**
     * 按批次id计数
     *
     * @param batchId  批次id
     * @param policyId 策略id
     * @param series   系列
     * @return {@link Integer }
     */
    Integer countByBatchId(String batchId, String policyId, String series);

    /**
     * 按政策和经销商查询数据
     *
     * @param policyIdComma 策略id逗号
     * @param dealerCode    代理商代码
     * @return int
     */
    int selectByDimAndPolicyIdComma(@Param("policyIdComma") String policyIdComma, @Param("dealerCode") String dealerCode);

    /**
     * 根据batchId查询一条数据
     *
     * @param batchId 批次id
     * @return {@link TrialUnrealResultDO }
     */
    TrialUnrealResultDO selectOneByBatchId(String batchId);
}
