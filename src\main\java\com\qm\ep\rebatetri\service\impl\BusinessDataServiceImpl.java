package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.mapper.BusinessDataMapper;
import com.qm.ep.rebatetri.service.BusinessDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

@Service
public class BusinessDataServiceImpl implements BusinessDataService {

    @Resource
    private BusinessDataMapper businessDataMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteUnrealByPolicyId(String policyId) {
        businessDataMapper.deleteByPolicyId(policyId);
    }
}
