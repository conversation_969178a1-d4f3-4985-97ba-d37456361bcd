package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.qm.ep.rebatetri.constant.Constants;
import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.PolicyDO;
import com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO;
import com.qm.ep.rebatetri.domain.dto.CalcObjectDTO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcLogStructureDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.enumerate.CalcTypeEnum;
import com.qm.ep.rebatetri.enumerate.ExecTypeEnum;
import com.qm.ep.rebatetri.enumerate.StateEnum;
import com.qm.ep.rebatetri.mapper.ExecTrialCalcHistoryMapper;
import com.qm.ep.rebatetri.mapper.PolicyMapper;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.*;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.JSONUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecCalcServiceImpl implements ExecCalcService {
    @Autowired
    private PolicyMapper policyMapper;
    @Autowired
    private UnrealCalcResultService unrealCalcResultService;
    @Resource
    ExecTrialCalcService execTrialCalcService;

    @Resource
    ExecFormalCalcService execFormalCalcService;

    @Resource
    private ExecTrialCalcHistoryMapper execTrialCalcHistoryMapper;

    @Resource
    private ExecTrialCalcHistoryService execTrialCalcHistoryService;

    @Resource
    private ExecFormalCalcHistoryService execFormalCalcHistoryService;

    @Resource
    private ExecCalcLogService execCalcLogService;

    @Resource
    private RebateBaseRemote rebateBaseRemote;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    UnrealCalcHistoryService unrealCalcHistoryService;

    @Autowired
    @Qualifier("calTrialAsync")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 试算准备
     *
     * @param execCalcDTO 参数
     * @return 返回
     */
    @Override
    public String prepareCalc(ExecCalcDTO execCalcDTO) {
        String historyId = "";
        switch (execCalcDTO.getCalcType()) {
            case TRIAL:
                historyId = execTrialCalcService.prepareCalc(execCalcDTO);
                break;
            case FORMAL:
                historyId = execFormalCalcService.prepareCalc(execCalcDTO);
                break;
            default:
                break;
        }
        return historyId;
    }

    /**
     * 发起计算对象试算
     *
     * @param historyId 参数
     */
    @Override
    public void execCalc(ExecCalcDTO execCalcDTO, String historyId) throws InterruptedException {
        switch (execCalcDTO.getCalcType()) {
            case TRIAL:
                execTrialCalcService.execCalc(execCalcDTO, historyId);
                break;
            case FORMAL:
                execFormalCalcService.execCalc(historyId);
                break;
            default:
                break;
        }
    }

    /**
     * 计算的统一包裹
     *
     * @param execCalcDTO 参数
     * @return 返回
     */
    @Override
    public JsonResultVo<Object> startCalc(ExecCalcDTO execCalcDTO) {
        JsonResultVo<Object> ret = new JsonResultVo<>();
        String historyId = "";
        String lockKey = CollUtil.join(Arrays.asList(execCalcDTO.getPolicyId(), execCalcDTO.getObjectId(), execCalcDTO.getObjectType(), execCalcDTO.getCalcType()), ":");
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(40, TimeUnit.SECONDS)) {
                // 业务处理
                historyId = prepareCalc(execCalcDTO);
            } else {
                log.error("正在生成 {} 历史版本！请稍后重试！", lockKey);
            }
        } catch (InterruptedException e) {
            log.error("准备计算历史记录出错！", e);
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        if (StrUtil.isNotBlank(historyId)) {
            try {
                execCalc(execCalcDTO, historyId);
                ret.setMsg(execCalcDTO.getCalcType().getDesc() + "已发起");
            } catch (Exception e) {
                String errorLog = execCalcDTO.getCalcType().getDesc() + "出错：" + e.getMessage();
                log.error(errorLog);
                switch (execCalcDTO.getCalcType()) {
                    case TRIAL:
                        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execTrialCalcHistoryService.getById(historyId);
                        execTrialCalcHistoryDO.setLog(execCalcLogService.append(execTrialCalcHistoryDO.getLog(),
                                ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                        execTrialCalcHistoryDO.setState(StateEnum.ERROR);
                        execTrialCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                        execTrialCalcHistoryService.updateById(execTrialCalcHistoryDO);
                        break;
                    case FORMAL:
                        ExecFormalCalcHistoryDO execFormalCalcHistoryDO = execFormalCalcHistoryService.getById(historyId);
                        execFormalCalcHistoryDO.setLog(execCalcLogService.append(execFormalCalcHistoryDO.getLog(),
                                ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                        execFormalCalcHistoryDO.setState(StateEnum.ERROR);
                        execFormalCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                        execFormalCalcHistoryService.updateById(execFormalCalcHistoryDO);
                        break;
                    default:
                        break;
                }

                ret.setMsgErr(errorLog);
                Thread.currentThread().interrupt();
            }
        } else {
            ret.setMsgErr("生成历史记录出错！请稍后重试！");
        }

        return ret;
    }

    /**
     * 计算所有符合条件的政策的阶梯
     */
    @Override
    @DS(DataSourceType.W)
    public void calcLadder() {
        JsonResultVo<List<ExecCalcDTO>> ret = rebateBaseRemote.getLadderExecCalcList();
        for (ExecCalcDTO execCalcDTO : ret.getData()) {
            execCalcDTO.setExecType(ExecTypeEnum.AUTO);
            execCalcDTO.setCalcType(CalcTypeEnum.FORMAL);
            startCalc(execCalcDTO);
        }
    }

    @Override
    public void batchExecCalc(List<ExecCalcDTO> execCalcDTOS) {

        // policyId-DealerCode 唯一
        for (ExecCalcDTO execCalcDTO : execCalcDTOS) {
            execCalcDTO.setVflag(1);
            this.startCalcForUnreal(execCalcDTO);

        }
        // 创建一个 CompletableFuture 数组
        // java.util.concurrent.CompletableFuture<?>[] futures = execCalcDTOS.stream()
        //         .map(calExecCal ->{
        //             calExecCal.setVflag(1);
        //             return CompletableFuture.runAsync(() -> this.startCalcForUnreal(calExecCal), threadPoolTaskExecutor);
        //                 }
        //
        //         )
        //         .toArray(CompletableFuture[]::new);
        // CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
        // allOf.join();

    }


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void startCalcForUnreal(ExecCalcDTO execCalcDTO) {

        // 获取动态SQL并初始化一条计算历史
        String historyId = execTrialCalcService.prepareCalcForUnreal(execCalcDTO);

        log.info("------------当前historyId-----------" + historyId);

        if (StrUtil.isNotBlank(historyId)) {
            try {
                execTrialCalcService.execCalcForUnreal(execCalcDTO, historyId);
                // 清空计算结果
                unrealCalcResultService.deleteExecCalcResultByHistoryId(historyId);
                // 对虚拟返利进行修正，这里有一个问题就是虚拟返利计算结果有时候存在问题，例如实际计奖数量5台，但是虚拟返利1-5可能为0，这时候需要修正。并且需要将虚拟返利的最大点进行扩展
                unrealCalcResultService.correctUnrealCarRebateSummary(execCalcDTO.getPolicyId());

            } catch (Exception e) {
                String errorLog = execCalcDTO.getCalcType().getDesc() + "出错：" + e.getMessage();
                log.info("出错的历史记录id:{}", historyId);
                UnrealCalcHistoryDO unrealTrialCalcHistoryDO = unrealCalcHistoryService.getById(historyId);
                unrealTrialCalcHistoryDO.setLog(execCalcLogService.append(unrealTrialCalcHistoryDO.getLog(),
                        ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                unrealTrialCalcHistoryDO.setState(StateEnum.ERROR);
                unrealTrialCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                unrealCalcHistoryService.updateById(unrealTrialCalcHistoryDO);

                Thread.currentThread().interrupt();
            }
        } else {
            log.error("生成历史记录出错！请稍后重试！");
        }

    }


    @Override
    public String startCalcV3(ExecCalcDTO execCalcDTO) {
        String historyId = "";
        String lockKey = CollUtil.join(Arrays.asList(execCalcDTO.getPolicyId(), execCalcDTO.getObjectId(), execCalcDTO.getObjectType(), execCalcDTO.getCalcType()), ":");
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(40, TimeUnit.SECONDS)) {
                // 业务处理
                historyId = prepareCalc(execCalcDTO);
            } else {
                log.error("正在生成 {} 历史版本！请稍后重试！", lockKey);
            }
        } catch (InterruptedException e) {
            log.error("准备计算历史记录出错！", e);
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        if (StrUtil.isNotBlank(historyId)) {
            try {
                execCalc(execCalcDTO, historyId);
            } catch (Exception e) {
                String errorLog = execCalcDTO.getCalcType().getDesc() + "出错：" + e.getMessage();
                log.error(errorLog);
                switch (execCalcDTO.getCalcType()) {
                    case TRIAL:
                        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execTrialCalcHistoryMapper.selectById(historyId);
                        execTrialCalcHistoryDO.setLog(execCalcLogService.append(execTrialCalcHistoryDO.getLog(),
                                ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                        execTrialCalcHistoryDO.setState(StateEnum.ERROR);
                        execTrialCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                        execTrialCalcHistoryMapper.updateById(execTrialCalcHistoryDO);
                        break;
                    case FORMAL:
                        ExecFormalCalcHistoryDO execFormalCalcHistoryDO = execFormalCalcHistoryService.getById(historyId);
                        execFormalCalcHistoryDO.setLog(execCalcLogService.append(execFormalCalcHistoryDO.getLog(),
                                ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                        execFormalCalcHistoryDO.setState(StateEnum.ERROR);
                        execFormalCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                        execFormalCalcHistoryService.updateById(execFormalCalcHistoryDO);
                        break;
                    default:
                        break;
                }

                Thread.currentThread().interrupt();
            }
        }

        return historyId;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void batchExecCalcV3(ExecCalcDTO execCalcDTO) {
        PolicyDO policyDO = policyMapper.inquirePolicyByPolicyId(execCalcDTO.getPolicyId());
        // 获取动态SQL并初始化一条计算历史
        CalcObjectDTO calcObjectDTO = new CalcObjectDTO();
        calcObjectDTO.setPolicyId(execCalcDTO.getPolicyId());
        calcObjectDTO.setObjectId(execCalcDTO.getObjectId());
        calcObjectDTO.setObjectType(execCalcDTO.getObjectType());
        calcObjectDTO.setAbbreviated(true);
        calcObjectDTO.setVr(execCalcDTO.getVr());
        calcObjectDTO.setVflag(execCalcDTO.getVflag());
        calcObjectDTO.setDealerSeriesQty(execCalcDTO.getDealerSeriesQty());
        calcObjectDTO.setDealerCode(execCalcDTO.getDealerCode());
        calcObjectDTO.setLimit(execCalcDTO.getLimit());
        calcObjectDTO.setTrailType(policyDO.getTrailType());
        JsonResultVo<SqlStructureVO> sqlStructure = rebateBaseRemote.getSqlStructure3(calcObjectDTO);

        // log.info("SqlStructure:{}", sqlStructure);
        UnrealCalcHistoryDO unrealCalcHistoryDO = new UnrealCalcHistoryDO();
        unrealCalcHistoryDO.setPolicyId(execCalcDTO.getPolicyId());
        unrealCalcHistoryDO.setObjectId(execCalcDTO.getObjectId());
        unrealCalcHistoryDO.setObjectType(execCalcDTO.getObjectType());
        unrealCalcHistoryDO.setExecType(ExecTypeEnum.AUTO);
        unrealCalcHistoryDO.setState(StateEnum.PROCESS);
        unrealCalcHistoryDO.setBegin(DateUtils.getSysdateTime());
        unrealCalcHistoryDO.setSqlStructure(JSONUtils.beanToJson(sqlStructure.getData()));
        unrealCalcHistoryDO.setBatchId(execCalcDTO.getBatchId());

        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("创建试算任务").build());
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行SQL：" + sqlStructure.getData().getTemporaryTableSqlFormatted() + "\n\n" + sqlStructure.getData().getSqlFormatted()).build());

        unrealCalcHistoryDO.setLog(execCalcLogService.append(null, execCalcLogStructures));
        unrealCalcHistoryDO.setDtstamp(DateUtils.getSysdateTime());
        unrealCalcHistoryService.save(unrealCalcHistoryDO);

        String historyId = unrealCalcHistoryDO.getId();
        log.info("-------------batchExecCalcV3.historyId:{}-----------", historyId);

        if (StrUtil.isNotBlank(historyId)) {
            try {
                execTrialCalcService.execCalcForUnrealV3(execCalcDTO, historyId);
                // 清空计算结果
                unrealCalcResultService.deleteExecCalcResultByHistoryId(historyId);
            } catch (Exception e) {

                String errorLog = execCalcDTO.getCalcType().getDesc() + "出错：" + e.getMessage();
                log.error(errorLog);

                UnrealCalcHistoryDO unrealTrialCalcHistoryDO = unrealCalcHistoryService.getById(historyId);
                unrealTrialCalcHistoryDO.setLog(execCalcLogService.append(unrealTrialCalcHistoryDO.getLog(),
                        ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                unrealTrialCalcHistoryDO.setState(StateEnum.ERROR);
                unrealTrialCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                unrealCalcHistoryService.updateById(unrealTrialCalcHistoryDO);

                Thread.currentThread().interrupt();

            }
        }

    }
}
