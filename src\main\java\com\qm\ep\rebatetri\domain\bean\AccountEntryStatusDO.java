package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("account_entry_status")
@Schema(description = "计算结果入账状态对象")
public class AccountEntryStatusDO implements Serializable {
    
    

    @Schema(description = "对应exec_formal_calc_result的id")
    @TableField("dataId")
    private Long dataId;

    @Schema(description = "对应exec_formal_calc_history的id")
    @TableField("uniqueKey")
    private String uniqueKey;

}
