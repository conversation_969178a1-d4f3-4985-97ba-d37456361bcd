package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;

import java.util.List;

/**
 * <p>
 * 试算结果汇总表-试算器用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public interface TrialCalcResultSummaryDetailService extends IService<TrialCalcResultSummaryDetailPO> {

    /**
     * 按batchId查询最优点数据列表
     *
     * @param batchId 批次id
     * @return {@link List }<{@link TrialCalcResultSummaryDetailPO }>
     */
    List<TrialCalcResultSummaryDetailPO> selectListByBatchId(String batchId);

    /**
     * 获取经销商平均返利排名前3的返利数据
     *
     * @param series     系列
     * @param dealerCode 代理商代码
     * @return {@link List }<{@link KanbanPolicyTopDTO }>
     */
    List<KanbanPolicyTopDTO> getKanbanPolicyTop(String series, String dealerCode);
}
