package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.ReportDataDO;
import com.qm.ep.rebatetri.domain.dto.ReportDataDTO;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface ReportDataService extends IQmBaseService<ReportDataDO> {

    QmPage<ReportDataDO> queryTable(String tenantName, ReportDataDTO paramDTO);

    void deleteData(String tenantName, String reportId);

}
