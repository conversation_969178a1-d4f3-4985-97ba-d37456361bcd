package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.TrailBusinessCachePO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface TrailBusinessCacheService extends IService<TrailBusinessCachePO> {

    @Async("realRebateCalAsync")
    void cacheBusinessAakData(String year);
    @Async("realRebateCalAsync")
    void cacheBusinessStdData(String year);
    @Async("realRebateCalAsync")
    void cacheBusinessInvoiceData(String year);
    @Async("realRebateCalAsync")
    void cacheBusinessAakAimData(String year);
    @Async("realRebateCalAsync")
    void cacheBusinessStdAimData(String year);

    Integer getActualAakStdInvoiceCountByPolicyIds(List<String> policyIds, String dealerCode, String series);

    /**
     * 获取对应政策周期下的Aak、Std目标数据（区分经销商）
     *
     * @param policyIds  政策ID列表
     * @param dealerCode 经销商代码
     * @param series     系列
     * @return int
     */
    Integer getAakStdMap(List<String> policyIds, String dealerCode, String series);

    /**
     * 获取对应政策周期下的Aak、Std目标数据（不区分经销商）
     *
     * @param policyIds 策略ID
     * @param series    系列
     * @return int
     */
    Integer getMaxAakStdMap(List<String> policyIds, String series);
}
