package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExecCalcService {

    /**
     * 试算准备
     * @param execCalcDTO 参数
     * @return 返回
     */
    String prepareCalc(ExecCalcDTO execCalcDTO);

    /**
     * 发起计算对象试算

     * @throws InterruptedException 异常
     */
    void execCalc(ExecCalcDTO execCalcDTO, String historyId) throws InterruptedException;

    /**
     * 计算的包裹，统一
     * @param execCalcDTO 参数
     * @return 返回
     */
    JsonResultVo<Object> startCalc(ExecCalcDTO execCalcDTO);

    /**
     * 计算所有符合条件的政策的阶梯
     */
    // @Async("execCalcAsync")
    void calcLadder();


   // @Async("execCalcAsync")
    void batchExecCalc(List<ExecCalcDTO> execCalcDTOS);

    @Async("realRebateCalAsync")
    void startCalcForUnreal(ExecCalcDTO execCalcDTO);


    String startCalcV3(ExecCalcDTO execCalcUnrealDTO);

    @Async("unrealRebateCalcAsync")
    void batchExecCalcV3(ExecCalcDTO execCalcDTO);
}
