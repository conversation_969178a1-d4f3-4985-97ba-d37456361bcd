package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO;
import com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO;
import com.qm.ep.rebatetri.mapper.TrialUnrealResultMapper;
import com.qm.ep.rebatetri.mapper.UnrealCalcHistoryMapper;
import com.qm.ep.rebatetri.service.UnrealCalcHistoryService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class UnrealCalcHistoryServiceImpl extends QmBaseServiceImpl<UnrealCalcHistoryMapper, UnrealCalcHistoryDO> implements UnrealCalcHistoryService {
}
