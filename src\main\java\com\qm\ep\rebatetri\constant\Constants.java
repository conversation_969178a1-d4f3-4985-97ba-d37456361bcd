package com.qm.ep.rebatetri.constant;

/**
 * 常量类
 * <AUTHOR>
 */
public class Constants {
    public static final String AAK = "aak";
    public static final String STD = "std";
    public static final String INVOICE = "invoice";
    public static final String SERIES = "系列";
    public static final String DEALER_CODE = "经销商代码";
    public static final String VIN = "VIN码";
    public static final String TYPE = "类型";
    public static final String ALL_SERIES = "全系";
    public static final String ERROR_STATUS = "error";
    public static final String SUCCESS_STATUS = "success";
    public static final String RESULT_MAX_AMOUNT = "exec_calc_result_record_max_amount";
    public static final String COMMA = ",";
    public static final int TWO = 2;
    public static final String LINE = "-";
}
