package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Schema(description = "数据DTO")
@Getter
@Setter
@TableName("trail_record")
public class TrailRecordPO implements Serializable {

    
    

    @Schema(description = "同上")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "密封日期")
    private String sealDate;

    @Schema(description = "经销商代码")
    private String dealerCode;


}
