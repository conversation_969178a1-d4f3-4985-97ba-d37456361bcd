package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 试算结果汇总表-试算器用 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Mapper
public interface TrialCalcResultSummaryDetailMapper extends BaseMapper<TrialCalcResultSummaryDetailPO> {

    /**
     * 按批次id选查询明细数据
     *
     * @param batchId 批次id
     * @return {@link List }<{@link TrialCalcResultSummaryDetailPO }>
     */
    List<TrialCalcResultSummaryDetailPO> selectListByBatchId(String batchId);


    /**
     * 获取经销商平均返利排名前3的返利数据
     *
     * @param series     系列
     * @param dealerCode 代理商代码
     * @return {@link List }<{@link KanbanPolicyTopDTO }>
     */
    List<KanbanPolicyTopDTO> getKanbanPolicyTop(String series, String dealerCode);
}
