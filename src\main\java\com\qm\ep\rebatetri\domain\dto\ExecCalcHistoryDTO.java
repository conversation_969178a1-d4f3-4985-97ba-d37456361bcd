package com.qm.ep.rebatetri.domain.dto;

import com.qm.ep.rebatetri.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatetri.enumerate.CalcTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "计算对象计算历史信息")
@Data
public class ExecCalcHistoryDTO extends JsonParamDto {

    
    

    @Schema(description = "同上")
    private String id;

    @Schema(description = "政策ID")
    private String policyId;

    @Schema(description = "计算对象ID")
    private String objectId;

    @Schema(description = "计算对象类型")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "计算类型")
    private CalcTypeEnum calcType;

    @Schema(description = "试算版本")
    private String calcVersion;

}