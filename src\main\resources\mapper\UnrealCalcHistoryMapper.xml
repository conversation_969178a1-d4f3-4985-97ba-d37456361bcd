<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.UnrealCalcHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <id column="id" property="id"/>
        <result column="policyId" property="policyId"/>
        <result column="objectId" property="objectId"/>
        <result column="objectType" property="objectType"/>
        <result column="execType" property="execType"/>
        <result column="calcVersion" property="calcVersion"/>
        <result column="state" property="state"/>
        <result column="begin" property="begin"/>
        <result column="end" property="end"/>
        <result column="sqlStructure" property="sqlStructure"/>
        <result column="log" property="log"/>
        <result column="sumResult" property="sumResult"/>
        <result column="executorThreadName" property="executorThreadName"/>
        <result column="createon" property="createOn"/>
        <result column="createby" property="createBy"/>
        <result column="updateon" property="updateOn"/>
        <result column="updateby" property="updateBy"/>
        <result column="isDeleted" property="isDeleted"/>
        <result column="recordVersion" property="recordVersion"/>
        <result column="dtstamp" property="dtstamp"/>
    </resultMap>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select a.policyId,
                     a.objectId,
                     a.objectType,
                     a.execType,
                     a.calcVersion,
                     a.state,
                     a.begin,
                     a.end,
                     TIMESTAMPDIFF(SECOND,a.begin, a.end) AS cost,
                     a.sqlStructure,
                     a.log,
                     a.sumResult,
                     a.executorThreadName,
                     a.createon,
                     a.createby,
                     a.updateon,
                     a.updateby,
                     a.isDeleted,
                     a.recordVersion,
                     a.id,
                     a.dtstamp,
                     a.batchId
              from unreal_calc_history a

    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="getUnrealCalcHistory" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        select id,
               policyId,
               objectId,
               objectType,
               execType,
               calcVersion,
               state, begin, end, sqlStructure,
            log, sumResult, executorThreadName, createon, createby, updateon, updateby, isDeleted, recordVersion, dtstamp
        from unreal_calc_history
        where objectId = #{calcObjectId}
    </select>
    <select id="getUnrealCalcHistoryByBatchId" resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        select id, sqlStructure, batchId
        from unreal_calc_history
        where batchId = #{batchId}
    </select>
    <insert id="insertResultSummary">
        insert into trial_calc_result_summary ( historyId, dealerCode, series, namt, nqty,dim,policyId,
        DTSTAMP,vrqty,vrseries,batchId)
        select
        historyId,
        ${dealerCodeField},
        ${seriesField},
        sum(${realEField}) as namt,
        0 as nqty,
        #{dim} as dim,
        #{policyId} as policyId,
        sysdate() as DTSTAMP,
        #{vrqty} as vrqty,
        #{vrseries} as vrseries,
        #{batchId} as batchId
        from unreal_calc_result
        WHERE historyId = #{historyId}
        <if test="series!=null and series!= ''">
            and ${seriesField}=#{series}
        </if>
        group by ${dealerCodeField},${seriesField}
    </insert>
    <delete id="deleteByObjectType">
        delete
        from unreal_calc_history
        where objectType = #{objectType}
    </delete>
    <select id="getUnrealCalcHistoryByPolicyIdAndObjectType"
            resultType="com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO">
        select *
        from unreal_calc_history
        <where>
            <if test="policyId!=null and policyId!= ''">
                and policyId = #{policyId}
            </if>
            <if test="objectType!=null and objectType!= ''">
                and objectType = #{objectType}
            </if>
        </where>
    </select>
    <select id="getHistoryIdByBatchId" resultType="java.lang.String">
        select id from unreal_calc_history where batchId = #{batchId}

        <if test="policyIds != null and  policyIds.size>0">
            and policyId in
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>
    <select id="selectSimpleById" resultType="com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO">
        SELECT * FROM unreal_calc_history WHERE id = #{historyId}
    </select>
    <select id="selectAakAndStdRebateResult" resultType="com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO">
        SELECT ${dealerCodeField} as dealerCode,${seriesField} as series,${vinField} as vin
        from unreal_calc_result WHERE historyId = #{historyId} AND ${rebateAmountField} >= '0'
        GROUP BY ${dealerCodeField},${seriesField}
    </select>
    <select id="selectInvoiceRebateResult" resultType="com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO">
        SELECT ${dealerCodeField} as dealerCode,${seriesField} as series,${vinField} as vin
        from unreal_calc_result WHERE historyId = #{historyId} AND ${rebateAmountField} >= '0'
        GROUP BY ${dealerCodeField},${seriesField}
    </select>
</mapper>
