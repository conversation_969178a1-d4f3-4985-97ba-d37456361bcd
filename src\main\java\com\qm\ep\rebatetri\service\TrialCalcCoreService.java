package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.ep.rebatetri.domain.vo.RebateCalDetailVO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

/**
 * 试算器服务
 * <AUTHOR>
 */
public interface TrialCalcCoreService {


    /**
     * cal最佳点
     *
     * @param dim      昏暗
     * @param type     类型
     * @param policyId 策略id
     */
    void calBestPoint(String dim, String type, String policyId);


    /**
     * 返利试算器看板
     *
     * @param request 请求
     * @return {@link List }<{@link KanbanDTO }>
     */
    List<KanbanDTO> kanban(RebateCalQueryRequest request);


    /**
     * 看板详细信息
     *
     * @param request 请求
     * @return {@link JsonResultVo }<{@link RebateCalDetailVO }>
     */
    JsonResultVo<RebateCalDetailVO> kanbanDetail(RebateCalDetailQueryRequest request);


    /**
     * 获取经销商年度aak任务
     *
     * @param year 年
     * @return {@link Map }<{@link String },{@link Integer }>
     */
    Map<String,Integer> getDealerYearAakTask(String year);


    /**
     * 获得经销商去年所有系列aak的Map目标
     *
     * @return int
     */
    int getDealerLastYearAllSeriesAakMaxTask();


    /**
     * 计算真实返利
     *
     * @param execCalList exec计算数据
     * @param type         类型
     */
    void trailRealRebate(List<ExecCalcDTO> execCalList, String type);


    /**
     * 最优返利计算
     *
     * @param dim        昏暗
     * @param dealerCode 代理商代码
     * @param calExecCal cal执行官
     */
    @Async("bestRebateCalAsync")
    void trialCalByDealerCode(String dim, String dealerCode, List<ExecCalcDTO> calExecCal);


    /**
     * 手动计算
     *
     * @param request 请求
     * @return {@link KanbanDTO }
     */
    KanbanDTO manualCalc(RebateManualCalRequest request);
}
