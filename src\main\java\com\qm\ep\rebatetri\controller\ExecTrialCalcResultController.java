package com.qm.ep.rebatetri.controller;

import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcResultDTO;
import com.qm.ep.rebatetri.service.ExecTrialCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execTrialCalcResult")
@Tag(name = "计算对象试算结果", description = "计算对象试算结果")
public class ExecTrialCalcResultController extends BaseController {

    @Resource
    private ExecTrialCalcResultService execCalcResultService;


    @Operation(summary = "查询计算对象试算结果列表", description = "查询计算对象试算结果列表")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecTrialCalcResultDO>> table(@RequestBody ExecCalcResultDTO execCalcResultDTO) {
        JsonResultVo<QmPage<ExecTrialCalcResultDO>> ret = new JsonResultVo<>();
        QmPage<ExecTrialCalcResultDO> page = execCalcResultService.queryCalcResult(execCalcResultDTO);
        ret.setData(page);
        return ret;
    }

    @Operation(summary = "通过政策id，删除计算因子试算结果记录", description = "通过政策id，删除计算因子试算结果记录")
    @PostMapping("/deleteByResultPolicyId")
    public int deleteByResultPolicyId(@RequestBody String policyId) {
        return execCalcResultService.deleteExecCalcResult(policyId);
    }
}
