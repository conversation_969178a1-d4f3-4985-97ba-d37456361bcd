package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据业务目标DTO")
@Data
public class AimDTO extends JsonParamDto {

    
    

    @Schema(description = "同上")
    private String id;
    @Schema(description = "组织代码")
    private String orgCode;
    @Schema(description = "AIM版本")
    private String aimVersion;

    @Schema(description = "详细信息 ID")
    private String detailId;
}