package com.qm.ep.rebatetri.config;

import com.qm.ep.rebatetri.ds.decorator.ContextDecorator;
import com.qm.ep.rebatetri.monitor.annotation.Monitor;
import com.qm.ep.rebatetri.monitor.threadpool.annotation.MonitorThreadPool;
import com.qm.ep.rebatetri.monitor.threadpool.executor.MonitoredThreadPoolTaskExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@Monitor
public class AsyncScheduledTaskConfig {

    @Value("${task.execution.pool.core-size:10}")
    private int corePoolSize;
    @Value("${task.execution.pool.max-size:100}")
    private int maxPoolSize;
    @Value("${task.execution.pool.queue-capacity:1000}")
    private int queueCapacity;
    @Value("${task.execution.thread-name-prefix:task-}")
    private String namePrefix;
    @Value("${task.execution.pool.keep-alive:10}")
    private int keepAliveSeconds;

    @Bean
    @MonitorThreadPool("1.制造虚拟车线程池")
    public Executor unrealCarExecutor() {
        MonitoredThreadPoolTaskExecutor executor = new MonitoredThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "execCalc-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        /*
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean
    @MonitorThreadPool("2.虚拟车递增返利试算线程池")
    public Executor unrealRebateCalcAsync() {
        MonitoredThreadPoolTaskExecutor executor = new MonitoredThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "unrealRebateCalcAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        /*
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean
    @MonitorThreadPool("3.真实返利线程池")
    public Executor realRebateCalAsync() {
        MonitoredThreadPoolTaskExecutor executor = new MonitoredThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "realRebateCalAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        /*
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }



    @Bean
    @MonitorThreadPool("正式计算线程池")
    public Executor execFormalCalcAsync() {
        MonitoredThreadPoolTaskExecutor executor = new MonitoredThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "execFormalCalc-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        /*
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor execReportCalcAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "execReportCalc-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());
        //拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor autoEnterAccountAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "autoEnterAccountAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "calTrialAsync")
    public ThreadPoolTaskExecutor calTrialAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "calTrialAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());
        //拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }


}