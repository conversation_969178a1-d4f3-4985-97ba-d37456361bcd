<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.UnrealPolicyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.UnrealPolicyRecordPO">
        <id column="id" property="id" />
        <result column="policyId" property="policyId" />
        <result column="createon" property="createon" />
        <result column="data_status" property="dataStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, policyId, createon, data_status
    </sql>
    <update id="updateUnrealPolicyStatusById">
        update unreal_policy_record set data_status = #{status} WHERE id = #{id}
    </update>
    <select id="selectTheEarliestPolicy" resultType="com.qm.ep.rebatetri.domain.bean.UnrealPolicyRecordPO">
        select * from unreal_policy_record where data_status in ('0') order by id limit 1
    </select>
    <select id="selectCalMainTableName" resultType="java.lang.String">
        SELECT maintable FROM `calfactormain` WHERE policyid = #{policyId}
    </select>

    <select id="selectByPolicyId" resultMap="BaseResultMap">
        select  * from unreal_policy_record where policyId = #{policyId}
        <if test="list != null and list.size > 0">
            and data_status in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
