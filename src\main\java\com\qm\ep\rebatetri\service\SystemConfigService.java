package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.SystemConfigDO;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface SystemConfigService extends IQmBaseService<SystemConfigDO> {
    /**
     * 通过参数代码获取参数值
     * @param code 参数
     * @return 返回
     */
    String getValueByCode(String code);
}
