package com.qm.ep.rebatetri.domain.dto;

import com.qm.ep.rebatetri.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatetri.enumerate.CalcTypeEnum;
import com.qm.ep.rebatetri.enumerate.ExecTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */


/**
 * 计算对象，policyId+objectId+dealerCode构成唯一
 */
@Schema(description = "计算对象执行计算")
@Data
public class ExecCalcDTO {
    @Schema(description = "政策名")
    private String policyName;


    @Schema(description = "政策ID(必需)")
    private String policyId;

    @Schema(description = "计算对象ID(必需)")
    private String objectId;

    @Schema(description = "合并方案名称")
    private String objectName;


    @Schema(description = "计算对象类型(必需)")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "计算类型：正式计算、试算(必需)")
    private CalcTypeEnum calcType;

    @Schema(description = "执行类型：自动、人工")
    private ExecTypeEnum execType;

    @Schema(description = "经销商代码(必需)")
    private String dealerCode;

    @Schema(description = "是否自动转底表")
    private String autoTurnTable;

    @Schema(description = "底表名称")
    private String tableName;

    @Schema(description = "是否自动入账")
    private String autoEnterAccount;

    @Schema(description = "试算器批量执行ID(必需)")
    private String batchId;

    @Schema(description = "维度 Y年Q季度M月(必需)")
    private String dim;

    /**
     * 计算的是实际返利还是虚拟返利 0实际 1虚拟
     */
    @Schema(description = "计算的是实际返利还是虚拟返利 0实际 1虚拟")
    private int vr;

    /**
     * 车系对应的虚拟车数量
     */
    @Schema(description = "车系对应的虚拟车数量")
    private Map<String, Integer> seriesQty;

    @Schema(description = "当前虚拟车系列")
    private String series;

    /**
     * 1 - 试算 ; 其他 - 虚拟车试算
     */
    @Schema(description = "1 - 试算 ; 其他 - 虚拟车试算")
    private int vflag;

    @Schema(description = "经销商下车系对应的虚拟车数量")
    private Map<String,Map<String, Integer>> dealerSeriesQty;


    @Schema(description = "限制")
    private int limit;


    @Schema(description = "门闩")
    private CountDownLatch latch;

    @Schema(description = "试算类型（aak-std-invoice）")
    private String trailType;

}