package com.qm.ep.rebatetri.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatetri.domain.bean.TenantDealerDO;
import com.qm.ep.rebatetri.mapper.TenantDealerMapper;
import com.qm.ep.rebatetri.service.TenantDealerService;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantDealerServiceImpl extends QmBaseServiceImpl<TenantDealerMapper, TenantDealerDO> implements TenantDealerService {

    @Autowired
    private TenantDealerMapper tenantDealerMapper;

    @Override
    @DS(DataSourceType.W)
    public String getDsName(String dealerCode) {
        String tenantName;
        String factoryCode = "ALL";
        if(factoryCode.equals(dealerCode)){
            tenantName = DataSourceType.W;
        }else{
            LambdaQueryWrapper<TenantDealerDO> wrapper = new QmQueryWrapper<TenantDealerDO>().lambda();
            wrapper.eq(TenantDealerDO::getDealerCode, dealerCode);
            TenantDealerDO tenantDealerDO = tenantDealerMapper.selectOne(wrapper);
            if(tenantDealerDO == null || BootAppUtil.isNullOrEmpty(tenantDealerDO.getId())){
                tenantName = "dealerx";
            }else{
                tenantName = tenantDealerDO.getDsCode();
            }
        }
        return tenantName;
    }
}
