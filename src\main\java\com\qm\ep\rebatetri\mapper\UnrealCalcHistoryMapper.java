package com.qm.ep.rebatetri.mapper;

import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO;
import com.qm.ep.rebatetri.domain.dto.DealerSeriesVinDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UnrealCalcHistoryMapper extends QmBaseMapper<UnrealCalcHistoryDO> {

    /**
     * 获取计算对象的试算历史
     * @param calcObjectId 计算对象ID
     * @return 返回
     */
    List<UnrealCalcHistoryDO> getUnrealCalcHistory(String calcObjectId);

    /**
     * 通过策略id和对象类型获取不真实计算历史
     *
     * @param policyId   策略id
     * @param objectType 对象类型
     * @return {@link List }<{@link UnrealCalcHistoryDO }>
     */
    List<UnrealCalcHistoryDO> getUnrealCalcHistoryByPolicyIdAndObjectType(@Param("policyId") String policyId,
                                                                           @Param("objectType") String objectType);

    /**
     * 获取计算对象的试算历史
     * @param batchId 计算对象ID
     * @return 返回
     */
    List<UnrealCalcHistoryDO> getUnrealCalcHistoryByBatchId(String batchId);


    /**
     * 导入计算计算结果，方便后面统计
     *
     * @param realEField      皇家埃菲尔德
     * @param seriesField     系列字段
     * @param dealerCodeField 经销商代码字段
     * @param historyId       历史id
     * @param policyId        策略id
     * @param dim             昏暗
     * @param vrseries        vr系列
     * @param vrqty           vrqty
     * @param batchId         批次id
     * @param series          系列
     */
    void insertResultSummary(@Param("realEField") String realEField,
                             @Param("seriesField") String seriesField,
                             @Param("dealerCodeField") String dealerCodeField,
                             @Param("historyId") String historyId,
                             @Param("policyId") String policyId,
                             @Param("dim") String dim,
                             @Param("vrseries") String vrseries,
                             @Param("vrqty") int vrqty,
                             @Param("batchId") String batchId,
                             @Param("series") String series
                             );

    /**
     * 按对象类型删除
     *
     * @param objectType 对象类型
     * @return int
     */
    int deleteByObjectType(@Param("objectType") String objectType);

    /**
     * 通过批处理id获取历史id
     *
     * @param batchId   批次id
     * @param policyIds 策略ID
     * @return {@link List }<{@link String }>
     */
    List<String> getHistoryIdByBatchId(String batchId, List<String> policyIds);

    /**
     * 按id选择简单
     *
     * @param historyId 历史id
     * @return {@link ExecTrialCalcHistoryDO }
     */
    ExecTrialCalcHistoryDO selectSimpleById(String historyId);

    /**
     * 选择aa和std返利结果
     *
     * @param historyId         历史id
     * @param rebateAmountField 返利金额字段
     * @param seriesField       系列字段
     * @param vinField          vin场
     * @param dealerCodeField   经销商代码字段
     * @return {@link List }<{@link DealerSeriesVinDTO }>
     */
    List<DealerSeriesVinDTO> selectAakAndStdRebateResult(String historyId, String rebateAmountField, String seriesField, String vinField, String dealerCodeField);

    /**
     * 选择发票返利结果
     *
     * @param historyId         历史id
     * @param rebateAmountField 返利金额字段
     * @param seriesField       系列字段
     * @param vinField          vin场
     * @param dealerCodeField   经销商代码字段
     * @return {@link List }<{@link DealerSeriesVinDTO }>
     */
    List<DealerSeriesVinDTO> selectInvoiceRebateResult(@Param("historyId") String historyId, @Param("rebateAmountField") String rebateAmountField, @Param("seriesField") String seriesField, @Param("vinField") String vinField, @Param("dealerCodeField") String dealerCodeField);
}
