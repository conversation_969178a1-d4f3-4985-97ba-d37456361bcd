<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatetri.mapper.TrialCalcResultSummaryMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <id column="id" property="id"/>
        <result column="historyId" property="historyId"/>
        <result column="dealerCode" property="dealerCode"/>
        <result column="series" property="series"/>
        <result column="namt" property="namt"/>
        <result column="nqty" property="nqty"/>
        <result column="dim" property="dim"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , historyId, dealerCode, series, namt, nqty, dim, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select *
        from (select a.historyId,
                     a.dealerCode,
                     a.series,
                     a.namt,
                     a.nqty,
                     a.dim,
                     a.DTSTAMP,
                     a.policyId,
                     a.id,
                     batchId,
                     a.vin
              from trial_calc_result_summary a) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">
                #{item}
            </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">
                            ${k} IS NULL
                        </when>
                        <otherwise>
                            ${k} = #{v}
                        </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="getSeriesList" resultType="java.lang.String">
        --         select '全系' series
--         union all
        select distinct series
        from trial_calc_result_summary
        where dim = #{dim}
          and policyId = '0'
    </select>
    <select id="getTrialCalAmtSumByBatchId" resultType="java.lang.Double">
        select ifnull(sum(namt), 0)
        from trial_calc_result_summary
        where batchId = #{halfId}
          and dealerCode = #{dealerCode}
    </select>
    <select id="getRebatePolicyTop3" resultType="com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO">
        select *
        from (SELECT *
              FROM (SELECT a.policyId,
                           a.policyName                         policyName,
                           round(sum(IFNULL(a.namt, 0)) ,0)              totalAmt,
                           count(*)                             total,
                           sum(IFNULL(a.namt, 0)) / count(*) as avgAmt,
                           ROW_NUMBER()                         OVER (PARTITION BY a.policyId ORDER BY a.policyId) AS rn
                    FROM trial_calc_result_summary_detail a
                             LEFT JOIN policy b ON a.policyId = b.Id
                    where dealerCode = #{dealerCode} and a.batchId = #{batchId}
                        <if test="series != null and series != '' and series != '全系'">
                            and a.series = #{series}
                        </if>

                    GROUP BY a.policyId) a
              WHERE a.rn = 1) t
        ORDER BY totalAmt desc limit 3
    </select>
    <select id="selectAllByDim" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        select
        <include refid="Base_Column_List"/>
        from trial_calc_result_summary where dim = #{dim}
    </select>
    <select id="selectByDealerCodeDim" resultType="java.lang.String">
    </select>
    <select id="getDealerAakTask" resultType="com.qm.ep.rebatetri.domain.dto.DealerTaskDTO">
        select *, aak_map as taskQty
        from trial_calc_result_summary
        where dealerCode = #{dealerCode}
          and batchId = 'AAK'
    </select>
    <select id="getDealerStdTask" resultType="com.qm.ep.rebatetri.domain.dto.DealerTaskDTO">
        select *
        from trial_calc_result_summary
        where dealerCode = #{dealerCode}
          and batchId = 'AAK'
    </select>
    <select id="getUnrealRebateAmtSumByQty" resultType="java.lang.Double">
        select ifnull(sum(namt), 0)
        from unreal_car_rebate_summary
        where nqty = #{qty}
        and dealerCode = #{dealerCode}
        <if test="series != null and series != '' and series != '全系'">
            AND series = #{series}
        </if>

        <if test="policyIds != null and policyIds.size > 0">
            and policyId in
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectDetailsByUniqueKey" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDetailPO">
        SELECT *
        FROM trial_calc_result_summary_detail
        WHERE dealerCode = #{dealerCode}
        <if test="series != null and series != '' and series != '全系'">
            AND series = #{series}
        </if>
          and policyId = #{policyId}
          and batchid is null
    </select>
    <select id="selectUnrealRebateByPolicyIdAndQtyAndDealerCode" resultType="java.lang.String">
        SELECT namt
        from unreal_car_rebate_summary
        WHERE policyId = #{policyId}
          AND nqty = #{qty}
          AND dealerCode = #{dealerCode}
          AND series = #{series}
    </select>
    <select id="getSeriesRealRebate" resultType="com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO">
        SELECT
        dealerCode,
        '全系' AS series,
        sum(namt) AS namt,   -- 计奖返利
        sum(nqty) AS nqty,   -- 计奖数量
        sum(stdQty) AS stdQty
        FROM
        `trial_calc_result_summary`
        WHERE
        dealerCode = #{dealerCode}
        AND batchId IN ('AAK', 'AAKAMT')
        AND namt >= 0
        <if test="policyIds != null and policyIds.size() > 0">
            AND (policyId IN
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR policyId = '0')
        </if>
        GROUP BY
        dealerCode
        HAVING
        nqty > 0

        UNION ALL

        SELECT
        dealerCode,
        series,
        sum(namt) AS namt,   -- 计奖返利
        sum(nqty) AS nqty,   -- 计奖数量
        sum(stdQty) AS stdQty
        FROM
        `trial_calc_result_summary`
        WHERE
        dealerCode = #{dealerCode}
        AND batchId IN ('AAK', 'AAKAMT')
        AND namt >= 0
        <if test="policyIds != null and policyIds.size() > 0">
            AND (policyId IN
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR policyId = '0')
        </if>
        GROUP BY
        dealerCode, series
        HAVING
        nqty > 0

    </select>
    <select id="selectAvgRebateByPolicyId" resultType="java.lang.Double">
        SELECT
            IFNULL( SUM( namt )/ SUM( vrqty ), 0 )
        FROM
            `trial_calc_result_summary`
        WHERE
            dealerCode = #{dealerCode}
          AND batchId = 'AAKAMT'
        <if test="series != null and series != '' and series != '全系'">
            AND series = #{series}
        </if>
          AND policyId = #{policyId}
    </select>

    <select id="getMaxInitRebateByPolicyIds" resultType="java.lang.Double">
        select ifnull(sum(namt), 0)
        from unreal_car_rebate_summary
        where nqty =
        (select max(nqty) from unreal_car_rebate_summary where dealerCode = #{dealerCode}
            <if test="series != null and series != '' and series != '全系'">
                AND series = #{series}
            </if>
            and policyId in
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        )
        and dealerCode = #{dealerCode}
        <if test="series != null and series != '' and series != '全系'">
            AND series = #{series}
        </if>
        <if test="policyIds != null and policyIds.size > 0">
            and policyId in
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getSeriesUnrealRebateAmtSumByPolicyIds"
            resultType="com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryDTO">
        select dealerCode,series,nqty, ifnull(sum(namt), 0) as amt
        from unreal_car_rebate_summary
        where 1=1
        <if test="dealerCode != null and dealerCode != ''">
            AND dealerCode = #{dealerCode}
        </if>
        <if test="policyIds != null and policyIds.size > 0">
            and policyId in
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY dealerCode,series,nqty
    </select>
    <select id="selectRealAakCount" resultType="java.lang.Integer">
        SELECT sum(aakQty) as aakQty from  trial_calc_result_summary  WHERE policyId = '0' AND dealerCode = #{dealerCode}
            <if test="series != null and series != '全系'">
                AND series = #{series}
            </if>

    </select>
    <select id="selectAakMapCount" resultType="java.lang.Integer">
        SELECT aak_map from  trial_calc_result_summary  WHERE policyId = '0' AND dealerCode = #{dealerCode} AND series = #{series}
    </select>
    <select id="getAllSeriesUnrealRebateAmtSumByPolicyIds"
            resultType="com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryDTO">
        select dealerCode,'全系' as series,nqty, ifnull(sum(namt), 0) as amt
        from unreal_car_rebate_summary
        where 1=1
        <if test="dealerCode != null and dealerCode != ''">
            AND dealerCode = #{dealerCode}
        </if>
        <if test="policyIds != null and policyIds.size > 0">
            and policyId in
            <foreach collection="policyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY dealerCode,nqty
    </select>

    <select id="getRealRebateByPolicyId" resultMap="BaseResultMap">
        SELECT * FROM `trial_calc_result_summary` WHERE policyId = #{policyId} AND batchId = 'AAKAMT' ORDER BY dealerCode,series,nqty ASC
    </select>
</mapper>
