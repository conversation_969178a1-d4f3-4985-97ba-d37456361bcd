package com.qm.ep.rebatetri.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.ep.rebatetri.domain.bean.UnrealPolicyRecordPO;
import com.qm.ep.rebatetri.mapper.UnrealPolicyRecordMapper;
import com.qm.ep.rebatetri.service.UnrealPolicyRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 政策试算数据待初始化表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Service
public class UnrealPolicyRecordServiceImpl extends ServiceImpl<UnrealPolicyRecordMapper, UnrealPolicyRecordPO> implements UnrealPolicyRecordService {

    @Resource
    private UnrealPolicyRecordMapper unrealPolicyRecordMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateUnrealPolicyStatusById(Integer id, String status) {
        unrealPolicyRecordMapper.updateUnrealPolicyStatusById(id, status);
    }

    @Override
    public UnrealPolicyRecordPO selectTheEarliestPolicy() {
        return unrealPolicyRecordMapper.selectTheEarliestPolicy();
    }

    @Override
    public Set<String> selectCalMainTableName(String policyId) {
        List<String> tableNames = unrealPolicyRecordMapper.selectCalMainTableName(policyId);
        HashSet<String> result = new HashSet<>();
        for (String tableName : tableNames) {
            String[] split = tableName.split(",");
            result.addAll(Arrays.asList(split));
        }
        return result;
    }
}
