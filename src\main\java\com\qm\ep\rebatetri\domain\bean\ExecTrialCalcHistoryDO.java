package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.ep.rebatetri.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatetri.enumerate.ExecTypeEnum;
import com.qm.ep.rebatetri.enumerate.StateEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exec_trial_calc_history")
@Schema(description = "试算历史主表对象")
public class ExecTrialCalcHistoryDO implements Serializable {
    
    

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "计算对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "计算对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "计算类型")
    @TableField("execType")
    private ExecTypeEnum execType;

    @Schema(description = "历史版本号")
    @TableField("calcVersion")
    private Integer calcVersion;

    @Schema(description = "状态")
    @TableField("state")
    private StateEnum state;

    @Schema(description = "试算开始时间")
    @TableField(value = "begin")
    private Date begin;

    @Schema(description = "试算结束时间")
    @TableField(value = "end")
    private Date end;

    @Schema(description = "语句sql")
    @TableField("sqlStructure")
    private String sqlStructure;

    @Schema(description = "日志log")
    @TableField("log")
    private String log;

    @Schema(description = "结果求和")
    @TableField("sumResult")
    private String sumResult;

    @Schema(description = "执行线程名称")
    @TableField("executorThreadName")
    private String executorThreadName;

    @Schema(description = "创建者")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据is_deleted")
    @TableField("isDeleted")
    private String isDeleted;

    @Schema(description = "数据record_version")
    @TableField("recordVersion")
    private String recordVersion;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Date dtstamp;

    // @Schema(description = "试算器批量执行ID")
    // @TableField("batchId")
    // private String batchId;

    @Schema(description = "数据")
    @TableField(exist = false)
    private String batchId;
}
