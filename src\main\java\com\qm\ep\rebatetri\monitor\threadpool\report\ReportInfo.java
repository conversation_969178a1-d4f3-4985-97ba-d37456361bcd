package com.qm.ep.rebatetri.monitor.threadpool.report;

import com.alibaba.fastjson.JSON;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ReportInfo {

    /**
     * 任务id
     */
    private String jobId;

    /**
     * 任务标签
     */
    private String tag;

    /**
     * 任务执行结果 0 成功 1 失败
     */
    private Integer status;

    /**
     * 执行时间 毫秒
     */
    private long executeTime;

    private String poolName;

    private String createTime;


    public String toJson(){
        return JSON.toJSONString(this);
    }
}
