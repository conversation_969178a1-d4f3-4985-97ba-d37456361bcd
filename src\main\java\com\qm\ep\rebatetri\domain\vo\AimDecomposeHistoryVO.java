package com.qm.ep.rebatetri.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
* <p>
* 
* </p>
* <AUTHOR>
* @since 2023-01-30
*/
@Schema(description = "数据数据DTO")
@Data
public class AimDecomposeHistoryVO {

    
    

    @Schema(description = "同上")
    private String id;

    @Schema(description = "目标详情ID")
    private String detailId;

    @Schema(description = "excel数据是否落库，1：上传中， 2：上传出错，3：完成")
    private String status;

    @Schema(description = "分解合计")
    private Integer breakdownTotal;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "出错log，便于跟踪问题")
    private String log;

    @Schema(description = "是否锁定，每个目标详情，只能锁定一个版本。0：未锁定，1：锁定")
    private Integer isLock;

    @Schema(description = "目标值")
    private Double value;

    @Schema(description = "创建时间")
    private Date createOn;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "更新")
    private Date updateOn;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "DTSTAMP")
    private Timestamp dtstamp;
}