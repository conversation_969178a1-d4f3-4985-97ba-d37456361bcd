package com.qm.ep.rebatetri.domain.vo;

import com.qm.ep.rebatetri.domain.dto.KanbanDetailDTO;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据DTO")
@Data
public class RebateCalDetailVO {
    @Schema(description = "看板策略 top3")
    private List<KanbanPolicyTopDTO> kanbanPolicyTop3;
    @Schema(description = "看板详细信息")
    private List<KanbanDetailDTO> kanbanDetail;
}
