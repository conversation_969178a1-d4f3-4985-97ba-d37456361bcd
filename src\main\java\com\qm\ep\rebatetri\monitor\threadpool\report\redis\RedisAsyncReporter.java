package com.qm.ep.rebatetri.monitor.threadpool.report.redis;


import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSON;
import com.qm.ep.rebatetri.monitor.threadpool.report.*;
import com.qm.ep.rebatetri.monitor.threadpool.utils.DateUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 往redis异步上报数据信息
 *
 * <AUTHOR>
 */
@Component
public class RedisAsyncReporter implements Reporter {

    @Resource
    private RedisService redisService;

    @Override
    public void doReportTask(String ip, Integer port, String applicationName, String tagId, ReportInfo reportInfo) {
        String redisKey = Utils.buildJobInfoListKey(
                ip,
                port,
                applicationName,
                DateUtils.getTodayStr(),
                reportInfo.getPoolName(),
                tagId);
        //只记录耗时最高的5k个
        redisService.zAdd(redisKey, reportInfo.toJson(), reportInfo.getExecuteTime());
    }

    @Override
    public void doReportRealTime(String ip, Integer port, String applicationName, ThreadPoolRealTimeInfo realTimeInfo) {
        String keyName = Utils.buildThreadPoolRealTimeKey(
                ip,
                port,
                applicationName,
                realTimeInfo.getPoolName());
        redisService.set(keyName, JSON.toJSONString(realTimeInfo), Utils.convertToSecond(7, TimeUnit.DAYS));
    }

    @Override
    public void doReportAlarmInfo(String applicationName, String alarmEmails) {
        String redisKey = Utils.buildAlarmKey(applicationName);
//        redisService.setMapItem(redisKey, applicationName, alarmEmails);
//        redisService.expire(redisKey, 7, TimeUnit.DAYS);
    }

    @Override
    public void doReportTaskTimes(Integer taskTimes, ThreadPoolDetailInfo param) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dayKey = dateFormat.format(new Date());
        String keyName = Utils.buildThreadPoolTaskTimesKey(
                param.getIp(),
                param.getPort(),
                param.getApplicationName(),
                param.getPoolName(),
                dayKey);
        redisService.llSet(keyName, taskTimes + ":" + DateUtils.getCurrentMin(), Utils.convertToSecond(7, TimeUnit.DAYS));
    }

    @Override
    public void doReportTotalData(TotalDataInfo totalDataInfo) {
        String mapKeyName = Utils.buildTotalDataKey(totalDataInfo.getApplicationName());
        redisService.sSetAndTime(mapKeyName, Utils.convertToSecond(7, TimeUnit.DAYS), NetUtil.getLocalhostStr() + "$" + totalDataInfo.getPort() + "$" + totalDataInfo.getPoolName());
    }

    @Override
    public void doReportThreadPoolInfo(ThreadPoolDetailInfo param) {
        String dateKey = DateUtils.getTodayStr();
        String keyName = Utils.buildThreadPoolDetailInfoKey(
                param.getIp(),
                param.getPort(),
                param.getApplicationName(),
                param.getPoolName(),
                dateKey);
        redisService.llSet(keyName, param.toJson(), Utils.convertToSecond(7, TimeUnit.DAYS));
    }

    @Override
    public void doReportErrorTaskTimes(int errorCount, ThreadPoolDetailInfo param) {
        String dateKey = DateUtils.getTodayStr();
        String keyName = Utils.buildErrorTaskKey(
                param.getIp(),
                param.getPort(),
                param.getApplicationName(),
                param.getPoolName(),
                dateKey);
        redisService.llSet(keyName, errorCount + ":" + DateUtils.getCurrentMin(), Utils.convertToSecond(7, TimeUnit.DAYS));
    }

    @Override
    public void doReportTagTimes(int tagCount, ThreadPoolDetailInfo param) {
        String dateKey = DateUtils.getTodayStr();
        String keyName = Utils.buildTagCountKey(
                param.getIp(),
                param.getPort(),
                param.getApplicationName(),
                param.getPoolName(),
                dateKey);
        redisService.llSet(keyName, tagCount + ":" + DateUtils.getCurrentMin(), Utils.convertToSecond(7, TimeUnit.DAYS));
    }
}
