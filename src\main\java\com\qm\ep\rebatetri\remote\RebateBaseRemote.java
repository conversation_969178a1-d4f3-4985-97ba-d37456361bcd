package com.qm.ep.rebatetri.remote;

import com.qm.ep.rebatetri.domain.bean.RedFlagTrialMainDO;
import com.qm.ep.rebatetri.domain.bean.SeriesQuotaDO;
import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.ep.rebatetri.domain.vo.AimDecomposeHistoryVO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(contextId= "tds-service-rebate",name = "tds-service-rebate")
public interface RebateBaseRemote {

    /**
     * 获取业务数据
     * @return 返回
     */
    @PostMapping(value = "/businessData/getDataList", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<Object> getDataList(@RequestBody BusinessDataDTO businessDataDTO, @RequestHeader("tenantId") String tenantId);

    /**
     * 获取政策列表
     * @return 返回
     */
    @PostMapping(value = "/policy/list", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<Object> getPolicyList(@RequestBody PolicyConditionDTO policyConditionDTO, @RequestHeader("tenantId") String tenantId);

    /**
     * 获取sql结构（定制化的修改，增加虚拟车模拟）
     * @param calcFactorDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/calcObject/getSqlStructure2", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<SqlStructureVO> getMockSqlStructure(@RequestBody CalcObjectDTO calcFactorDTO);


    /**
     * 获取真正sql结构
     *
     * @param calcFactorDTO 计算系数dto
     * @param tenantId      租户id
     * @return {@link JsonResultVo }<{@link SqlStructureVO }>
     */
    @PostMapping(value = "/calcObject/getSqlStructure", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<SqlStructureVO> getRealSqlStructure(@RequestBody CalcObjectDTO calcFactorDTO);

    /**
     * 商务政策详细信息
     * @param policyDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/policy/detail", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<PolicyDTO> getPolicyDetail(@RequestBody PolicyDTO policyDTO);

    /**
     * 获取业务目标分解列表
     * @param aimDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/aim/getAimDetailList", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<List<AimDetailDTO>> getAimDetailList(@RequestBody AimDTO aimDTO);

    /**
     * 获取业务目标分解列表
     * @param aimDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/aim/detail/getLockedHistory", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<AimDecomposeHistoryVO> getLockedHistory(@RequestBody AimDTO aimDTO);

    /**
     * 将商务政策置为“已计算”
     * @param policyDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/policy/calculated", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<Object> calculated(@RequestBody PolicyDTO policyDTO);

    /**
     * 底表转换
     * @param turnBusinessBottomTableDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/turnBusinessBottomTable/turnBusinessBottomTableVerification", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<String> turnBusinessBottomTable(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO);

    /**
     * 获取可以进行阶梯计算的政策对象列表
     * @return 返回
     */
    @PostMapping(value = "/calcObject/getLadderExecCalcList", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<List<ExecCalcDTO>> getLadderExecCalcList();

    /**
     * 获取系统参数
     * @param code 参数
     * @return 返回
     */
    @PostMapping(value = "/systemConfig/getValueByCode", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<String> getValueByCode(@RequestBody String code, @RequestHeader("tenantId") String tenantId);

    /**
     * 获取底表结构
     * @param tableName 参数
     * @return 返回
     */
    @GetMapping(value = "/businessConstruction/getDataColumnAndId", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<BusinessConstructionDTO> getDataColumnAndId(@RequestParam String tableName);

    /**
     * 根据年月删除数据
     * @param tableName 参数
     * @param yesrs 参数
     * @return 返回
     */
    @PostMapping(value = "/businessData/deleteByMap", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<String> deleteByMap(@RequestParam("tableName") String tableName, @RequestBody String yesrs);

    @PostMapping(value = "/sysPersonOrg/checkPersonAtTopLevel", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<Boolean> checkPersonAtTopLevel(@RequestBody String personId);

    @PostMapping(value = "/sysPersonOrg/getLeafOrg", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<List<String>> getLeafOrg(@RequestBody String personId);

    @PostMapping(value = "/redFlagTrialMain/getRedFlagMainById", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<RedFlagTrialMainDO> getRedFlagMainById(@RequestParam String id);

    @PostMapping(value = "/redFlagTrialMain/getRedFlagMainByPolicyId", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<RedFlagTrialMainDO> getRedFlagMainByPolicyId(@RequestParam String policyId);

    @PostMapping(value = "/calcObject/getSqlStructure3", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<SqlStructureVO> getSqlStructure3(@RequestBody CalcObjectDTO calcFactorDTO);

    @GetMapping(value = "/seriesQuota/seriesList", produces = "application/json", headers = {"tenantId=15"})
    JsonResultVo<List<SeriesQuotaDO>> seriesList();

    @PostMapping(value = "/policy/queryPolicyList", produces = "application/json", headers = {"tenantId=15"})
    List<RebateQueryResponse> queryPolicyList(@RequestBody RebateQueryListRequest request);
}
