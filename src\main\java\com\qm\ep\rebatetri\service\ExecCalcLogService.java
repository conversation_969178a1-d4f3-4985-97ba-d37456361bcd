package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.dto.ExecCalcLogStructureDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExecCalcLogService {

    /**
     * 追加日志内容
     * @param originLog 原始日志
     * @param newLog 要追加的新日志
     * @return 返回
     */
    String append(String originLog, String newLog);

    /**
     * 追加日志内容
     * @param originLog 原始日志
     * @param newLog 要追加的新日志
     * @return 返回
     */
    String append(String originLog, ExecCalcLogStructureDTO newLog);

    /**
     * 追加日志内容
     * @param originLog 原始日志
     * @param newLogs 要追加的新日志
     * @return 返回
     */
    String append(String originLog, List<ExecCalcLogStructureDTO> newLogs);
}
