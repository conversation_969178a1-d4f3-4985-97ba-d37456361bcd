package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "BusinessConstructionDTO对象")
@Data
@ToString(callSuper = true)
public class BusinessConstructionDTO extends JsonParamDto {

    
    

    @Schema(description = "主键")
    private String id;

    @Schema(description = "业务底表名")
    private String tableName;

    @Schema(description = "业务底表字段名")
    private String fieldName;

    @Schema(description = "业务底表关联字段")
    private String relevanceFieldName;

    @Schema(description = "业务底表字段类型")
    private String fieldType;

    @Schema(description = "保留小数位")
    private Integer decimalPoint;

    @Schema(description = "是否必填")
    private Integer required;

    @Schema(description = "联合唯一标识")
    private Integer uniqueKey;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "所有的政策可用")
    private Integer openState;

}
