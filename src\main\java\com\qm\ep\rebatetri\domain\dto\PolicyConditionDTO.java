package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Schema(description = "策略条件 DTO")
@EqualsAndHashCode(callSuper = true)
@Data
public class PolicyConditionDTO extends JsonParamDto {

    @Schema(description = "同上")
    private String id;

    @Schema(description = "政策id集合，逗号分隔")
    private List<String> policyIdList;

    @Schema(description = "政策代码")
    private String vpolicycode;

    @Schema(description = "政策名称")
    private String vpolicyname;

    @Schema(description = "完成状态 00-录入，01-配置完成，20-提交，25-经理审核，30-发布，99-弃用")
    private List<String> vfinishstates;

    @Schema(description = "政策开始时间")
    private Date dbegin;

    @Schema(description = "政策结束时间")
    private Date dend;

    @Schema(description = "关联车型")
    private String vapplyprdt;

    @Schema(description = "合作伙伴 1全部2指定3不指定")
    private String  vapplyorg;

    @Schema(description = "是否可见 1是0否")
    private Integer visible;

    @Schema(description = "允许试算 1是0否")
    private Integer calc;

    @Schema(description = "参与试算标识 1是，0否")
    private Integer canTrial;

}
