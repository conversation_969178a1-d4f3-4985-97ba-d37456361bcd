package com.qm.ep.rebatetri.remote;

import com.qm.ep.rebatetri.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatetri.enumerate.CalcTypeEnum;
import com.qm.ep.rebatetri.enumerate.ExecTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "计算对象执行计算")
@Data
public class ExecCalcUnrealDTO extends JsonParamDto {

    
    

    @Schema(description = "政策ID")
    private String policyId;

    @Schema(description = "计算对象ID")
    private String objectId;

    @Schema(description = "计算对象类型")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "计算类型：正式计算、试算")
    private CalcTypeEnum calcType;

    @Schema(description = "执行类型：自动、人工")
    private ExecTypeEnum execType;

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "是否自动转底表")
    private String autoTurnTable;

    @Schema(description = "底表名称")
    private String tableName;

    @Schema(description = "是否自动入账")
    private String autoEnterAccount;

}