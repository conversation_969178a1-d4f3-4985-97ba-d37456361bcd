package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.constant.Constants;
import com.qm.ep.rebatetri.domain.bean.TrialCalcResultSummaryDO;
import com.qm.ep.rebatetri.domain.bean.TrialUnrealResultDO;
import com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryDTO;
import com.qm.ep.rebatetri.domain.dto.KanbanPolicyTopDTO;
import com.qm.ep.rebatetri.mapper.TrialCalcResultSummaryMapper;
import com.qm.ep.rebatetri.mapper.TrialUnrealResultMapper;
import com.qm.ep.rebatetri.service.TrialCalcResultSummaryService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试算结果汇总表-试算器用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
public class TrialCalcResultSummaryServiceImpl extends QmBaseServiceImpl<TrialCalcResultSummaryMapper, TrialCalcResultSummaryDO> implements TrialCalcResultSummaryService {


    @Resource
    private TrialCalcResultSummaryMapper trialCalcResultSummaryMapper;

    @Resource
    private TrialUnrealResultMapper trialUnrealResultMapper;

    @Override
    public List<TrialCalcResultSummaryDO> selectAllByDim(String dim) {
        return trialCalcResultSummaryMapper.selectAllByDim(dim);
    }

    @Override
    public Double getUnrealRebateAmtSumByQty(int qty, String dealerCode, List<String> policyIds, String series) {

        Double amt = trialCalcResultSummaryMapper.getUnrealRebateAmtSumByQty(qty, dealerCode, policyIds, series);
        // 如果Map点超过了初始化返利的数据，获取不到就会为0，这时候默认取最大的初始化返利的数据
        if (amt == 0.0) {
            amt = trialCalcResultSummaryMapper.getMaxInitRebateByPolicyIds(dealerCode, policyIds, series);
        }
        return amt;
    }

    @Override
    public Map<String, Double> getAllUnrealRebateAmtSumByPolicyIds(String dealerCode, List<String> policyIds) {
        List<UnrealCarRebateSummaryDTO> seriesResult = trialCalcResultSummaryMapper.getSeriesUnrealRebateAmtSumByPolicyIds(dealerCode, policyIds);
        Map<String, Double> stringDoublehashmap = new HashMap<>(32);
        for (UnrealCarRebateSummaryDTO temp : seriesResult) {
            String key = temp.getDealerCode() + "-" + temp.getSeries() + "-" + temp.getNqty();
            stringDoublehashmap.put(key, temp.getAmt());
        }
        List<UnrealCarRebateSummaryDTO> allSeriesResult = trialCalcResultSummaryMapper.getAllSeriesUnrealRebateAmtSumByPolicyIds(dealerCode, policyIds);
        for (UnrealCarRebateSummaryDTO all : allSeriesResult) {
            String key = all.getDealerCode() + "-" + all.getSeries() + "-" + all.getNqty();
            stringDoublehashmap.put(key, all.getAmt());
        }
        return stringDoublehashmap;
    }

    @Override
    public List<KanbanPolicyTopDTO> getRebatePolicyTop3(String series, String dealerCode, String batchId) {
        List<KanbanPolicyTopDTO> rebatePolicyTop3 = trialCalcResultSummaryMapper.getRebatePolicyTop3(series, dealerCode, batchId);
        // 修改金额和外层保持一致，因为明细有时候计算会出现精度的问题导致和最优点不一致，差几块钱，但是产品要求强一致
        TrialUnrealResultDO trialUnrealResultDO = trialUnrealResultMapper.selectOneByBatchId(batchId);
        String policyId = trialUnrealResultDO.getPolicyId();
        if (policyId.contains(Constants.COMMA) || policyId.contains(Constants.LINE)) {

        } else {
            // 单政策逻辑
            for (KanbanPolicyTopDTO temp : rebatePolicyTop3) {

                temp.setTotalAmt(trialUnrealResultDO.getTotalAmt());
                temp.setAvgAmt(trialUnrealResultDO.getBestAmt());
            }

        }

        return rebatePolicyTop3;
    }

    @Override
    public List<TrialCalcResultSummaryDO> getRealRebateByPolicyId(String policyId) {
        return trialCalcResultSummaryMapper.getRealRebateByPolicyId(policyId);
    }
}
