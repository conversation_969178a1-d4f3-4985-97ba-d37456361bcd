package com.qm.ep.rebatetri.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qm.ep.rebatetri.domain.bean.UnrealCarRebateSummaryPO;
import com.qm.ep.rebatetri.mapper.UnrealCarRebateSummaryMapper;
import com.qm.ep.rebatetri.service.UnrealCarRebateSummaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.tds.dynamic.constant.DataSourceType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 返利跑批数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Service
public class UnrealCarRebateSummaryServiceImpl extends ServiceImpl<UnrealCarRebateSummaryMapper, UnrealCarRebateSummaryPO> implements UnrealCarRebateSummaryService {

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void saveBatchAsync(List<UnrealCarRebateSummaryPO> saveList) {
        this.saveBatch(saveList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    public void saveBatchUpdateAsync(List<UnrealCarRebateSummaryPO> updateList) {
        this.updateBatchById(updateList);
    }
}
