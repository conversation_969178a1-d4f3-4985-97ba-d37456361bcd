package com.qm.ep.rebatetri.service.impl;

import com.qm.ep.rebatetri.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatetri.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebatetri.remote.RebateDataRemote;
import com.qm.ep.rebatetri.service.ExecFormalCalcHistoryService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecFormalCalcHistoryServiceImpl extends QmBaseServiceImpl<ExecFormalCalcHistoryMapper, ExecFormalCalcHistoryDO> implements ExecFormalCalcHistoryService {

    @Resource
    RebateDataRemote rebateDataRemote;

    @Override
    public void autoApplyEntryAccount(ExecFormalCalcHistoryDO execCalcHistoryDO) {
        rebateDataRemote.applyEntryAccount(execCalcHistoryDO);
    }
}
