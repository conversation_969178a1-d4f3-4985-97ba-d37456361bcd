package com.qm.ep.rebatetri.monitor.threadpool.job;

import com.qm.ep.rebatetri.monitor.threadpool.config.MonitorCache;
import com.qm.ep.rebatetri.monitor.threadpool.report.ReportInfo;
import com.qm.ep.rebatetri.monitor.threadpool.report.Reporter;
import com.qm.ep.rebatetri.monitor.threadpool.report.redis.RedisService;
import com.qm.ep.rebatetri.monitor.threadpool.report.redis.Utils;
import com.qm.tds.api.util.SpringContextHolder;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class AsyncConsumer implements Runnable {

    private final Reporter reporter;
    private final RedisService redisService;

    public AsyncConsumer() {
        this.reporter = SpringContextHolder.getBean(Reporter.class);
        this.redisService = SpringContextHolder.getBean(RedisService.class);
    }

    @Override
    public void run() {
//        while (true) {
//            ReportInfo reportInfo = null;
//            try {
//                reportInfo = MonitorCache.arrayBlockingQueue.take();
//                String tagKey = Utils.buildTagKey(MonitorCache.applicationName, reportInfo.getPoolName());
//                String tagUUid = (String) redisService.hget(tagKey, reportInfo.getTag());
//                if (StringUtils.isEmpty(tagUUid)) {
//                    tagUUid = UUID.randomUUID().toString();
//                    redisService.hset(tagKey, reportInfo.getTag(), tagUUid);
//                }
//                reporter.doReportTask(MonitorCache.ip, MonitorCache.port, MonitorCache.applicationName, tagUUid, reportInfo);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        }
    }
}
