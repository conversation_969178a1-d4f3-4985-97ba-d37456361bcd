package com.qm.ep.rebatetri.monitor.threadpool.executor;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ExecutorPeekRecordHolder {

    /**
     * 记录最近15秒内的活跃线程最高峰
     */
    private int activeThreadCountPeek = 0;

    /**
     * 记录最近15秒内的任务队列最大值
     */
    private int taskQueueLengthPeek = 0;

    private List<Long> executeTaskTimes = new LinkedList<>();


    public void recordActiveThreadCountPeek(int activeThreadCount) {
        if (activeThreadCountPeek < activeThreadCount) {
            activeThreadCountPeek = activeThreadCount;
        }
    }

    public void recordTaskQueueLengthPeek(int taskQueueLength) {
        if (taskQueueLengthPeek < taskQueueLength) {
            taskQueueLengthPeek = taskQueueLength;
        }
    }

    public void recordExecuteTaskTimes(long timeCount) {
        executeTaskTimes.add(timeCount);
    }

    /**
     * 清理队列长度和线程池活跃数值
     */
    public void clearActiveCountAndQueueSize() {
        this.setActiveThreadCountPeek(0);
        this.setTaskQueueLengthPeek(0);
    }

}
