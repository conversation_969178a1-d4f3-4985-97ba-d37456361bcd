package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "DealerCalcDTO对象")
@Data
public class DealerSoldCalcDTO extends JsonParamDto {

    
    

    @Schema(description = "政策ID列表")
    private String policyIdList;

    @Schema(description = "车系")
    private String carSeries;

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "统计时间范围")
    private String statisticTimeRange;

    @Schema(description = "已零售")
    private Integer aak;

    @Schema(description = "预计返利")
    private Double expectedRebateAmount;

    @Schema(description = "单台返利均值")
    private Double singleUnitRebate;

}