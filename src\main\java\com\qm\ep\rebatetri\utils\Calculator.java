package com.qm.ep.rebatetri.utils;

import lombok.Data;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 计算器
 *
 * <AUTHOR>
 * @date 2024/12/17
 */
@Data
public class Calculator {
    protected Map<String, Integer> seriesCounts;
    protected Map<String, Integer> seriesOldMap;


    public Calculator(Map<String, Integer> seriesCounts) {
        this.seriesCounts = new ConcurrentHashMap<>(seriesCounts);
        this.seriesOldMap = new ConcurrentHashMap<>(seriesCounts);
    }

    /**
     * 获取当前跟踪Map的count最大的车系
     *
     * @return {@link String }
     */
    public String getMaxCountSeries() {
        String keyWithMaxValue = null;
        int maxValue = Integer.MIN_VALUE;

        for (Map.Entry<String, Integer> entry : seriesOldMap.entrySet()) {
            if (entry.getValue() > maxValue) {
                maxValue = entry.getValue();
                keyWithMaxValue = entry.getKey();
            }
        }

        return keyWithMaxValue;
    }
}
