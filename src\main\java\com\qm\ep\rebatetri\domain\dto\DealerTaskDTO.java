package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 经销商销售目标DTO
 * <AUTHOR>
 * @date 2023年9月6日
 */

@Schema(description = "数据业务目标DTO")
@Data
public class DealerTaskDTO {

    
    

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "目标名称")
    private String taskName;

    @Schema(description = "经销商名称")
    private String dealerName;

    @Schema(description = "年月")
    private String yearMonth;

    @Schema(description = "系列")
    private String series;

    @Schema(description = "目标数")
    private Integer taskQty;

    @Schema(description = "维度 Y年度 Q季度 M月度")
    private String dim;

    @Schema(description = "AAK地图")
    private Integer aakMap;

    @Schema(description = "STD地图")
    private Integer stdMap;
}