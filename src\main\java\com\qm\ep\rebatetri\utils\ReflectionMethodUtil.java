package com.qm.ep.rebatetri.utils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * @description:
 * @author: huchen
 * @create: 2023-01-10 13:58
 **/
public class ReflectionMethodUtil {

    /**
     * 动态调用实体的get方法
     *
     * @param dto 实体
     * @param name 动态拼接字段
     * @throws Exception
     * @return
     */
    public static Object getValue(Object dto, String name) {
        try {

            Method m = (Method) dto.getClass().getMethod(("get" + name));

            return m.invoke(dto);// 调用getter方法获取属性值

        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 动态调用实体的set方法
     *
     * @param dto 实体
     * @param name 动态拼接字段
     * @param value 值
     * @throws Exception
     */
    public static void setValue(Object dto, String name, Object value) {
        try {
            Method[] m = dto.getClass().getMethods();
            for (int i = 0; i < m.length; i++) {
                if (("set" + name).toLowerCase().equals(m[i].getName().toLowerCase())) {
                    m[i].invoke(dto, value);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
