package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页参数
 *
 * <AUTHOR>
 * @date 2023/09/04
 */
@Schema(description = "数据:分页参数")
@Data
public class PageRequest implements Serializable {

    /**
     * 每页数目
     */
    @Schema(description = "每页数目")
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize=1000;
    /**
     * 页码
     */
    @Schema(description = "页码")
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum=1;

}
