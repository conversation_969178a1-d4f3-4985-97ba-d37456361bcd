package com.qm.ep.rebatetri.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

/**
* <p>
* 政策发布配置
* </p>
* <AUTHOR>
* @since 2023-08-28
*/
@Schema(description = "数据政策发布配置")
@Data
public class PolicyPublishConfigDTO extends JsonParamDto {

    
    
    @Schema(description = "ID")
    private String id;
    @Schema(description = "政策ID")
    private String policyId;
    @Schema(description = "经销商代码")
    private String dealerCode;
    @Schema(description = "经销商名称")
    private String dealerName;

    @Schema(description = "可见")
    private Integer visible;

    @Schema(description = "计算")
    private Integer calc;

    @Schema(description = "DTSTAMP")
    private Timestamp dtstamp;

    @Schema(description = "合作伙伴 1全部2指定3不指定")
    private String  vapplyorg;

    @Schema(description = "年月")
    private String  yearMonth;
}