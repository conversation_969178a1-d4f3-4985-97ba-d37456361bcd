package com.qm.ep.rebatetri.mapper;


import com.qm.ep.rebatetri.domain.bean.BusinessDataDO;
import com.qm.ep.rebatetri.domain.dto.*;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface BusinessDataMapper extends QmBaseMapper<BusinessDataDO> {

    List<DealerTaskDTO> getDealerAAKTask(@Param("dealerCode") String dealerCode, @Param("dim") String dim, @Param("ymList") List<String> ymList, @Param("year") String year);

    /**
     * 按照时间维度获取可参与试算的政策的计算对象
     * @param beginDate
     * @param endDate
     * @return
     */
    List<ExecCalcDTO> getCalcPolicy(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<DealerAAKDTO> getDealerAAK(@Param("year")String year, @Param("months")List<String> months, @Param("dim")String dim, @Param("dealerCodes") List<String> dealerCode);

    List<DealerSTDDTO> getDealerSTD(@Param("year")String year, @Param("months")List<String> months, @Param("dim")String dim, @Param("dealerCodes") List<String> dealerCodes);

    List<DealerTaskDTO> getDealerSTDTask(@Param("dealerCode") String dealerCode, @Param("dim") String dim, @Param("ymList") List<String> ymList, @Param("year") String year);

    List<PolicyPublishConfigDTO> getPolicyPublishConfig(PolicyPublishConfigDTO policyPublishConfigDTO);

    List<String> isVisible(List<String> policyIds);


    void truncateData(List<String> tables);

    /**
     * 获取所有经销商中车系aak目标最大值
     *
     * @param year 年
     * @return {@link List}<{@link DealerTaskDTO}>
     */
    List<DealerTaskDTO> getDealerYearAAKTask(String year);

    List<BusinessDataDO> selectByVins(List<String> vins, String type);

    List<ExecCalcDTO> getCalcPolicyOnAllDealer(String policyId);

    void deleteByPolicyId(@Param("policyId") String policyId);


    List<BusinessDataDO> selectInvoiceByVins(@Param("vins") List<String> vins);

    List<DealerAAKDTO> getDealerInvoice(@Param("year")String year, @Param("months")List<String> months, @Param("dim")String dim, @Param("dealerCodes") List<String> dealerCodes);
}
