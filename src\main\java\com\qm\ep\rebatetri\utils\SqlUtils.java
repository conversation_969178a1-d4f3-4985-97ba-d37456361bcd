package com.qm.ep.rebatetri.utils;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.parser.ParserException;
import com.alibaba.druid.sql.parser.SQLParserUtils;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import com.alibaba.druid.util.JdbcConstants;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.util.BootAppUtil;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SqlUtils {
    private SqlUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static String emphasis(String originStr) {
        return "`" + (BootAppUtil.isnotNullOrEmpty(originStr)?originStr:"") + "`";
    }

    /**
     * sql参数中特殊字符处理器
     * '：用于包裹搜索条件，需转为\'
     * %：用于代替任意数目的任意字符，需转换为\%
     * _：用于代替一个任意字符，需转换为\_
     * \：转义符号，需转换为\\在java中\也是特殊字符因此需要两次转义，而replace正则需要再次转义
     *
     * \要最先处理，防止把转义\再次转一遍
     * eg:abc _'%*sdf\\
     * result:abc\_\'\%\*sdf\\\\
     */
    public static String escape(String originStr){
        return  originStr.trim().replaceAll("\\s", "").replace("\\", "\\\\\\\\")
                .replace("_", "\\_").replace("'", "\\'")
                .replace("%", "\\%").replace("*", "\\*");
    }

    public static List<SQLStatement> parse(String sql) throws ParserException{
        SQLStatementParser parser = SQLParserUtils.createSQLStatementParser(sql, JdbcConstants.MYSQL);
        return parser.parseStatementList();
    }

    public static String format(String sql) throws ParserException{
        List<SQLStatement> statementList = parse(sql);
        return SQLUtils.toSQLString(statementList, JdbcConstants.MYSQL);
    }

    public static String aliasName(boolean useAliasName, String originalName) {
        return useAliasName ? DigestUtil.md5Hex(originalName): originalName;
    }

    public static void convertFilterSortBy(Map<String, String> webColumnWithDataColumnMap, JsonParamDto paramDto){
        String tSortBy = paramDto.getTsortby();
        if(BootAppUtil.isnotNullOrEmpty(tSortBy)){
            String[] tSortByArr = tSortBy.split(",");
            StringBuilder builder = new StringBuilder();
            for(int i = 0; i < tSortByArr.length; i++){
                String[] columnArr = tSortByArr[i].split("\\|");
                builder.append(webColumnWithDataColumnMap.get(columnArr[0])).append("|").append(columnArr[1]);
                if(i != tSortByArr.length - 1){
                    builder.append(",");
                }
            }
            paramDto.setTsortby(builder.toString());
        }
    }

    public static void convertFilterWhere(Map<String, String> webColumnWithDataColumnMap, JsonParamDto paramDto){
        String tWhere = paramDto.getTwhere();
        if(BootAppUtil.isnotNullOrEmpty(tWhere)){
            String[] tWhereArr = tWhere.split(" and ");
            StringBuilder builder = new StringBuilder();
            for(int i = 0; i < tWhereArr.length; i++){
                String[] columnArr = tWhereArr[i].split(" ");
                for(int j = 0; j < columnArr.length; j++){
                    if(j == 0){
                        builder.append(webColumnWithDataColumnMap.get(columnArr[j])).append(" ");
                    }else if(j == columnArr.length - 1){
                        builder.append(columnArr[j]);
                    }else{
                        builder.append(columnArr[j]).append(" ");
                    }
                }
                if(i != tWhereArr.length - 1){
                    builder.append(" and ");
                }
            }
            paramDto.setTwhere(builder.toString());
        }
    }

}
