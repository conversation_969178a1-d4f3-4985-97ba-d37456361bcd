package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.TrialUnrealLogDO;
import com.qm.ep.rebatetri.domain.dto.HalfCalcDTO;
import com.qm.tds.api.service.IQmBaseService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TrialUnrealLogService extends IQmBaseService<TrialUnrealLogDO> {

    void saveCommonLogNoBatchId(String dealerCode, String dim, String series, String remark, String policyId);
    void saveCommonLog(String dealerCode, String dim, String series, String batchId, String remark, String policyId);

    void saveHalfLog(String policyId, String dealerCode, String dim, String series, String batchId, HalfCalcDTO left, HalfCalcDTO right, Double midAmt, String remark);
}
