package com.qm.ep.rebatetri.domain.bean;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

@Schema(description = "数据数据TrialUnrealLogDO对象")
public class UnrealCarRebateSummaryDTO implements Serializable {


    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "车系")
    private String series;

    @Schema(description = "数量")
    private int nqty;

    @Schema(description = "左节点返利均值")
    private Double amt;

    @Schema(description = "返利均值")
    private Double namt;


}
