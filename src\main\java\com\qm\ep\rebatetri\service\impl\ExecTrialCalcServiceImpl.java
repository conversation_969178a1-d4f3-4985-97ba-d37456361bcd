package com.qm.ep.rebatetri.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.qm.ep.rebatetri.constant.Constants;
import com.qm.ep.rebatetri.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatetri.domain.bean.UnrealCalcHistoryDO;
import com.qm.ep.rebatetri.domain.dto.CalcObjectDTO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatetri.domain.dto.ExecCalcLogStructureDTO;
import com.qm.ep.rebatetri.domain.vo.SqlStructureVO;
import com.qm.ep.rebatetri.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatetri.enumerate.ExecTypeEnum;
import com.qm.ep.rebatetri.enumerate.StateEnum;
import com.qm.ep.rebatetri.mapper.ExecTrialCalcHistoryMapper;
import com.qm.ep.rebatetri.mapper.UnrealCalcHistoryMapper;
import com.qm.ep.rebatetri.mapper.UnrealCalcResultMapper;
import com.qm.ep.rebatetri.remote.RebateBaseRemote;
import com.qm.ep.rebatetri.service.*;
import com.qm.ep.rebatetri.utils.SqlUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.springframework.transaction.annotation.Isolation.READ_UNCOMMITTED;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecTrialCalcServiceImpl implements ExecTrialCalcService {

    @Resource
    private UnrealCalcResultMapper unrealCalcResultMapper;

    @Resource
    private UnrealCalcHistoryMapper unrealCalcHistoryMapper;
    private static final int MAX_RETRY_TIME = 50;

    @Resource
    private ExecCalcLogService execCalcLogService;

    @Resource
    RebateBaseRemote rebateBaseRemote;

    @Resource
    ExecTrialCalcHistoryService execTrialCalcHistoryService;

    @Resource
    UnrealCalcHistoryService unrealCalcHistoryService;

    @Resource
    ExecTrialCalcResultService execTrialCalcResultService;

    @Resource
    UnrealCalcResultService unrealCalcResultService;

    @Resource
    private ExecTrialCalcHistoryMapper execTrialCalcHistoryMapper;

    @Value("${rebate.exec-calc.result.max-amount:6000000}")
    private Long execCalcResultRecordMaxAmount;

    /**
     * 试算准备
     *
     * @param execCalcDTO 参数
     * @return 返回
     */
    @Override
    // todo：调a01服务去获取上地表查询aak+虚拟车
    // @Transactional(propagation= Propagation.REQUIRES_NEW)
    public String prepareCalc(ExecCalcDTO execCalcDTO) {
        /*if(BootAppUtil.isNullOrEmpty(execCalcDTO.getPolicyId()) || BootAppUtil.isNullOrEmpty(execCalcDTO.getObjectId()) || BootAppUtil.isNullOrEmpty(execCalcDTO.getObjectType())) {
            throw new QmException("政策ID或计算对象ID或计算对象类型为空！");
        }*/
        CalcObjectDTO calcObjectDTO = new CalcObjectDTO();
        calcObjectDTO.setPolicyId(execCalcDTO.getPolicyId());
        calcObjectDTO.setObjectId(execCalcDTO.getObjectId());
        calcObjectDTO.setObjectType(execCalcDTO.getObjectType());
        calcObjectDTO.setAbbreviated(true);
        calcObjectDTO.setSeriesQty(execCalcDTO.getSeriesQty());
        calcObjectDTO.setVr(execCalcDTO.getVr());
        calcObjectDTO.setDealerCode(execCalcDTO.getDealerCode());
        JsonResultVo<SqlStructureVO> sqlStructure = rebateBaseRemote.getRealSqlStructure(calcObjectDTO);

        ExecTrialCalcHistoryDO maxExecHistory = execTrialCalcHistoryMapper.selectByCondition(execCalcDTO.getPolicyId(), execCalcDTO.getObjectId(), execCalcDTO.getObjectType().getCode());

        ExecTrialCalcHistoryDO execCalcHistoryDO = new ExecTrialCalcHistoryDO();
        execCalcHistoryDO.setPolicyId(execCalcDTO.getPolicyId());
        execCalcHistoryDO.setObjectId(execCalcDTO.getObjectId());
        execCalcHistoryDO.setObjectType(execCalcDTO.getObjectType());
        execCalcHistoryDO.setExecType(null == execCalcDTO.getExecType() ? ExecTypeEnum.MANUAL : execCalcDTO.getExecType());
        execCalcHistoryDO.setCalcVersion(maxExecHistory == null ? 1 : maxExecHistory.getCalcVersion() + 1);
        execCalcHistoryDO.setState(StateEnum.PROCESS);
        execCalcHistoryDO.setBegin(DateUtils.getSysdateTime());
        execCalcHistoryDO.setSqlStructure(JSONUtils.beanToJson(sqlStructure.getData()));
        execCalcHistoryDO.setBatchId(execCalcDTO.getBatchId());

        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("创建试算任务").build());
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行SQL：" + sqlStructure.getData().getTemporaryTableSqlFormatted() + "\n\n" + sqlStructure.getData().getSqlFormatted()).build());

        execCalcHistoryDO.setLog(execCalcLogService.append(null, execCalcLogStructures));
        execCalcHistoryDO.setDtstamp(DateUtils.getSysdateTime());
        execTrialCalcHistoryService.save(execCalcHistoryDO);

        return execCalcHistoryDO.getId();
    }

    /**
     * 发起计算对象试算
     *
     * @param historyId 参数
     */
    @Override
    @DS(DataSourceType.W)
    @Transactional(rollbackFor = Exception.class, isolation = READ_UNCOMMITTED)
    public void execCalc(ExecCalcDTO execCalcDTO, String historyId) throws InterruptedException {
        ExecTrialCalcHistoryDO execCalcHistoryDO = execTrialCalcHistoryMapper.selectById(historyId);
        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        if (execCalcHistoryDO == null) {
            log.error("获取当前试算历史{}日志出错，本次试算退出", historyId);
            return;
        }

        execCalcHistoryDO.setExecutorThreadName(Thread.currentThread().getName());
        execTrialCalcHistoryMapper.updateById(execCalcHistoryDO);

        execCalcHistoryDO = execTrialCalcHistoryMapper.selectById(historyId);

        try {
            String sqlStructureBase = execCalcHistoryDO.getSqlStructure();
            SqlStructureVO sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);

            Map<String, Object> sqlMap = new HashMap<>(1);
            if (BootAppUtil.isnotNullOrEmpty(sqlStructure.getTemporaryTableSqlFormatted())) {
                sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
                execTrialCalcResultService.getBySql(sqlMap);
            }

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);
            List<Map> countResult = execTrialCalcResultService.getBySql(sqlMap);
            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT, "15");
            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : execCalcResultRecordMaxAmount;
            if (CollUtil.isNotEmpty(countResult) && total <= maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i - 1))) + ") as field" + i);
                }

                String sql = " insert ignore into exec_trial_calc_result(historyId," + StringUtils.join(fields, ",") + ") \n" +
                        " select '" + historyId + "' as historyId,sqlbase.* from " + sqlbase;

                sqlMap.put("sql", sql);

                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算SQL语句").build());
                int insertCount = execTrialCalcResultService.saveBySql(sqlMap);
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算成功，共生成" + insertCount + "条记录").build());

                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());
                List<Map> sumResult = execTrialCalcResultService.getBySql(sqlMap);
                if (CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    execCalcHistoryDO.setSumResult(rs);
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功！结果为：" + rs).build());
                } else {
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                execCalcHistoryDO.setState(StateEnum.FINISH);
                //   if(execCalcDTO.getVr() == 0) {
                // 插入返利试算器汇总表-实际返利
                //    groupResult(execCalcHistoryDO, execCalcDTO.getDim(), execCalcDTO.getVr(), execCalcDTO.getSeriesQty(), execCalcDTO.getSeries());

            } else {
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("试算结果记录数" + total + "。由于记录数大于500万，本次试算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                execCalcHistoryDO.setState(StateEnum.ERROR);
            }

            // 删除临时表
            Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
            if (null != temporaryTableSqlMap) {
                List<String> dropTemporaryTableSql = new ArrayList<>();
                temporaryTableSqlMap.values().forEach(s -> s.keySet().forEach(t -> dropTemporaryTableSql.add(" DROP TEMPORARY TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                if (CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                    sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                    execTrialCalcResultService.getBySql(sqlMap);
                }
            }
        } catch (Exception e) {
            execCalcHistoryDO.setState(StateEnum.ERROR);
            execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算出错\n" + e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {
            execCalcHistoryDO.setLog(execCalcLogService.append(execCalcHistoryDO.getLog(), execCalcLogStructures));
            execCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
            execTrialCalcHistoryMapper.updateById(execCalcHistoryDO);
        }
    }


    private void groupResult(UnrealCalcHistoryDO historyDO, String dim, int vr, Map<String, Integer> seriesQty, String series, String trailType) {
        String historyId = historyDO.getId();
        String sqlStructure = historyDO.getSqlStructure();
        JSONObject sqlStructureObject = JSON.parseObject(sqlStructure);
        JSONArray fields = sqlStructureObject.getJSONArray("fields");

        int seriesIndex = -1;
        int realEIndex = -1;
        int dealerIndex = -1;
        int vinIndex = -1;
        for (int i = 1; i <= fields.size(); i++) {
            if (fields.getString(i - 1).equals("系列")) {
                seriesIndex = i;
            }
            if (fields.getString(i - 1).equals("返利金额")) {
                realEIndex = i;
            }
            if (fields.getString(i - 1).equals("经销商代码")) {
                dealerIndex = i;
            }
            if (fields.getString(i - 1).equals("VIN码")) {
                vinIndex = i;
            }

        }
        if (seriesIndex == -1 || realEIndex == -1 || dealerIndex == -1 || vinIndex == -1) {
            log.info("未找到配置字段：系列/返利金额/经销商代码/VIN码");
            return;
        }
        String realEField = "field" + realEIndex;
        String seriesField = "field" + seriesIndex;
        String dealerField = "field" + dealerIndex;
        String vinField = "field" + vinIndex;
        String vrseries = null;
        int vrqty = 0;
        if (vr == 1 && seriesQty != null) {
            // 非全系取 虚拟车系和对应虚拟数量
            List<String> keys = new ArrayList<>(seriesQty.keySet());
            String firstKey = keys.get(0);
            vrseries = firstKey;
            vrqty = seriesQty.get(firstKey).intValue();
        }
        // 汇总计算结果总额
        // long summaryId = IdWorker.getId();
        execTrialCalcHistoryMapper.groupResult(realEField, seriesField, dealerField, historyId, historyDO.getPolicyId(), dim, vrseries, vrqty, historyDO.getBatchId(), series, vinField, trailType);
        // 汇总真实返利的车的明细
        String policyName = execTrialCalcHistoryMapper.selectPolicyNameById(historyDO.getPolicyId());
        execTrialCalcHistoryMapper.groupResultDetail(realEField, seriesField, dealerField, historyDO.getPolicyId(), policyName, dim, series, vinField, historyId);

    }


    @Override
    public String prepareCalcForUnreal(ExecCalcDTO execCalcDTO) {

        CalcObjectDTO calcObjectDTO = new CalcObjectDTO();
        calcObjectDTO.setPolicyId(execCalcDTO.getPolicyId());
        calcObjectDTO.setObjectId(execCalcDTO.getObjectId());
        calcObjectDTO.setObjectType(execCalcDTO.getObjectType());
        calcObjectDTO.setAbbreviated(true);
        calcObjectDTO.setSeriesQty(execCalcDTO.getSeriesQty());
        calcObjectDTO.setVr(execCalcDTO.getVr());
        calcObjectDTO.setDealerCode(execCalcDTO.getDealerCode());



        JsonResultVo<SqlStructureVO> sqlStructure = execCalcDTO.getVflag() == 1
                ? rebateBaseRemote.getMockSqlStructure(calcObjectDTO)
                : rebateBaseRemote.getRealSqlStructure(calcObjectDTO);
        // log.info("sqlStructure is {}", sqlStructure.getData());
        UnrealCalcHistoryDO unrealCalcHistoryDO = new UnrealCalcHistoryDO();
        unrealCalcHistoryDO.setPolicyId(execCalcDTO.getPolicyId());
        unrealCalcHistoryDO.setObjectId(execCalcDTO.getObjectId());
        unrealCalcHistoryDO.setObjectType(execCalcDTO.getObjectType());
        unrealCalcHistoryDO.setExecType(ExecTypeEnum.REAL_REBATE);
        unrealCalcHistoryDO.setState(StateEnum.PROCESS);
        unrealCalcHistoryDO.setBegin(DateUtils.getSysdateTime());
        unrealCalcHistoryDO.setSqlStructure(JSONUtils.beanToJson(sqlStructure.getData()));
        unrealCalcHistoryDO.setBatchId(execCalcDTO.getBatchId());

        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("创建试算任务").build());
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行SQL：" + sqlStructure.getData().getTemporaryTableSqlFormatted() + "\n\n" + sqlStructure.getData().getSqlFormatted()).build());

        unrealCalcHistoryDO.setLog(execCalcLogService.append(null, execCalcLogStructures));
        unrealCalcHistoryDO.setDtstamp(DateUtils.getSysdateTime());
        unrealCalcHistoryService.save(unrealCalcHistoryDO);

        return unrealCalcHistoryDO.getId();
    }

    @Override
    @DS(DataSourceType.W)
    // @Transactional(rollbackFor = Exception.class)
    public void execCalcForUnreal(ExecCalcDTO execCalcDTO, String historyId) {
        UnrealCalcHistoryDO unrealCalcHistoryDO = unrealCalcHistoryService.getById(historyId);
        List<ExecCalcLogStructureDTO> unrealCalcLogStructures = new ArrayList<>();
        if (unrealCalcHistoryDO == null) {
            log.error("获取当前试算历史{}日志出错，本次试算退出", historyId);
            return;
        }

        unrealCalcHistoryDO.setExecutorThreadName(Thread.currentThread().getName());
        unrealCalcHistoryService.updateById(unrealCalcHistoryDO);

        unrealCalcHistoryDO = unrealCalcHistoryService.getById(historyId);

        try {
            String sqlStructureBase = unrealCalcHistoryDO.getSqlStructure();
            SqlStructureVO sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);

            Map<String, Object> sqlMap = new HashMap<>(1);
            if (BootAppUtil.isnotNullOrEmpty(sqlStructure.getTemporaryTableSqlFormatted())) {
                sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
                // 创建临时表
                unrealCalcResultService.getBySql(sqlMap);
            }

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);
            List<Map> countResult = unrealCalcResultService.getBySql(sqlMap);
            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT, "15");
            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : execCalcResultRecordMaxAmount;
            if (CollUtil.isNotEmpty(countResult) && total <= maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i - 1))) + ") as field" + i);
                }

                String sql = " insert ignore into unreal_calc_result(historyId," + StringUtils.join(fields, ",") + ") \n" +
                        " select '" + historyId + "' as historyId,sqlbase.* from " + sqlbase;

                sqlMap.put("sql", sql);

                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算SQL语句").build());
                int insertCount = unrealCalcResultService.saveBySql(sqlMap);
                log.info("------------------插入unrealCalcResult的条数--------------------------" + insertCount);
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算成功，共生成" + insertCount + "条记录,这里只针对一家经销商取一台车").build());

                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());
                List<Map> sumResult = unrealCalcResultService.getBySql(sqlMap);
                if (CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    unrealCalcHistoryDO.setSumResult(rs);
                    unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功！结果为：" + rs).build());
                } else {
                    unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                unrealCalcHistoryDO.setState(StateEnum.FINISH);
                // 核心方法：将每台车的返利结果汇总到summary和明细
                this.groupResult(unrealCalcHistoryDO, execCalcDTO.getDim(), execCalcDTO.getVr(), execCalcDTO.getSeriesQty(), execCalcDTO.getSeries(), execCalcDTO.getTrailType());

            } else {
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("试算结果记录数" + total + "。由于记录数大于500万，本次试算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                unrealCalcHistoryDO.setState(StateEnum.ERROR);
            }

            // 删除临时表
            Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
            if (null != temporaryTableSqlMap) {
                List<String> dropTemporaryTableSql = new ArrayList<>();
                temporaryTableSqlMap.values().forEach(s -> s.keySet().forEach(t -> dropTemporaryTableSql.add(" DROP TEMPORARY TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                if (CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                    sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                    execTrialCalcResultService.getBySql(sqlMap);
                }
            }
        } catch (Exception e) {
            unrealCalcHistoryDO.setState(StateEnum.ERROR);
            unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算出错\n" + e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {
            unrealCalcHistoryDO.setLog(execCalcLogService.append(unrealCalcHistoryDO.getLog(), unrealCalcLogStructures));
            unrealCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
            unrealCalcHistoryService.updateById(unrealCalcHistoryDO);
        }
    }

    @Override
    public void execCalcForUnrealV2(ExecCalcDTO execCalcDTO, String historyId) {

        UnrealCalcHistoryDO unrealCalcHistoryDO = unrealCalcHistoryService.getById(historyId);

        List<ExecCalcLogStructureDTO> unrealCalcLogStructures = new ArrayList<>();
        if (unrealCalcHistoryDO == null) {
            throw new QmException("获取当前试算历史出错，本次试算退出");
        }
        SqlStructureVO sqlStructure = null;
        Map<String, Object> sqlMap = new HashMap<>();
        try {
            String sqlStructureBase = unrealCalcHistoryDO.getSqlStructure();
            sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);

            sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
            // 创建临时表
            unrealCalcResultService.getBySql(sqlMap);

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);
            List<Map> countResult = unrealCalcResultService.getBySql(sqlMap);
            if (CollUtil.isNotEmpty(countResult)) {
                throw new QmException(historyId + "计算结果为0");
            }
            // 获取试算结果的总数
            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT, "15");

            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : execCalcResultRecordMaxAmount;
            if (total <= maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i - 1))) + ") as field" + i);
                }

                String sql = " insert ignore into unreal_calc_result(historyId," + StringUtils.join(fields, ",") + ") \n" +
                        " select '" + historyId + "' as historyId,sqlbase.* from " + sqlbase;
                // 插入试算结果
                sqlMap.put("sql", sql);

                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算SQL语句").build());
                int insertCount = unrealCalcResultService.saveBySql(sqlMap);
                log.info("------------------插入unrealCalcResult的条数--------------------------" + insertCount);
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算成功，共生成" + insertCount + "条记录").build());

                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());
                List<Map> sumResult = unrealCalcResultService.getBySql(sqlMap);
                if (CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    unrealCalcHistoryDO.setSumResult(rs);
                    unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功！结果为：" + rs).build());
                } else {
                    unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                unrealCalcHistoryDO.setState(StateEnum.FINISH);
                // 将每台车的返利结果汇总到summary
                // if (execCalcDTO.getVflag() == 1) {
                //     groupResult(unrealCalcHistoryDO, execCalcDTO.getDim(), execCalcDTO.getVr(), execCalcDTO.getSeriesQty(), execCalcDTO.getSeries());
                // }
            } else {
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("试算结果记录数" + total + "。由于记录数大于" + maxAmount + "万，本次试算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                unrealCalcHistoryDO.setState(StateEnum.ERROR);
            }


        } catch (Exception e) {
            unrealCalcHistoryDO.setState(StateEnum.ERROR);
            unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算出错\n" + e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {
            unrealCalcHistoryDO.setExecutorThreadName(Thread.currentThread().getName());
            unrealCalcHistoryDO.setLog(execCalcLogService.append(unrealCalcHistoryDO.getLog(), unrealCalcLogStructures));
            unrealCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
            // 不更新SqlStructure
            unrealCalcHistoryDO.setSqlStructure(null);
            unrealCalcHistoryService.updateById(unrealCalcHistoryDO);

            if (sqlStructure != null) {
                // 删除临时表
                log.info("historyId{},删除临时表：execTrialCalcResultService.getBySql开始", historyId);
                Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
                if (null != temporaryTableSqlMap) {
                    List<String> dropTemporaryTableSql = new ArrayList<>();
                    temporaryTableSqlMap.values().forEach(s -> s.keySet().forEach(t -> dropTemporaryTableSql.add(" DROP TEMPORARY TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                    if (CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                        sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                        try {
                            execTrialCalcResultService.getBySql(sqlMap);
                        } catch (Exception e) {
                            log.error("Failed to delete temporary table", e);
                        }
                    }
                }
                log.info("historyId{},删除临时表：execTrialCalcResultService.getBySql结束", historyId);
            }
        }
    }

    @Override
    @DS(DataSourceType.W)
    // @Transactional(rollbackFor = Exception.class)
    public void execCalcForUnrealV3(ExecCalcDTO execCalcDTO, String historyId) throws InterruptedException {


        log.info("正式计算执行：execCalcForUnrealV3(String historyId={}) 开始", historyId);
        int retryCount = 0;
        UnrealCalcHistoryDO unrealCalcHistoryDO = null;
        while (retryCount < MAX_RETRY_TIME) {
            unrealCalcHistoryDO = unrealCalcHistoryService.getById(historyId);
            if (unrealCalcHistoryDO != null) {
                break;
            }
            retryCount++;
            TimeUnit.SECONDS.sleep(3);
        }

        List<ExecCalcLogStructureDTO> unrealCalcLogStructures = new ArrayList<>();

        if (unrealCalcHistoryDO == null) {
            log.error("获取当前计算历史{}日志出错，本次计算退出", historyId);
            return;
        } else {
            log.info("获取当前计算历史{}日志成功！尝试次数：{}", historyId, retryCount);
        }

        unrealCalcHistoryDO.setExecutorThreadName(Thread.currentThread().getName());


        try {
            // log.info("开始更新线程名称：historyId={}" , unrealCalcHistoryDO.getId());
            // unrealCalcHistoryService.updateById(unrealCalcHistoryDO);

            String sqlStructureBase = unrealCalcHistoryDO.getSqlStructure();
            SqlStructureVO sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);

            Map<String, Object> sqlMap = new HashMap<>(1);
            if (BootAppUtil.isnotNullOrEmpty(sqlStructure.getTemporaryTableSqlFormatted())) {
                sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
                // 创建临时表
                unrealCalcResultService.getBySql(sqlMap);
            }

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);
            List<Map> countResult = unrealCalcResultService.getBySql(sqlMap);
            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT, "15");
            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : execCalcResultRecordMaxAmount;
            if (CollUtil.isNotEmpty(countResult) && total <= maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i - 1))) + ") as field" + i);
                }


                // // 改造动态SQL：插入试算结果只插入经销商车系下一台车的返利，
                // // 因为对于经销商车系的维度，每台车返利一致，不处理可能很快就导致result达到亿万级别数据
                // String unrealSQLBase = this.filterOneVinRebateSQL(sqlbase);
                String[] split = execCalcDTO.getBatchId().split("-");
                int qty = Integer.parseInt(split[1]);

                String sql = " insert ignore into unreal_calc_result(historyId,field50," + StringUtils.join(fields, ",") + ") \n" +
                        " select '" + historyId + "' as historyId,'" + qty + "' as field50,sqlbase.* from " + sqlbase;
                // String sql = " insert ignore into unreal_calc_result(historyId,field50" + StringUtils.join(fields, ",") + ") \n" +
                //         " select '" + historyId +qty+ "' as historyId,sqlbase.* from " + unrealSQLBase;

                sqlMap.put("sql", sql);

                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算SQL语句").build());
                int insertCount = unrealCalcResultService.saveBySql(sqlMap);
                log.info("------------------插入unrealCalcResult的条数--------------------------" + insertCount);
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算成功，共生成" + insertCount + "条记录").build());
                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());
                List<Map> sumResult = unrealCalcResultService.getBySql(sqlMap);
                if (CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    unrealCalcHistoryDO.setSumResult(rs);
                    unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功,但因为动态SQL进行了改造，只针对经销商车系维度保留一台车，所以金额对不上！改造前的结果为：" + rs).build());
                } else {
                    unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                unrealCalcHistoryDO.setState(StateEnum.FINISH);
                // 核心步骤：汇总每台车的返利结果到summary
                this.summaryUnrealRebateAmount(unrealCalcHistoryDO, execCalcDTO.getBatchId());
            } else {
                unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("试算结果记录数" + total + "。由于记录数大于500万，本次试算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                unrealCalcHistoryDO.setState(StateEnum.ERROR);
            }

            // 删除临时表
            Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
            if (null != temporaryTableSqlMap) {
                List<String> dropTemporaryTableSql = new ArrayList<>();
                temporaryTableSqlMap.values().forEach(s -> s.keySet().forEach(t -> dropTemporaryTableSql.add(" DROP TEMPORARY TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                if (CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                    sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                    execTrialCalcResultService.getBySql(sqlMap);
                }
            }
        } catch (Exception e) {
            unrealCalcHistoryDO.setState(StateEnum.ERROR);
            unrealCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算出错\n" + e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {
            unrealCalcHistoryDO.setLog(execCalcLogService.append(unrealCalcHistoryDO.getLog(), unrealCalcLogStructures));
            unrealCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
            unrealCalcHistoryDO.setSqlStructure(null);
            unrealCalcHistoryMapper.updateById(unrealCalcHistoryDO);
        }
    }

    private String filterOneVinRebateSQL(String sqlbase) {

        // 提取 group by 前的数据
        String selectPart = sqlbase.substring(0, sqlbase.indexOf("group by"));
        String dealerCode = "`" + DigestUtil.md5Hex("经销商代码") + "`";
        String series = "`" + DigestUtil.md5Hex("系列") + "`";

        return selectPart + " GROUP BY " + dealerCode + "," + series;
    }

    private void summaryUnrealRebateAmount(UnrealCalcHistoryDO historyDO, String batchId) {
        //    batchId : policyId+"-"+qty
        String[] split = batchId.split("-");
        int qty = Integer.parseInt(split[1]);

        String historyId = historyDO.getId();
        String sqlStructure = historyDO.getSqlStructure();
        JSONObject sqlStructureObject = JSON.parseObject(sqlStructure);
        JSONArray fields = sqlStructureObject.getJSONArray("fields");

        int seriesIndex = -1;
        int rebateIndex = -1;
        int dealerIndex = -1;
        for (int i = 1; i <= fields.size(); i++) {
            if (fields.getString(i - 1).equals("经销商代码")) {
                dealerIndex = i;
            }
            if (fields.getString(i - 1).equals("系列")) {
                seriesIndex = i;
            }
            if (fields.getString(i - 1).equals("返利金额")) {
                rebateIndex = i;
            }

        }

        String rebateField = "field" + rebateIndex;
        String seriesField = "field" + seriesIndex;
        String dealerField = "field" + dealerIndex;

        // unrealCalcResultMapper.summaryRebateResult(dealerField, seriesField, rebateField, historyDO.getPolicyId(), historyId, qty, batchId);
        unrealCalcResultMapper.summaryUnrealRebateAmount(dealerField, seriesField, rebateField, historyDO.getPolicyId(), historyId, qty, batchId);
    }

}
