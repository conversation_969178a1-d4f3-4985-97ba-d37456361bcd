package com.qm.ep.rebatetri.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("COMBINATIONMAIN")
@Schema(description = "计算方案合并主表对象")
public class CombinationMainDO implements Serializable {



    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "政策主键")
    @TableField("POLICYID")
    private String policyId;

    @Schema(description = "名称")
    @TableField("COMBINATIONNAME")
    private String combinationName;

    @Schema(description = "描述")
    @TableField("combinationdesc")
    private String combinationDesc;

    @Schema(description = "计算方案数据源")
    @TableField("CALCOBJECTS")
    private String calcObjects;

    @Schema(description = "创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "创建人名称")
    @TableField(exist = false)
    private String createByname;

    @Schema(description = "更新人名称")
    @TableField(exist = false)
    private String updateByname;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
