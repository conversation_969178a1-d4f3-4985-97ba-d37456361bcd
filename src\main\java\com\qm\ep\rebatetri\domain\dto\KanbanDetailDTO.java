package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 看板详细数据
 *
 * <AUTHOR>
 * @date 2024/12/12
 */
@Data
@Schema(description = "数据对象")
public class KanbanDetailDTO implements Serializable {

    
    

    @Schema(description = "策略 ID")
    private String policyId;
    @Schema(description = "底盘号VIN")
    private String vin;
    /**
     * 返利金额
     */
    @Schema(description = "返利金额")
    private String amt;
    /**
     * 原来返利金额
     */
    @Schema(description = "原来返利金额")
    private String originalAmt;
    /**
     * 返利金额差额
     */
    @Schema(description = "返利金额差额")
    private String diffAmt;
    @Schema(description = "系列名称")
    private String seriesName;
}
