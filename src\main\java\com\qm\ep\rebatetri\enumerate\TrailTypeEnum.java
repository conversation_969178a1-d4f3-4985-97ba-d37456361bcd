package com.qm.ep.rebatetri.enumerate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Schema(description = "试算器试算类型枚举")
public enum TrailTypeEnum {


    /**
     * aak
     */
    @Schema(description = "数据aak")
    AAK("aak", "经销商提车统计"),


    /**
     * std
     */
    @Schema(description = "数据std")
    STD("std", "经销商提车统计"),


    /**
     * 发票
     */
    @Schema(description = "发票")
    INVOICE("invoice", "终端发票查询");


    /**
     * 编码
     */
    @Schema(description = "编码")
    private final String code;
    /**
     * 数据因子名
     */
    @Schema(description = "数据因子名")
    private final String desc;

    TrailTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
//    根据code获取desc

    public static String getDescByCode(String code) {
        for (TrailTypeEnum trailTypeEnum : TrailTypeEnum.values()) {
            if (trailTypeEnum.getCode().equals(code)) {
                return trailTypeEnum.getDesc();
            }
        }
        return null;
    }
}
