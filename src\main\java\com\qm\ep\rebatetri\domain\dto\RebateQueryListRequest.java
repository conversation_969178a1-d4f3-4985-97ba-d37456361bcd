package com.qm.ep.rebatetri.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * dms返利看板查询请求
 *
 * <AUTHOR>
 * @date 2023/10/12
 */
@Schema(description = "数据:数据dms返利看板查询请求")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RebateQueryListRequest {
    /**
     * 经销商代码
     */
    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "政策编码")
    private String policyCode;

    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 政策结束时间-开始时间
     */
    @Schema(description = "政策结束时间-开始时间")
    private String startTime;

    /**
     * 政策结束时间-结束时间
     */
    @Schema(description = "政策结束时间-结束时间")
    private String endTime;

    /**
     * 车型
     */
    @Schema(description = "车型")
    private List<String> series;


    @Schema(description = "试算器-试算类型:aak-AAK,std-STD，invoice-销售发票")
    private String reimburseStatus;


}
