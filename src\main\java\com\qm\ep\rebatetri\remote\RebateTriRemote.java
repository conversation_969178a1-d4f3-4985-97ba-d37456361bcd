package com.qm.ep.rebatetri.remote;

import com.qm.ep.rebatetri.config.AsyncFeignConfig;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(contextId= "tds-service-rebate-tri",name = "tds-service-rebate-tri", configuration = AsyncFeignConfig.class)
public interface RebateTriRemote {

    @GetMapping(value = "/dealerCalc/trialV2", produces = "application/json")
    JsonResultVo<Object> trialCalcV2();

}
