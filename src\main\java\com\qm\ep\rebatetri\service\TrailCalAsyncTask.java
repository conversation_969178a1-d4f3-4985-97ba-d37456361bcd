package com.qm.ep.rebatetri.service;

import com.qm.ep.rebatetri.domain.bean.BusinessDataDO;
import com.qm.ep.rebatetri.domain.dto.PolicyPublishConfigDTO;

import java.util.List;
import java.util.Map;

public interface TrailCalAsyncTask {

    /**
     * 插入虚拟车
     *
     * @param policyPublishConfigList 策略发布配置列表
     * @param finalMapMax             最终地图最大值
     * @param policyId                策略id
     * @param table                   桌子
     */
    void doInsertCarIntoUnrealBusinessdata(List<PolicyPublishConfigDTO> policyPublishConfigList, int finalMapMax, String policyId, Map<String, List<BusinessDataDO>> table);

    /**
     * 插入虚拟发票
     *
     * @param policyPublishConfigList 策略发布配置列表
     * @param finalMapMax             最终地图最大值
     * @param policyId                策略id
     * @param table                   桌子
     */
    void doInsertInvoiceIntoUnrealBusinessdata(List<PolicyPublishConfigDTO> policyPublishConfigList, int finalMapMax, String policyId, Map<String, List<BusinessDataDO>> table);
}
